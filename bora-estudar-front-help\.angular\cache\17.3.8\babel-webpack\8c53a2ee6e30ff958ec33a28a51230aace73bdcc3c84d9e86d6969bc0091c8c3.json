{"ast": null, "code": "import { inject, EventEmitter } from '@angular/core';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatIconButton } from '@angular/material/button';\nimport { MatMenu, MatMenuTrigger, MatMenuItem } from '@angular/material/menu';\nimport { AuthService } from '../../../core/security/auth/auth.service';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport let UserProfileComponent = /*#__PURE__*/(() => {\n  class UserProfileComponent {\n    constructor() {\n      this.logoutClicked = new EventEmitter();\n      this.user = null;\n      this.authService = inject(AuthService);\n    }\n    ngOnInit() {\n      this.loadUser();\n    }\n    loadUser() {\n      this.authService.getUser().subscribe({\n        next: user => {\n          this.user = user;\n        },\n        error: error => {\n          console.error('Erro ao carregar dados do usuário:', error);\n        }\n      });\n    }\n    onLogout() {\n      this.logoutClicked.emit();\n    }\n    getUserInitials() {\n      if (!this.user?.name) return '';\n      const names = this.user.name.toString().split(' ');\n      if (names.length === 1) {\n        return names[0].charAt(0).toUpperCase();\n      }\n      return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();\n    }\n    static #_ = this.ɵfac = function UserProfileComponent_Factory(t) {\n      return new (t || UserProfileComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserProfileComponent,\n      selectors: [[\"app-user-profile\"]],\n      outputs: {\n        logoutClicked: \"logoutClicked\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 20,\n      vars: 3,\n      consts: [[\"userMenu\", \"matMenu\"], [1, \"user-profile\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Menu do usu\\u00E1rio\", 1, \"user-profile-button\", 3, \"matMenuTriggerFor\"], [1, \"user-menu\"], [\"mat-menu-item\", \"\", \"disabled\", \"\", 1, \"user-info\"], [1, \"user-avatar\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-email\"], [3, \"click\"]],\n      template: function UserProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2)(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"account_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"mat-menu\", 3, 0)(6, \"div\", 4)(7, \"div\", 5)(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"account_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 8);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"mat-menu-item\", 9);\n          i0.ɵɵlistener(\"click\", function UserProfileComponent_Template_mat_menu_item_click_15_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onLogout());\n          });\n          i0.ɵɵelementStart(16, \"mat-icon\");\n          i0.ɵɵtext(17, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19, \"Sair\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const userMenu_r2 = i0.ɵɵreference(5);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r2);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate((ctx.user == null ? null : ctx.user.name) || \"Usu\\u00E1rio\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.user == null ? null : ctx.user.email) || \"\");\n        }\n      },\n      dependencies: [CommonModule, MatIcon, MatIconButton, MatMenu, MatMenuTrigger, MatMenuItem],\n      styles: [\".user-profile[_ngcontent-%COMP%]{display:flex;align-items:center}.user-profile-button[_ngcontent-%COMP%]{color:#fff}.user-profile-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;width:28px;height:28px}.user-menu[_ngcontent-%COMP%]{min-width:250px}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px!important;border-bottom:1px solid #e0e0e0;margin-bottom:8px;cursor:default!important}.user-info[_ngcontent-%COMP%]:hover{background-color:transparent!important}.user-avatar[_ngcontent-%COMP%]{margin-right:12px}.user-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:40px;width:40px;height:40px;color:#666}.user-details[_ngcontent-%COMP%]{flex:1}.user-name[_ngcontent-%COMP%]{font-weight:500;font-size:16px;color:#333;margin-bottom:4px}.user-email[_ngcontent-%COMP%]{font-size:14px;color:#666}mat-menu-item[_ngcontent-%COMP%]{display:flex;align-items:center}mat-menu-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:12px;color:#666}\"]\n    });\n  }\n  return UserProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}