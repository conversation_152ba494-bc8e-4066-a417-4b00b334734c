{"ast": null, "code": "import { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { catchError, tap } from 'rxjs';\nimport { StorageService } from './storage.service';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nconst AUTH_API = environment.authApi;\nconst httpOptions = {\n  headers: new HttpHeaders({\n    'Content-Type': 'application/json'\n  })\n};\nexport class AuthService {\n  constructor() {\n    this.http = inject(HttpClient);\n    this.storageService = inject(StorageService);\n  }\n  login(body) {\n    return this.http.post(AUTH_API.concat('/signin'), body, httpOptions).pipe(tap(user => {\n      this.storageService.saveUser(user);\n    }), catchError(error => {\n      console.log(`Error on login: ${error.message}`);\n      this.storageService.clear();\n      throw error;\n    }));\n  }\n  register(body) {\n    return this.http.post(AUTH_API.concat('/signup'), body, httpOptions).pipe(catchError(error => {\n      console.log(`Error on login: ${error.message}`);\n      this.storageService.clear();\n      throw error;\n    }));\n  }\n  confirmEmail(token) {\n    return this.http.get(AUTH_API.concat(`/confirm?token=${token}`), httpOptions);\n  }\n  logout() {\n    return this.http.post(AUTH_API + 'signout', {}, httpOptions).pipe(tap(() => {\n      this.storageService.clear();\n    }), catchError(error => {\n      console.log(`Error on login: ${error.message}`);\n      this.storageService.clear();\n      throw error;\n    }));\n  }\n  getUser() {\n    return this.storageService.getUser();\n  }\n  isLoggedIn() {\n    return this.storageService.isLoggedIn();\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpClient", "HttpHeaders", "inject", "catchError", "tap", "StorageService", "environment", "AUTH_API", "authApi", "httpOptions", "headers", "AuthService", "constructor", "http", "storageService", "login", "body", "post", "concat", "pipe", "user", "saveUser", "error", "console", "log", "message", "clear", "register", "confirmEmail", "token", "get", "logout", "getUser", "isLoggedIn", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\core\\security\\auth\\auth.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable, inject } from '@angular/core';\r\nimport { Observable, catchError, tap } from 'rxjs';\r\nimport { SigninBody } from 'src/app/auth/models/signin-body';\r\nimport { SignupBody } from 'src/app/auth/models/signup-body';\r\nimport { UserResponseBasicDto } from 'src/app/shared/models/user/user-response-basic-dto';\r\nimport { StorageService } from './storage.service';\r\nimport { environment } from '../../../../environments/environment';\r\n\r\nconst AUTH_API = environment.authApi;\r\n\r\nconst httpOptions = {\r\n  headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n};\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  private readonly http = inject(HttpClient);\r\n  private readonly storageService = inject(StorageService);\r\n\r\n  login(body: SigninBody): Observable<UserResponseBasicDto> {\r\n    return this.http\r\n      .post<UserResponseBasicDto>(AUTH_API.concat('/signin'), body, httpOptions)\r\n      .pipe(\r\n        tap((user) => {\r\n          this.storageService.saveUser(user);\r\n        }),\r\n        catchError((error) => {\r\n          console.log(`Error on login: ${error.message}`);\r\n          this.storageService.clear();\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  register(body: SignupBody): Observable<UserResponseBasicDto> {\r\n    return this.http\r\n      .post<UserResponseBasicDto>(AUTH_API.concat('/signup'), body, httpOptions)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.log(`Error on login: ${error.message}`);\r\n          this.storageService.clear();\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  confirmEmail(token: string): Observable<any> {\r\n    return this.http.get(\r\n      AUTH_API.concat(`/confirm?token=${token}`),\r\n      httpOptions\r\n    );\r\n  }\r\n\r\n  logout(): Observable<any> {\r\n    return this.http.post(AUTH_API + 'signout', {}, httpOptions).pipe(\r\n      tap(() => {\r\n        this.storageService.clear();\r\n      }),\r\n      catchError((error) => {\r\n        console.log(`Error on login: ${error.message}`);\r\n        this.storageService.clear();\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  getUser(): Observable<UserResponseBasicDto> {\r\n    return this.storageService.getUser();\r\n  }\r\n\r\n  isLoggedIn(): Observable<boolean> {\r\n    return this.storageService.isLoggedIn();\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,MAAM,QAAQ,eAAe;AAClD,SAAqBC,UAAU,EAAEC,GAAG,QAAQ,MAAM;AAIlD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,sCAAsC;;AAElE,MAAMC,QAAQ,GAAGD,WAAW,CAACE,OAAO;AAEpC,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,IAAIT,WAAW,CAAC;IAAE,cAAc,EAAE;EAAkB,CAAE;CAChE;AAKD,OAAM,MAAOU,WAAW;EAHxBC,YAAA;IAImB,KAAAC,IAAI,GAAGX,MAAM,CAACF,UAAU,CAAC;IACzB,KAAAc,cAAc,GAAGZ,MAAM,CAACG,cAAc,CAAC;;EAExDU,KAAKA,CAACC,IAAgB;IACpB,OAAO,IAAI,CAACH,IAAI,CACbI,IAAI,CAAuBV,QAAQ,CAACW,MAAM,CAAC,SAAS,CAAC,EAAEF,IAAI,EAAEP,WAAW,CAAC,CACzEU,IAAI,CACHf,GAAG,CAAEgB,IAAI,IAAI;MACX,IAAI,CAACN,cAAc,CAACO,QAAQ,CAACD,IAAI,CAAC;IACpC,CAAC,CAAC,EACFjB,UAAU,CAAEmB,KAAK,IAAI;MACnBC,OAAO,CAACC,GAAG,CAAC,mBAAmBF,KAAK,CAACG,OAAO,EAAE,CAAC;MAC/C,IAAI,CAACX,cAAc,CAACY,KAAK,EAAE;MAC3B,MAAMJ,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAK,QAAQA,CAACX,IAAgB;IACvB,OAAO,IAAI,CAACH,IAAI,CACbI,IAAI,CAAuBV,QAAQ,CAACW,MAAM,CAAC,SAAS,CAAC,EAAEF,IAAI,EAAEP,WAAW,CAAC,CACzEU,IAAI,CACHhB,UAAU,CAAEmB,KAAK,IAAI;MACnBC,OAAO,CAACC,GAAG,CAAC,mBAAmBF,KAAK,CAACG,OAAO,EAAE,CAAC;MAC/C,IAAI,CAACX,cAAc,CAACY,KAAK,EAAE;MAC3B,MAAMJ,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAM,YAAYA,CAACC,KAAa;IACxB,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAClBvB,QAAQ,CAACW,MAAM,CAAC,kBAAkBW,KAAK,EAAE,CAAC,EAC1CpB,WAAW,CACZ;EACH;EAEAsB,MAAMA,CAAA;IACJ,OAAO,IAAI,CAAClB,IAAI,CAACI,IAAI,CAACV,QAAQ,GAAG,SAAS,EAAE,EAAE,EAAEE,WAAW,CAAC,CAACU,IAAI,CAC/Df,GAAG,CAAC,MAAK;MACP,IAAI,CAACU,cAAc,CAACY,KAAK,EAAE;IAC7B,CAAC,CAAC,EACFvB,UAAU,CAAEmB,KAAK,IAAI;MACnBC,OAAO,CAACC,GAAG,CAAC,mBAAmBF,KAAK,CAACG,OAAO,EAAE,CAAC;MAC/C,IAAI,CAACX,cAAc,CAACY,KAAK,EAAE;MAC3B,MAAMJ,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAU,OAAOA,CAAA;IACL,OAAO,IAAI,CAAClB,cAAc,CAACkB,OAAO,EAAE;EACtC;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACnB,cAAc,CAACmB,UAAU,EAAE;EACzC;EAAC,QAAAC,CAAA,G;qBAzDUvB,WAAW;EAAA;EAAA,QAAAwB,EAAA,G;WAAXxB,WAAW;IAAAyB,OAAA,EAAXzB,WAAW,CAAA0B,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}