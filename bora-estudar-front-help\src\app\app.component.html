<body>
  <div class="app_container">
    <mat-toolbar color="primary" position="start" class="header_toolbar">

      @if (showBackIcon && isLoggedIn === true && this.router.url !== '/associate' ) {
        <button mat-icon-button (click)="navigateToSearch()">
          <mat-icon>arrow_back</mat-icon>
        </button>
      }

      <span class="spacer"></span>

      <h1 class="app_name">{{appName}}</h1>

      <span class="spacer"></span>

      @if (isLoggedIn === true) {
        <!-- Área do usuário logado com ícone e nome -->
        <div class="user-info-container">
          <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-account-button">
            <mat-icon>account_circle</mat-icon>
          </button>
          <span class="user-name-toolbar">{{ user?.name || 'Usuário' }}</span>
        </div>

        <!-- Menu dropdown para logout -->
        <mat-menu #userMenu="matMenu">
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Sair</span>
          </button>
        </mat-menu>

        <button mat-icon-button (click)="snav.toggle()">
          <mat-icon>menu</mat-icon>
        </button>
      }
    </mat-toolbar>

    <mat-sidenav-container class="sidenav_container">
      <mat-sidenav #snav mode="over" class="mat_sidenav_content">
        @if (isLoggedIn === true) {
          <mat-nav-list>
            @if(this.router.url !== '/associate'){
              <a mat-list-item routerLink="/search" (click)="close()">Home</a>
              <a mat-list-item routerLink="/create" (click)="close()">Criar Grupos</a>
              <a mat-list-item routerLink="/my-study-group" (click)="close()">Meus Grupos</a>
            }
            <a mat-list-item (click)="logout()">Sair</a>
          </mat-nav-list>
        } @else {
          <mat-nav-list>
            <a mat-list-item routerLink="/login">Login</a>
            <a mat-list-item routerLink="/register">Register</a>
          </mat-nav-list>
        }
      </mat-sidenav>

      <mat-sidenav-content>
        <router-outlet><main></main></router-outlet>
      </mat-sidenav-content>
    </mat-sidenav-container>
  </div>
</body>
