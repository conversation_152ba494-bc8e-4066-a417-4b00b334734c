{"ast": null, "code": "import { Router } from '@angular/router';\nimport { inject } from '@angular/core';\nimport { AuthService } from '../auth/auth.service';\nimport { map } from 'rxjs/operators';\n/**\n * A guard that prevents access to a route if the user is already logged in.\n * If the user is logged in, it redirects to the '/search' route.\n * If the user is not logged in, it allows access to the route.\n * @param _route - The activated route snapshot.\n * @param _state - The router state snapshot.\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\n */\nexport const loggedInGuard = (_route, _state) => {\n  console.log('The loggedInGuard is being called correctly');\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  let isLoggedIn = false;\n  authService.isLoggedIn().subscribe(b => {\n    isLoggedIn = b;\n  });\n  if (isLoggedIn) {\n    router.navigateByUrl('/search');\n    return false;\n  }\n  return true;\n};\n/**\n * A guard that checks if the user is authenticated before allowing access to a route.\n * If the user is not authenticated, it redirects to the login page.\n *\n * @param _route - The activated route snapshot.\n * @param _state - The router state snapshot.\n * @returns A boolean indicating whether the user is authenticated or not, or a UrlTree to redirect to the login page.\n */\nexport const authGuard = (_route, _state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  return authService.isLoggedIn().pipe(map(isLoggedIn => {\n    if (isLoggedIn) {\n      return true;\n    }\n    return router.createUrlTree(['/login']);\n  }));\n};\n/**\n * A guard that prevents access to a route if the user is already logged in.\n * If the user is logged in, it redirects to the '/search' route.\n * If the user is not logged in, it allows access to the route.\n * @param _route - The activated route snapshot.\n * @param _state - The router state snapshot.\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\n */\nexport const discordAssociateGuard = (_route, _state) => {\n  console.log('The discordAssociateGuard is being called correctly');\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  const signed = localStorage.getItem('signed-user');\n  if (signed) {\n    const signedUser = JSON.parse(signed);\n    const isDiscordAssociate = signedUser.isDiscordAssociate;\n    if (!isDiscordAssociate) {\n      router.navigateByUrl('/associate');\n    }\n  } else {\n    console.error('Usuário não encontrado no localStorage');\n  }\n  // router.navigateByUrl('/search');\n  return true;\n};", "map": {"version": 3, "names": ["Router", "inject", "AuthService", "map", "loggedInGuard", "_route", "_state", "console", "log", "authService", "router", "isLoggedIn", "subscribe", "b", "navigateByUrl", "<PERSON>th<PERSON><PERSON>", "pipe", "createUrlTree", "discordAssociateGuard", "signed", "localStorage", "getItem", "<PERSON><PERSON><PERSON>", "JSON", "parse", "isDiscordAssociate", "error"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\core\\security\\guard\\auth.guard.ts"], "sourcesContent": ["import {\r\n  ActivatedRouteSnapshot,\r\n  CanActivateFn,\r\n  Router,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from '@angular/router';\r\nimport { inject } from '@angular/core';\r\nimport { AuthService } from '../auth/auth.service';\r\nimport { Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\n/**\r\n * A guard that prevents access to a route if the user is already logged in.\r\n * If the user is logged in, it redirects to the '/search' route.\r\n * If the user is not logged in, it allows access to the route.\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\r\n */\r\nexport const loggedInGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The loggedInGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n  let isLoggedIn = false;\r\n\r\n  authService.isLoggedIn().subscribe((b) => {\r\n    isLoggedIn = b;\r\n  });\r\n\r\n  if (isLoggedIn) {\r\n    router.navigateByUrl('/search');\r\n    return false;\r\n  }\r\n  return true;\r\n};\r\n\r\n/**\r\n * A guard that checks if the user is authenticated before allowing access to a route.\r\n * If the user is not authenticated, it redirects to the login page.\r\n *\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean indicating whether the user is authenticated or not, or a UrlTree to redirect to the login page.\r\n */\r\nexport const authGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): Observable<boolean | UrlTree> => {\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n  \r\n  return authService.isLoggedIn().pipe(\r\n    map(isLoggedIn => {\r\n      if (isLoggedIn) {\r\n        return true;\r\n      }\r\n      return router.createUrlTree(['/login']);\r\n    })\r\n  );\r\n};\r\n\r\n/**\r\n * A guard that prevents access to a route if the user is already logged in.\r\n * If the user is logged in, it redirects to the '/search' route.\r\n * If the user is not logged in, it allows access to the route.\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\r\n */\r\nexport const discordAssociateGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The discordAssociateGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n\r\n  const signed = localStorage.getItem('signed-user');\r\n\r\n  if (signed) {\r\n    const signedUser = JSON.parse(signed);\r\n    const isDiscordAssociate = signedUser.isDiscordAssociate;\r\n    if(!isDiscordAssociate){\r\n      router.navigateByUrl('/associate');\r\n    }\r\n  } else {\r\n    console.error('Usuário não encontrado no localStorage');\r\n  }\r\n  // router.navigateByUrl('/search');\r\n  return true;\r\n};\r\n"], "mappings": "AAAA,SAGEA,MAAM,QAGD,iBAAiB;AACxB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,sBAAsB;AAElD,SAASC,GAAG,QAAQ,gBAAgB;AAEpC;;;;;;;;AAQA,OAAO,MAAMC,aAAa,GAAkBA,CAC1CC,MAA8B,EAC9BC,MAA2B,KACN;EACrBC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAC1D,MAAMC,WAAW,GAAGR,MAAM,CAACC,WAAW,CAAC;EACvC,MAAMQ,MAAM,GAAGT,MAAM,CAACD,MAAM,CAAC;EAC7B,IAAIW,UAAU,GAAG,KAAK;EAEtBF,WAAW,CAACE,UAAU,EAAE,CAACC,SAAS,CAAEC,CAAC,IAAI;IACvCF,UAAU,GAAGE,CAAC;EAChB,CAAC,CAAC;EAEF,IAAIF,UAAU,EAAE;IACdD,MAAM,CAACI,aAAa,CAAC,SAAS,CAAC;IAC/B,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AAED;;;;;;;;AAQA,OAAO,MAAMC,SAAS,GAAkBA,CACtCV,MAA8B,EAC9BC,MAA2B,KACM;EACjC,MAAMG,WAAW,GAAGR,MAAM,CAACC,WAAW,CAAC;EACvC,MAAMQ,MAAM,GAAGT,MAAM,CAACD,MAAM,CAAC;EAE7B,OAAOS,WAAW,CAACE,UAAU,EAAE,CAACK,IAAI,CAClCb,GAAG,CAACQ,UAAU,IAAG;IACf,IAAIA,UAAU,EAAE;MACd,OAAO,IAAI;IACb;IACA,OAAOD,MAAM,CAACO,aAAa,CAAC,CAAC,QAAQ,CAAC,CAAC;EACzC,CAAC,CAAC,CACH;AACH,CAAC;AAED;;;;;;;;AAQA,OAAO,MAAMC,qBAAqB,GAAkBA,CAClDb,MAA8B,EAC9BC,MAA2B,KACN;EACrBC,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;EAClE,MAAMC,WAAW,GAAGR,MAAM,CAACC,WAAW,CAAC;EACvC,MAAMQ,MAAM,GAAGT,MAAM,CAACD,MAAM,CAAC;EAE7B,MAAMmB,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EAElD,IAAIF,MAAM,EAAE;IACV,MAAMG,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;IACrC,MAAMM,kBAAkB,GAAGH,UAAU,CAACG,kBAAkB;IACxD,IAAG,CAACA,kBAAkB,EAAC;MACrBf,MAAM,CAACI,aAAa,CAAC,YAAY,CAAC;IACpC;EACF,CAAC,MAAM;IACLP,OAAO,CAACmB,KAAK,CAAC,wCAAwC,CAAC;EACzD;EACA;EACA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}