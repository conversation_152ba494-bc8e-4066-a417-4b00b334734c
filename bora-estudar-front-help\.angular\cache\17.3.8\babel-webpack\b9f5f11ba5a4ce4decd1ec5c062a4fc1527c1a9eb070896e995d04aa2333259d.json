{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatButton } from '@angular/material/button';\nimport { MatChipListbox, MatChipOption } from '@angular/material/chips';\nimport { MatOption } from '@angular/material/core';\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\nimport { MatInput } from '@angular/material/input';\nimport { MatSelect } from '@angular/material/select';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService } from 'primeng/api';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/snack-bar\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../study-group.service\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/autocomplete\";\nimport * as i8 from \"primeng/toast\";\nconst _c0 = [\"input\"];\nfunction StudyCreateGroupComponent_mat_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" (\", option_r2.code, \") \", option_r2.name, \" \");\n  }\n}\nfunction StudyCreateGroupComponent_mat_chip_option_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip-option\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", day_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", day_r3.name, \" \");\n  }\n}\nexport let StudyCreateGroupComponent = /*#__PURE__*/(() => {\n  class StudyCreateGroupComponent {\n    constructor(snackBar, messageService, router, cdr, service, builder) {\n      this.snackBar = snackBar;\n      this.messageService = messageService;\n      this.router = router;\n      this.cdr = cdr;\n      this.service = service;\n      this.builder = builder;\n      this.options = [];\n      this.daysOfWeek = [{\n        id: 1,\n        name: 'Dom'\n      }, {\n        id: 2,\n        name: 'Seg'\n      }, {\n        id: 3,\n        name: 'Ter'\n      }, {\n        id: 4,\n        name: 'Qua'\n      }, {\n        id: 5,\n        name: 'Qui'\n      }, {\n        id: 6,\n        name: 'Sex'\n      }, {\n        id: 7,\n        name: 'Sáb'\n      }];\n      this.modalities = [{\n        value: 'presencial',\n        viewValue: 'Presencial'\n      }, {\n        value: 'online',\n        viewValue: 'Online'\n      }, {\n        value: 'hibrido',\n        viewValue: 'Híbrido'\n      }];\n      this.selectedModality = null;\n      this.subscription = null;\n      this.formulario = this.builder.group({\n        title: [''],\n        description: [''],\n        campoOculto: [''],\n        subject: [''],\n        maxStudents: [''],\n        meetingTime: [''],\n        modality: ['REMOTE'],\n        weekdays: ['']\n      });\n    }\n    ngOnInit() {\n      this.service.getSubjects().subscribe(dados => {\n        console.error('Dados carregados:', dados);\n        this.service.subjects = dados;\n        this.options = dados;\n        this.filteredOptions = this.options.slice();\n      });\n    }\n    filter() {\n      const filterValue = this.input.nativeElement.value.toLowerCase();\n      this.filteredOptions = this.service.subjects.filter(option => option.id.toString().includes(filterValue) || option.name.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue));\n    }\n    onOptionSelected(option) {\n      this.formulario.get('subject')?.patchValue(option);\n      this.formulario.get('campoOculto')?.patchValue(option.code + ' - ' + option.name);\n    }\n    createGroups() {\n      const idUsuario = localStorage.getItem('idUsuario');\n      const id = Number(idUsuario);\n      const title = this.formulario?.value.subject.code + ' ' + this.formulario?.value.subject.name;\n      const studyGroupData = {\n        title: title,\n        description: this.formulario?.value.description,\n        ownerId: id,\n        subject: this.formulario?.value.subject,\n        weekdays: this.formulario?.value.weekdays,\n        meetingTime: this.formulario?.value.meetingTime,\n        maxStudents: this.formulario?.value.maxStudents,\n        modality: this.formulario?.value.modality\n      };\n      this.subscription = this.service.createStudyGroup(studyGroupData).subscribe({\n        next: response => {\n          console.error('Grupo de estudo criado com sucesso:', response);\n          const mappedStudyGroup = this.service.mappingStudyGroup(response);\n          this.service.setStudyGroup(mappedStudyGroup);\n          this.snackBar.open('Grupo de estudo criado com sucesso!', 'X', {\n            duration: 2500\n          });\n          this.router.navigate([`/detail/${response.id}`]);\n        },\n        error: error => {\n          console.error('Erro ao criar grupo de estudo:', error);\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.subscription?.unsubscribe(); // Cancela a assinatura ao destruir o componente\n    }\n    static #_ = this.ɵfac = function StudyCreateGroupComponent_Factory(t) {\n      return new (t || StudyCreateGroupComponent)(i0.ɵɵdirectiveInject(i1.MatSnackBar), i0.ɵɵdirectiveInject(i2.MessageService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.StudyGroupService), i0.ɵɵdirectiveInject(i5.UntypedFormBuilder));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudyCreateGroupComponent,\n      selectors: [[\"app-study-create-group\"]],\n      viewQuery: function StudyCreateGroupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MessageService]), i0.ɵɵStandaloneFeature],\n      decls: 42,\n      vars: 9,\n      consts: [[\"input\", \"\"], [\"auto\", \"matAutocomplete\"], [1, \"filter-container\"], [3, \"formGroup\"], [1, \"row\"], [\"appearance\", \"outline\", 1, \"search_row_search_bar\"], [\"matInput\", \"\", \"formControlName\", \"campoOculto\", 1, \"search_row_search_bar\", 3, \"input\", \"focus\", \"matAutocomplete\"], [\"requireSelection\", \"\", 3, \"optionSelected\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"input100\"], [\"matInput\", \"\", \"formControlName\", \"description\"], [\"formControlName\", \"maxStudents\"], [3, \"value\"], [1, \"margin\", \"input30\"], [\"matInput\", \"\", \"type\", \"time\", \"formControlName\", \"meetingTime\"], [\"formControlName\", \"weekdays\", \"multiple\", \"\"], [1, \"allCenter\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"input100\", 3, \"click\"]],\n      template: function StudyCreateGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"form\", 3)(2, \"div\", 4)(3, \"mat-form-field\", 5)(4, \"mat-label\");\n          i0.ɵɵtext(5, \"Disciplina\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"input\", 6, 0);\n          i0.ɵɵlistener(\"input\", function StudyCreateGroupComponent_Template_input_input_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filter());\n          })(\"focus\", function StudyCreateGroupComponent_Template_input_focus_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filter());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"mat-autocomplete\", 7, 1);\n          i0.ɵɵlistener(\"optionSelected\", function StudyCreateGroupComponent_Template_mat_autocomplete_optionSelected_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onOptionSelected($event.option.value));\n          });\n          i0.ɵɵtemplate(10, StudyCreateGroupComponent_mat_option_10_Template, 2, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"mat-form-field\", 9)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Descri\\u00E7\\u00E3o\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"textarea\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 4)(16, \"mat-form-field\")(17, \"mat-label\");\n          i0.ɵɵtext(18, \"N\\u00FAmero de Alunos\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-select\", 11)(20, \"mat-option\", 12);\n          i0.ɵɵtext(21, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-option\", 12);\n          i0.ɵɵtext(23, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-option\", 12);\n          i0.ɵɵtext(25, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-option\", 12);\n          i0.ɵɵtext(27, \"5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-option\", 12);\n          i0.ɵɵtext(29, \"6\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 13)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Hora de In\\u00EDcio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"h2\");\n          i0.ɵɵtext(35, \"Dias da Semana\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"mat-chip-listbox\", 15);\n          i0.ɵɵtemplate(37, StudyCreateGroupComponent_mat_chip_option_37_Template, 2, 2, \"mat-chip-option\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 16);\n          i0.ɵɵelement(39, \"p-toast\");\n          i0.ɵɵelementStart(40, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function StudyCreateGroupComponent_Template_button_click_40_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.createGroups());\n          });\n          i0.ɵɵtext(41, \"Criar Grupo\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          const auto_r4 = i0.ɵɵreference(9);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.formulario);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"matAutocomplete\", auto_r4);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredOptions);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 5);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 6);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.daysOfWeek);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, ReactiveFormsModule, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, MatFormField, MatLabel, MatSelect, FormsModule, MatOption, MatChipListbox, MatChipOption, MatInput, MatButton, MatAutocompleteModule, i7.MatAutocomplete, i7.MatAutocompleteTrigger, ToastModule, i8.Toast, ProgressSpinnerModule],\n      styles: [\".filter-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:16px}.spacer[_ngcontent-%COMP%]{flex:1 1 auto}.input20[_ngcontent-%COMP%]{width:20%}.input30[_ngcontent-%COMP%]{width:30%}.input100[_ngcontent-%COMP%]{width:100%}.margin[_ngcontent-%COMP%]{margin-left:10px}.allCenter[_ngcontent-%COMP%]{display:flex;width:100%;margin-top:40px;justify-content:center}.search_row_search_bar[_ngcontent-%COMP%]{height:100%;width:100%}\"]\n    });\n  }\n  return StudyCreateGroupComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}