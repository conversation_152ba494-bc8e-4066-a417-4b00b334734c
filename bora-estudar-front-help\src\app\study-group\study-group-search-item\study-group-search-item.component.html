<div class="container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ studyGroup.title }}</mat-card-title>
      <mat-card-subtitle>{{ studyGroup.shortDescription }}</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <!-- <p>
        <strong>Monitor:</strong>
        {{ studyGroup.monitor }}
      </p> -->
      <p>
        <strong>Aluno<PERSON>:</strong>
        {{ studyGroup.participants }}
      </p>
      <p>
        <strong>Modalidade:</strong>
        {{ studyGroup.modality | titlecase }}
      </p>
      <p>
        <strong><PERSON><PERSON> de <PERSON>:</strong>
        {{ studyGroup.hour }}
      </p>
      <mat-chip-set>
        <mat-chip class="selected" *ngFor="let day of studyGroup.daysOfWeek">
         <p class="selectedText">{{day | titlecase}}</p>
        </mat-chip>
      </mat-chip-set>
    </mat-card-content>

    <mat-card-actions>
      <button
        mat-raised-button
        color="primary"
        class="full_width"
        (click)="openDetalheDialog(studyGroup)">
        Detalhes
      </button>
    </mat-card-actions>
  </mat-card>
</div>

<!-- [routerLink]="['/study-group', studyGroup.id]" -->
