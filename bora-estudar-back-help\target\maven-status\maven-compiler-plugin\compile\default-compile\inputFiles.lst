C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\repositories\EmailVerificationTokenRepository.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\entity\CategoryListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\CategoryCreateListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\entity\MessageListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\SubjectService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\security\JwtAuthenticationFilter.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\controllers\StudyGroupController.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\AuthService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\studygroup\StudyGroupFilterDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\RoleCreateListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\subject\SubjectNotFoundException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\MessageCreateListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\subject\SubjectCreateDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\UserMapper.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\TextChannelCreateListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\enums\ModalityEnum.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\subject\SubjectUpdateDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\security\JwtService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\controllers\UserController.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\converters\WeekdayToStudyGroupWeekday.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\VoiceChannelCreateListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\handlers\ValidationExceptionHandler.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\entity\VoiceChannelListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\repositories\StudyGroupUserRepository.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\MemberJoinListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\JsonMessage.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\UserDetailsServiceImpl.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\handlers\SubjectExceptionHandler.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\AuthDiscordService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\StudyGroupService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\user\UserCreateDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\controllers\SubjectController.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\entity\TextChannelListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\StudyGroupUserService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\studygroup\StudyGroupResponseDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\studygroup\TutorAlreadyRegisteredException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\configurations\ModelMapperConfig.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\converters\WeekdayToLong.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\user\UserResponseBasicDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\EventListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\user\UserLoginDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\studygroup\StudyGroupCreateDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\studygroup\NoStudentsSlotsAvailableException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\converters\StudyGroupUserToUserResponseBasicDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\handlers\SecurityExceptionHandler.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\StudyGroupMapper.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\BaseEntityMapper.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\handlers\StudyGroupExceptionHandler.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\DiscordOperationResult.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\studygroup\StudyGroupNotFoundException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\subject\SubjectResponseDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\EmailService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\entities\StudyGroupWeekday.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\studygroup\UserAlreadyRegisteredException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\security\AuthEntryPointJwt.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\BoraEstudarBackApplication.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\handlers\UserExceptionHandler.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\entities\User.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\converters\StudyGroupWeekdayToWeekday.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\configurations\SecurityConfig.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\entities\StudyGroupUser.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\EmailVerificationTokenService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\InviteCreateListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\entities\EmailVerificationToken.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\MessageUpdateListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\entities\Subject.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\repositories\StudyGroupRepository.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\repositories\UserRepository.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\studygroup\StudyGroupUpdateDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\studygroup\InsufficientPrivilegesException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\DiscordBotService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\studygroup\UserNotRegisteredException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\entities\Weekday.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\entities\StudyGroup.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\user\UserNotFoundException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\entity\InviteListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\user\UserUpdateDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\dtos\DiscordUserResponseDto.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\mappers\SubjectMapper.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\services\UserService.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\controllers\AuthController.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\repositories\SubjectRepository.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\controllers\AuthDiscordController.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\configuration\BotConfiguration.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\entity\RoleListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\entity\MemberListener.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\exceptions\security\TokenEmailNotFoundException.java
C:\Users\<USER>\Documents\uff\tcc\github\monolito-bora-estudar\bora-estudar-back-help\src\main\java\com\ufftcc\boraestudar\discord\event\MemberUpdateListener.java
