{"ast": null, "code": "import { ChangeDetectorRef, inject } from '@angular/core';\nimport { AuthService } from './core/security/auth/auth.service';\nimport { Router, RouterLink, RouterOutlet } from '@angular/router';\nimport { <PERSON><PERSON>avList, MatListItem } from '@angular/material/list';\nimport { <PERSON><PERSON><PERSON>avC<PERSON>r, <PERSON><PERSON>idenav, MatSidenavContent } from '@angular/material/sidenav';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatIconButton } from '@angular/material/button';\nimport { MatToolbar } from '@angular/material/toolbar';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { NavigationServiceService } from './study-group/navigation-service.service';\nimport { UserProfileComponent } from './shared/components/user-profile/user-profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"snav\"];\nfunction AppComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSearch());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_back\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-user-profile\", 9);\n    i0.ɵɵlistener(\"logoutClicked\", function AppComponent_Conditional_8_Template_app_user_profile_logoutClicked_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      i0.ɵɵnextContext();\n      const snav_r4 = i0.ɵɵreference(11);\n      return i0.ɵɵresetView(snav_r4.toggle());\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"menu\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppComponent_Conditional_12_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_12_Conditional_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.close());\n    });\n    i0.ɵɵtext(1, \"Home\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_12_Conditional_1_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.close());\n    });\n    i0.ɵɵtext(3, \"Criar Grupos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 13);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_12_Conditional_1_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.close());\n    });\n    i0.ɵɵtext(5, \"Meus Grupos\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-nav-list\");\n    i0.ɵɵtemplate(1, AppComponent_Conditional_12_Conditional_1_Template, 6, 0);\n    i0.ɵɵelementStart(2, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_12_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵtext(3, \"Sair\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.router.url !== \"/associate\" ? 1 : -1);\n  }\n}\nfunction AppComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-nav-list\")(1, \"a\", 14);\n    i0.ɵɵtext(2, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 15);\n    i0.ɵɵtext(4, \"Register\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(location) {\n      this.location = location;\n      this.appName = 'Bora Estudar UFF';\n      this.title = 'bora-estudar-front';\n      this.isLoggedIn = false;\n      this.user = undefined;\n      this.showBackIcon = false;\n      this.snackBar = inject(MatSnackBar);\n      this.authService = inject(AuthService);\n      this.router = inject(Router);\n      this.cdr = inject(ChangeDetectorRef);\n      this.navigationService = inject(NavigationServiceService);\n    }\n    ngOnInit() {\n      this.authService.isLoggedIn().subscribe(isLoggedIn => {\n        this.isLoggedIn = isLoggedIn;\n        this.cdr.detectChanges();\n      });\n      this.router.events.subscribe(() => {\n        this.showBackIcon = this.router.url !== '/search';\n        if (this.router.url === '/create') {\n          this.appName = 'Criar Grupo';\n        } else if (this.router.url === '/my-study-group') {\n          this.appName = 'Meus Grupos';\n        } else if (this.router.url.startsWith('/edit')) {\n          this.appName = 'Editar';\n        } else if (this.router.url.startsWith('/detail')) {\n          this.appName = 'Detalhes';\n        } else {\n          this.appName = 'Bora Estudar UFF';\n        }\n      });\n    }\n    logout() {\n      this.authService.logout().subscribe({\n        next: data => {\n          console.log(data);\n          this.router.navigateByUrl('/login');\n          this.close();\n          this.snackBar.open('Desconectado com sucesso!', 'X', {\n            duration: 2500\n          });\n        },\n        error: error => {\n          console.log(error);\n        }\n      });\n    }\n    getUser() {\n      this.authService.getUser().subscribe({\n        next: data => {\n          console.log(data);\n          this.user = data;\n          this.cdr.detectChanges();\n        },\n        error: error => {\n          console.log(error);\n        }\n      });\n    }\n    navigateToSearch() {\n      this.router.navigate(['/search']);\n    }\n    // navigateToSearch(): void {\n    //   const previousUrl = this.navigationService.getPreviousUrl();\n    //   if (previousUrl) {\n    //       this.router.navigate([previousUrl]);\n    //   } else {\n    //       this.router.navigate(['/home']);\n    //   }\n    // }\n    close() {\n      if (this.sidenav) {\n        this.sidenav.close();\n      }\n    }\n    static #_ = this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Location));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      viewQuery: function AppComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sidenav = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 17,\n      vars: 4,\n      consts: [[\"snav\", \"\"], [1, \"app_container\"], [\"color\", \"primary\", \"position\", \"start\", 1, \"header_toolbar\"], [\"mat-icon-button\", \"\"], [1, \"spacer\"], [1, \"app_name\"], [1, \"sidenav_container\"], [\"mode\", \"over\", 1, \"mat_sidenav_content\"], [\"mat-icon-button\", \"\", 3, \"click\"], [3, \"logoutClicked\"], [\"mat-list-item\", \"\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/search\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/create\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/my-study-group\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/login\"], [\"mat-list-item\", \"\", \"routerLink\", \"/register\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"body\")(1, \"div\", 1)(2, \"mat-toolbar\", 2);\n          i0.ɵɵtemplate(3, AppComponent_Conditional_3_Template, 3, 0, \"button\", 3);\n          i0.ɵɵelement(4, \"span\", 4);\n          i0.ɵɵelementStart(5, \"h1\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 4);\n          i0.ɵɵtemplate(8, AppComponent_Conditional_8_Template, 4, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"mat-sidenav-container\", 6)(10, \"mat-sidenav\", 7, 0);\n          i0.ɵɵtemplate(12, AppComponent_Conditional_12_Template, 4, 1, \"mat-nav-list\")(13, AppComponent_Conditional_13_Template, 5, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"mat-sidenav-content\")(15, \"router-outlet\");\n          i0.ɵɵelement(16, \"main\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(3, ctx.showBackIcon && ctx.isLoggedIn === true && ctx.router.url !== \"/associate\" ? 3 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.appName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(8, ctx.isLoggedIn === true ? 8 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(12, ctx.isLoggedIn === true ? 12 : 13);\n        }\n      },\n      dependencies: [MatToolbar, MatIconButton, MatIcon, MatSidenavContainer, MatSidenav, MatNavList, MatListItem, RouterLink, MatSidenavContent, RouterOutlet, UserProfileComponent],\n      styles: [\".header_toolbar[_ngcontent-%COMP%]{position:fixed}h1.app_name[_ngcontent-%COMP%]{margin-left:8px}.sidenav_container[_ngcontent-%COMP%]{margin-top:65px;display:flex;flex-direction:column;position:fixed;inset:0}.mat_sidenav_content[_ngcontent-%COMP%]{width:60%}.header_container[_ngcontent-%COMP%]   .sidenav_container[_ngcontent-%COMP%]{flex:1 0 auto}.example-spacer[_ngcontent-%COMP%]{flex:1 1 auto}  .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{width:100%!important}  .p-progress-spinner-circle{stroke:#3f51b5!important}\"]\n    });\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}