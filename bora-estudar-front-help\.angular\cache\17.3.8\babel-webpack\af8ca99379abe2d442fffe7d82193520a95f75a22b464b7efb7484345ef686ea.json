{"ast": null, "code": "import { concatMap } from './concatMap';\nimport { isFunction } from '../util/isFunction';\nexport function concatMapTo(innerObservable, resultSelector) {\n  return isFunction(resultSelector) ? concatMap(() => innerObservable, resultSelector) : concatMap(() => innerObservable);\n}\n//# sourceMappingURL=concatMapTo.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}