{"ast": null, "code": "import { catchError, map, of } from 'rxjs';\nimport { inject } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nconst AUTH_API = environment.authApi;\nconst httpOptions = {\n  headers: new HttpHeaders({\n    'Content-Type': 'application/json'\n  })\n};\nexport class StudyGroupService {\n  constructor() {\n    this.studyGroups = [];\n    this.myStudyGroups = [];\n    this.subjects = [];\n    this.http = inject(HttpClient);\n  }\n  getStudyGroups() {\n    return this.http.post(`${AUTH_API}/study-groups/filter`, {}, httpOptions).pipe(map(studyGroups => studyGroups.map(this.mappingStudyGroup)), catchError(error => {\n      console.error('Erro na chamada de API', error);\n      return of([]);\n    }));\n  }\n  getStudyGroupsFind(studentId) {\n    const requestBody = {\n      studentId\n    };\n    return this.http.post(`${AUTH_API}/study-groups/filter`, requestBody, httpOptions).pipe(map(studyGroups => studyGroups.map(this.mappingStudyGroup)), catchError(error => {\n      console.error('Erro na chamada de API', error);\n      return of([]);\n    }));\n    ;\n  }\n  mappingStudyGroup(item) {\n    const {\n      id,\n      description,\n      tutor,\n      subject,\n      students,\n      maxStudents,\n      meetingTime,\n      modality,\n      weekdays,\n      ownerId,\n      discordInviteUrl\n    } = item;\n    // const shortDescription =\n    //   description.length > 50 ? description.substr(0, 100) + '...' : description;\n    let countTutor = 0;\n    if (tutor) {\n      countTutor = 1;\n    }\n    const monitorText = `${countTutor}/${1}`;\n    const participantsCount = students.length;\n    const participantsText = `${participantsCount}/${maxStudents}`;\n    const mappedHour = meetingTime.substr(0, 5);\n    let mappedModality = '';\n    switch (modality) {\n      case 'REMOTE':\n        mappedModality = 'remoto';\n        break;\n      case 'PRESENCIAL':\n        mappedModality = 'presencial';\n        break;\n      default:\n        mappedModality = 'híbrido';\n    }\n    return {\n      id: id,\n      title: subject.name,\n      code: subject.code,\n      ownerId: ownerId,\n      shortDescription: description,\n      modality: mappedModality,\n      hour: mappedHour,\n      monitor: monitorText,\n      participants: participantsText,\n      students: students,\n      daysOfWeek: weekdays.map(day => day.name.toLowerCase().substring(0, 3)),\n      discordInviteUrl: discordInviteUrl\n    };\n  }\n  joinGroupService(groupId, id) {\n    return this.http.post(`${AUTH_API}/study-groups/${groupId}/students/${id}/join`, {}, httpOptions);\n  }\n  leaveGroupService(groupId, id) {\n    return this.http.post(`${AUTH_API}/study-groups/${groupId}/students/${id}/leave`, {}, httpOptions);\n  }\n  getSubjects() {\n    return this.http.get(`${AUTH_API}/subjects`);\n  }\n  getStudyGroupId(studyGroupId) {\n    return this.http.get(`${AUTH_API}/study-groups/${studyGroupId}`);\n  }\n  createStudyGroup(studyGroupData) {\n    return this.http.post(`${AUTH_API}/study-groups`, studyGroupData);\n  }\n  editStudyGroup(studyGroupData, groupId) {\n    return this.http.put(`${AUTH_API}/study-groups/${groupId}`, studyGroupData);\n  }\n  setStudyGroup(data) {\n    this.studyGroupParam = data;\n  }\n  getStudyGroup() {\n    return this.studyGroupParam;\n  }\n  static #_ = this.ɵfac = function StudyGroupService_Factory(t) {\n    return new (t || StudyGroupService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: StudyGroupService,\n    factory: StudyGroupService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["catchError", "map", "of", "inject", "HttpClient", "HttpHeaders", "AUTH_API", "environment", "authApi", "httpOptions", "headers", "StudyGroupService", "constructor", "studyGroups", "myStudyGroups", "subjects", "http", "getStudyGroups", "post", "pipe", "mappingStudyGroup", "error", "console", "getStudyGroupsFind", "studentId", "requestBody", "item", "id", "description", "tutor", "subject", "students", "maxStudents", "meetingTime", "modality", "weekdays", "ownerId", "discordInviteUrl", "countTutor", "monitorText", "participantsCount", "length", "participantsText", "mappedHour", "substr", "mappedModality", "title", "name", "code", "shortDescription", "hour", "monitor", "participants", "daysOfWeek", "day", "toLowerCase", "substring", "joinGroupService", "groupId", "leaveGroupService", "getSubjects", "get", "getStudyGroupId", "studyGroupId", "createStudyGroup", "studyGroupData", "editStudyGroup", "put", "setStudyGroup", "data", "studyGroupParam", "getStudyGroup", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\study-group\\study-group.service.ts"], "sourcesContent": ["import { catchError, map, Observable, of, tap } from 'rxjs';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { StudyGroup } from './study-group';\r\n\r\nconst AUTH_API = environment.authApi;\r\n\r\nconst httpOptions = {\r\n  headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n};\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class StudyGroupService {\r\n  private studyGroupParam: any;\r\n  studyGroups: StudyGroup[] = [];\r\n  myStudyGroups: StudyGroup[] = [];\r\n  subjects: any[] = [];\r\n\r\n  private readonly http = inject(HttpClient);\r\n  constructor() {}\r\n\r\n  getStudyGroups(): Observable<any[]> {\r\n    return this.http\r\n      .post<any[]>(`${AUTH_API}/study-groups/filter`, {}, httpOptions)\r\n      .pipe(\r\n        map((studyGroups) => studyGroups.map(this.mappingStudyGroup)),\r\n        catchError((error) => {\r\n          console.error('Erro na chamada de API', error);\r\n          return of([]);\r\n        })\r\n      );\r\n  }\r\n\r\n  getStudyGroupsFind(studentId: number): Observable<any[]> {\r\n    const requestBody = { studentId };\r\n\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/filter`, requestBody, httpOptions)\r\n    .pipe(\r\n      map((studyGroups) => studyGroups.map(this.mappingStudyGroup)),\r\n      catchError((error) => {\r\n        console.error('Erro na chamada de API', error);\r\n        return of([]);\r\n      })\r\n    );;\r\n  }\r\n\r\n  public mappingStudyGroup(item: any): any {\r\n    const {\r\n      id,\r\n      description,\r\n      tutor,\r\n      subject,\r\n      students,\r\n      maxStudents,\r\n      meetingTime,\r\n      modality,\r\n      weekdays,\r\n      ownerId,\r\n      discordInviteUrl\r\n    } = item;\r\n\r\n    // const shortDescription =\r\n    //   description.length > 50 ? description.substr(0, 100) + '...' : description;\r\n\r\n    let countTutor = 0;\r\n    if (tutor) {\r\n      countTutor = 1;\r\n    }\r\n    const monitorText = `${countTutor}/${1}`;\r\n\r\n    const participantsCount = students.length;\r\n    const participantsText = `${participantsCount}/${maxStudents}`;\r\n    const mappedHour = meetingTime.substr(0, 5);\r\n\r\n    let mappedModality = '';\r\n    switch (modality) {\r\n      case 'REMOTE':\r\n        mappedModality = 'remoto';\r\n        break;\r\n      case 'PRESENCIAL':\r\n        mappedModality = 'presencial';\r\n        break;\r\n      default:\r\n        mappedModality = 'híbrido';\r\n    }\r\n\r\n    return {\r\n      id: id,\r\n      title: subject.name,\r\n      code: subject.code,\r\n      ownerId: ownerId,\r\n      shortDescription: description,\r\n      modality: mappedModality,\r\n      hour: mappedHour,\r\n      monitor: monitorText,\r\n      participants: participantsText,\r\n      students: students,\r\n      daysOfWeek: weekdays.map((day: { name: string }) =>\r\n        day.name.toLowerCase().substring(0, 3)\r\n      ),\r\n      discordInviteUrl: discordInviteUrl\r\n    };\r\n  }\r\n\r\n  joinGroupService(groupId: number,id: number){\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/${groupId}/students/${id}/join`, {}, httpOptions)\r\n  }\r\n\r\n  leaveGroupService(groupId: number,id: number){\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/${groupId}/students/${id}/leave`, {}, httpOptions)\r\n  }\r\n\r\n  getSubjects(): Observable<any[]> {\r\n    return this.http.get<any[]>(`${AUTH_API}/subjects`);\r\n  }\r\n\r\n  getStudyGroupId(studyGroupId: number): Observable<any[]> {\r\n    return this.http.get<any[]>(`${AUTH_API}/study-groups/${studyGroupId}`);\r\n  }\r\n\r\n  createStudyGroup(studyGroupData: any): Observable<any> {\r\n    return this.http.post<any>(`${AUTH_API}/study-groups`, studyGroupData);\r\n  }\r\n\r\n  editStudyGroup(studyGroupData: any, groupId: number): Observable<any> {\r\n    return this.http.put<any>(`${AUTH_API}/study-groups/${groupId}`, studyGroupData);\r\n  }\r\n\r\n  setStudyGroup(data: any) {\r\n    this.studyGroupParam = data;\r\n  }\r\n\r\n  getStudyGroup() {\r\n    return this.studyGroupParam;\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,GAAG,EAAcC,EAAE,QAAa,MAAM;AAC3D,SAASC,MAAM,QAAoB,eAAe;AAClD,SAASC,UAAU,EAAEC,WAAW,QAAQ,sBAAsB;;AAG9D,MAAMC,QAAQ,GAAGC,WAAW,CAACC,OAAO;AAEpC,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,IAAIL,WAAW,CAAC;IAAE,cAAc,EAAE;EAAkB,CAAE;CAChE;AAKD,OAAM,MAAOM,iBAAiB;EAO5BC,YAAA;IALA,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,aAAa,GAAiB,EAAE;IAChC,KAAAC,QAAQ,GAAU,EAAE;IAEH,KAAAC,IAAI,GAAGb,MAAM,CAACC,UAAU,CAAC;EAC3B;EAEfa,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACD,IAAI,CACbE,IAAI,CAAQ,GAAGZ,QAAQ,sBAAsB,EAAE,EAAE,EAAEG,WAAW,CAAC,CAC/DU,IAAI,CACHlB,GAAG,CAAEY,WAAW,IAAKA,WAAW,CAACZ,GAAG,CAAC,IAAI,CAACmB,iBAAiB,CAAC,CAAC,EAC7DpB,UAAU,CAAEqB,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOnB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEAqB,kBAAkBA,CAACC,SAAiB;IAClC,MAAMC,WAAW,GAAG;MAAED;IAAS,CAAE;IAEjC,OAAO,IAAI,CAACR,IAAI,CAACE,IAAI,CAAQ,GAAGZ,QAAQ,sBAAsB,EAAEmB,WAAW,EAAEhB,WAAW,CAAC,CACxFU,IAAI,CACHlB,GAAG,CAAEY,WAAW,IAAKA,WAAW,CAACZ,GAAG,CAAC,IAAI,CAACmB,iBAAiB,CAAC,CAAC,EAC7DpB,UAAU,CAAEqB,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOnB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;IAAC;EACJ;EAEOkB,iBAAiBA,CAACM,IAAS;IAChC,MAAM;MACJC,EAAE;MACFC,WAAW;MACXC,KAAK;MACLC,OAAO;MACPC,QAAQ;MACRC,WAAW;MACXC,WAAW;MACXC,QAAQ;MACRC,QAAQ;MACRC,OAAO;MACPC;IAAgB,CACjB,GAAGX,IAAI;IAER;IACA;IAEA,IAAIY,UAAU,GAAG,CAAC;IAClB,IAAIT,KAAK,EAAE;MACTS,UAAU,GAAG,CAAC;IAChB;IACA,MAAMC,WAAW,GAAG,GAAGD,UAAU,IAAI,CAAC,EAAE;IAExC,MAAME,iBAAiB,GAAGT,QAAQ,CAACU,MAAM;IACzC,MAAMC,gBAAgB,GAAG,GAAGF,iBAAiB,IAAIR,WAAW,EAAE;IAC9D,MAAMW,UAAU,GAAGV,WAAW,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAE3C,IAAIC,cAAc,GAAG,EAAE;IACvB,QAAQX,QAAQ;MACd,KAAK,QAAQ;QACXW,cAAc,GAAG,QAAQ;QACzB;MACF,KAAK,YAAY;QACfA,cAAc,GAAG,YAAY;QAC7B;MACF;QACEA,cAAc,GAAG,SAAS;IAC9B;IAEA,OAAO;MACLlB,EAAE,EAAEA,EAAE;MACNmB,KAAK,EAAEhB,OAAO,CAACiB,IAAI;MACnBC,IAAI,EAAElB,OAAO,CAACkB,IAAI;MAClBZ,OAAO,EAAEA,OAAO;MAChBa,gBAAgB,EAAErB,WAAW;MAC7BM,QAAQ,EAAEW,cAAc;MACxBK,IAAI,EAAEP,UAAU;MAChBQ,OAAO,EAAEZ,WAAW;MACpBa,YAAY,EAAEV,gBAAgB;MAC9BX,QAAQ,EAAEA,QAAQ;MAClBsB,UAAU,EAAElB,QAAQ,CAAClC,GAAG,CAAEqD,GAAqB,IAC7CA,GAAG,CAACP,IAAI,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CACvC;MACDnB,gBAAgB,EAAEA;KACnB;EACH;EAEAoB,gBAAgBA,CAACC,OAAe,EAAC/B,EAAU;IACzC,OAAO,IAAI,CAACX,IAAI,CAACE,IAAI,CAAQ,GAAGZ,QAAQ,iBAAiBoD,OAAO,aAAa/B,EAAE,OAAO,EAAE,EAAE,EAAElB,WAAW,CAAC;EAC1G;EAEAkD,iBAAiBA,CAACD,OAAe,EAAC/B,EAAU;IAC1C,OAAO,IAAI,CAACX,IAAI,CAACE,IAAI,CAAQ,GAAGZ,QAAQ,iBAAiBoD,OAAO,aAAa/B,EAAE,QAAQ,EAAE,EAAE,EAAElB,WAAW,CAAC;EAC3G;EAEAmD,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC5C,IAAI,CAAC6C,GAAG,CAAQ,GAAGvD,QAAQ,WAAW,CAAC;EACrD;EAEAwD,eAAeA,CAACC,YAAoB;IAClC,OAAO,IAAI,CAAC/C,IAAI,CAAC6C,GAAG,CAAQ,GAAGvD,QAAQ,iBAAiByD,YAAY,EAAE,CAAC;EACzE;EAEAC,gBAAgBA,CAACC,cAAmB;IAClC,OAAO,IAAI,CAACjD,IAAI,CAACE,IAAI,CAAM,GAAGZ,QAAQ,eAAe,EAAE2D,cAAc,CAAC;EACxE;EAEAC,cAAcA,CAACD,cAAmB,EAAEP,OAAe;IACjD,OAAO,IAAI,CAAC1C,IAAI,CAACmD,GAAG,CAAM,GAAG7D,QAAQ,iBAAiBoD,OAAO,EAAE,EAAEO,cAAc,CAAC;EAClF;EAEAG,aAAaA,CAACC,IAAS;IACrB,IAAI,CAACC,eAAe,GAAGD,IAAI;EAC7B;EAEAE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACD,eAAe;EAC7B;EAAC,QAAAE,CAAA,G;qBA1HU7D,iBAAiB;EAAA;EAAA,QAAA8D,EAAA,G;WAAjB9D,iBAAiB;IAAA+D,OAAA,EAAjB/D,iBAAiB,CAAAgE,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}