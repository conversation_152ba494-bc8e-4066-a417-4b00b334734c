<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="testdb">
  <database-model serializer="dbm" dbms="H2" family-id="H2" format-version="4.48">
    <root id="1"/>
    <database id="2" parent="1" name="TESTDB">
      <Current>1</Current>
    </database>
    <collation id="3" parent="2" name="ALBANIAN"/>
    <collation id="4" parent="2" name="ALBANIAN_ALBANIA"/>
    <collation id="5" parent="2" name="ARABIC"/>
    <collation id="6" parent="2" name="ARABIC_ALGERIA"/>
    <collation id="7" parent="2" name="ARABIC_BAHRAIN"/>
    <collation id="8" parent="2" name="ARABIC_EGYPT"/>
    <collation id="9" parent="2" name="ARABIC_IRAQ"/>
    <collation id="10" parent="2" name="ARABIC_JORDAN"/>
    <collation id="11" parent="2" name="ARABIC_KUWAIT"/>
    <collation id="12" parent="2" name="ARABIC_LEBANON"/>
    <collation id="13" parent="2" name="ARABIC_LIBYA"/>
    <collation id="14" parent="2" name="ARABIC_MOROCCO"/>
    <collation id="15" parent="2" name="ARABIC_OMAN"/>
    <collation id="16" parent="2" name="ARABIC_QATAR"/>
    <collation id="17" parent="2" name="ARABIC_SAUDI_ARABIA"/>
    <collation id="18" parent="2" name="ARABIC_SUDAN"/>
    <collation id="19" parent="2" name="ARABIC_SYRIA"/>
    <collation id="20" parent="2" name="ARABIC_TUNISIA"/>
    <collation id="21" parent="2" name="ARABIC_UNITED_ARAB_EMIRATES"/>
    <collation id="22" parent="2" name="ARABIC_YEMEN"/>
    <collation id="23" parent="2" name="BELARUSIAN"/>
    <collation id="24" parent="2" name="BELARUSIAN_BELARUS"/>
    <collation id="25" parent="2" name="BULGARIAN"/>
    <collation id="26" parent="2" name="BULGARIAN_BULGARIA"/>
    <collation id="27" parent="2" name="CATALAN"/>
    <collation id="28" parent="2" name="CATALAN_SPAIN"/>
    <collation id="29" parent="2" name="CHINESE"/>
    <collation id="30" parent="2" name="CHINESE_CHINA"/>
    <collation id="31" parent="2" name="CHINESE_HONG_KONG_SAR_CHINA"/>
    <collation id="32" parent="2" name="CHINESE_SINGAPORE"/>
    <collation id="33" parent="2" name="CHINESE_TAIWAN"/>
    <collation id="34" parent="2" name="CROATIAN"/>
    <collation id="35" parent="2" name="CROATIAN_CROATIA"/>
    <collation id="36" parent="2" name="CZECH"/>
    <collation id="37" parent="2" name="CZECH_CZECHIA"/>
    <collation id="38" parent="2" name="DANISH"/>
    <collation id="39" parent="2" name="DANISH_DENMARK"/>
    <collation id="40" parent="2" name="DUTCH"/>
    <collation id="41" parent="2" name="DUTCH_BELGIUM"/>
    <collation id="42" parent="2" name="DUTCH_NETHERLANDS"/>
    <collation id="43" parent="2" name="ENGLISH"/>
    <collation id="44" parent="2" name="ENGLISH_AUSTRALIA"/>
    <collation id="45" parent="2" name="ENGLISH_CANADA"/>
    <collation id="46" parent="2" name="ENGLISH_INDIA"/>
    <collation id="47" parent="2" name="ENGLISH_IRELAND"/>
    <collation id="48" parent="2" name="ENGLISH_MALTA"/>
    <collation id="49" parent="2" name="ENGLISH_NEW_ZEALAND"/>
    <collation id="50" parent="2" name="ENGLISH_PHILIPPINES"/>
    <collation id="51" parent="2" name="ENGLISH_SINGAPORE"/>
    <collation id="52" parent="2" name="ENGLISH_SOUTH_AFRICA"/>
    <collation id="53" parent="2" name="ENGLISH_UNITED_KINGDOM"/>
    <collation id="54" parent="2" name="ENGLISH_UNITED_STATES"/>
    <collation id="55" parent="2" name="ESTONIAN"/>
    <collation id="56" parent="2" name="ESTONIAN_ESTONIA"/>
    <collation id="57" parent="2" name="FINNISH"/>
    <collation id="58" parent="2" name="FINNISH_FINLAND"/>
    <collation id="59" parent="2" name="FRENCH"/>
    <collation id="60" parent="2" name="FRENCH_BELGIUM"/>
    <collation id="61" parent="2" name="FRENCH_CANADA"/>
    <collation id="62" parent="2" name="FRENCH_FRANCE"/>
    <collation id="63" parent="2" name="FRENCH_LUXEMBOURG"/>
    <collation id="64" parent="2" name="FRENCH_SWITZERLAND"/>
    <collation id="65" parent="2" name="GERMAN"/>
    <collation id="66" parent="2" name="GERMAN_AUSTRIA"/>
    <collation id="67" parent="2" name="GERMAN_GERMANY"/>
    <collation id="68" parent="2" name="GERMAN_LUXEMBOURG"/>
    <collation id="69" parent="2" name="GERMAN_SWITZERLAND"/>
    <collation id="70" parent="2" name="GREEK"/>
    <collation id="71" parent="2" name="GREEK_CYPRUS"/>
    <collation id="72" parent="2" name="GREEK_GREECE"/>
    <collation id="73" parent="2" name="HEBREW"/>
    <collation id="74" parent="2" name="HEBREW_ISRAEL"/>
    <collation id="75" parent="2" name="HINDI"/>
    <collation id="76" parent="2" name="HINDI_INDIA"/>
    <collation id="77" parent="2" name="HUNGARIAN"/>
    <collation id="78" parent="2" name="HUNGARIAN_HUNGARY"/>
    <collation id="79" parent="2" name="ICELANDIC"/>
    <collation id="80" parent="2" name="ICELANDIC_ICELAND"/>
    <collation id="81" parent="2" name="INDONESIAN"/>
    <collation id="82" parent="2" name="INDONESIAN_INDONESIA"/>
    <collation id="83" parent="2" name="IRISH"/>
    <collation id="84" parent="2" name="IRISH_IRELAND"/>
    <collation id="85" parent="2" name="ITALIAN"/>
    <collation id="86" parent="2" name="ITALIAN_ITALY"/>
    <collation id="87" parent="2" name="ITALIAN_SWITZERLAND"/>
    <collation id="88" parent="2" name="JAPANESE"/>
    <collation id="89" parent="2" name="JAPANESE_JAPAN"/>
    <collation id="90" parent="2" name="JAPANESE_JAPAN_JP"/>
    <collation id="91" parent="2" name="KOREAN"/>
    <collation id="92" parent="2" name="KOREAN_SOUTH_KOREA"/>
    <collation id="93" parent="2" name="LATVIAN"/>
    <collation id="94" parent="2" name="LATVIAN_LATVIA"/>
    <collation id="95" parent="2" name="LITHUANIAN"/>
    <collation id="96" parent="2" name="LITHUANIAN_LITHUANIA"/>
    <collation id="97" parent="2" name="MACEDONIAN"/>
    <collation id="98" parent="2" name="MACEDONIAN_NORTH_MACEDONIA"/>
    <collation id="99" parent="2" name="MALAY"/>
    <collation id="100" parent="2" name="MALAY_MALAYSIA"/>
    <collation id="101" parent="2" name="MALTESE"/>
    <collation id="102" parent="2" name="MALTESE_MALTA"/>
    <collation id="103" parent="2" name="NORWEGIAN"/>
    <collation id="104" parent="2" name="NORWEGIAN_BOKMÅL"/>
    <collation id="105" parent="2" name="NORWEGIAN_BOKMÅL_NORWAY"/>
    <collation id="106" parent="2" name="NORWEGIAN_NORWAY"/>
    <collation id="107" parent="2" name="NORWEGIAN_NORWAY_NY"/>
    <collation id="108" parent="2" name="NORWEGIAN_NYNORSK_NORWAY"/>
    <collation id="109" parent="2" name="OFF"/>
    <collation id="110" parent="2" name="POLISH"/>
    <collation id="111" parent="2" name="POLISH_POLAND"/>
    <collation id="112" parent="2" name="PORTUGUESE"/>
    <collation id="113" parent="2" name="PORTUGUESE_BRAZIL"/>
    <collation id="114" parent="2" name="PORTUGUESE_PORTUGAL"/>
    <collation id="115" parent="2" name="ROMANIAN"/>
    <collation id="116" parent="2" name="ROMANIAN_ROMANIA"/>
    <collation id="117" parent="2" name="RUSSIAN"/>
    <collation id="118" parent="2" name="RUSSIAN_RUSSIA"/>
    <collation id="119" parent="2" name="SERBIAN"/>
    <collation id="120" parent="2" name="SERBIAN_BOSNIA_&amp;_HERZEGOVINA"/>
    <collation id="121" parent="2" name="SERBIAN_MONTENEGRO"/>
    <collation id="122" parent="2" name="SERBIAN_SERBIA"/>
    <collation id="123" parent="2" name="SERBIAN_SERBIA_AND_MONTENEGRO"/>
    <collation id="124" parent="2" name="SLOVAK"/>
    <collation id="125" parent="2" name="SLOVAK_SLOVAKIA"/>
    <collation id="126" parent="2" name="SLOVENIAN"/>
    <collation id="127" parent="2" name="SLOVENIAN_SLOVENIA"/>
    <collation id="128" parent="2" name="SPANISH"/>
    <collation id="129" parent="2" name="SPANISH_ARGENTINA"/>
    <collation id="130" parent="2" name="SPANISH_BOLIVIA"/>
    <collation id="131" parent="2" name="SPANISH_CHILE"/>
    <collation id="132" parent="2" name="SPANISH_COLOMBIA"/>
    <collation id="133" parent="2" name="SPANISH_COSTA_RICA"/>
    <collation id="134" parent="2" name="SPANISH_CUBA"/>
    <collation id="135" parent="2" name="SPANISH_DOMINICAN_REPUBLIC"/>
    <collation id="136" parent="2" name="SPANISH_ECUADOR"/>
    <collation id="137" parent="2" name="SPANISH_EL_SALVADOR"/>
    <collation id="138" parent="2" name="SPANISH_GUATEMALA"/>
    <collation id="139" parent="2" name="SPANISH_HONDURAS"/>
    <collation id="140" parent="2" name="SPANISH_MEXICO"/>
    <collation id="141" parent="2" name="SPANISH_NICARAGUA"/>
    <collation id="142" parent="2" name="SPANISH_PANAMA"/>
    <collation id="143" parent="2" name="SPANISH_PARAGUAY"/>
    <collation id="144" parent="2" name="SPANISH_PERU"/>
    <collation id="145" parent="2" name="SPANISH_PUERTO_RICO"/>
    <collation id="146" parent="2" name="SPANISH_SPAIN"/>
    <collation id="147" parent="2" name="SPANISH_UNITED_STATES"/>
    <collation id="148" parent="2" name="SPANISH_URUGUAY"/>
    <collation id="149" parent="2" name="SPANISH_VENEZUELA"/>
    <collation id="150" parent="2" name="SWEDISH"/>
    <collation id="151" parent="2" name="SWEDISH_SWEDEN"/>
    <collation id="152" parent="2" name="THAI"/>
    <collation id="153" parent="2" name="THAI_THAILAND"/>
    <collation id="154" parent="2" name="THAI_THAILAND_TH"/>
    <collation id="155" parent="2" name="TURKISH"/>
    <collation id="156" parent="2" name="TURKISH_TURKEY"/>
    <collation id="157" parent="2" name="UKRAINIAN"/>
    <collation id="158" parent="2" name="UKRAINIAN_UKRAINE"/>
    <collation id="159" parent="2" name="VIETNAMESE"/>
    <collation id="160" parent="2" name="VIETNAMESE_VIETNAM"/>
    <role id="161" parent="2" name="PUBLIC"/>
    <schema id="162" parent="2" name="INFORMATION_SCHEMA"/>
    <schema id="163" parent="2" name="PUBLIC">
      <Current>1</Current>
    </schema>
    <user id="164" parent="2" name="SA">
      <Admin>1</Admin>
    </user>
    <table id="165" parent="163" name="APPLICATION_USER"/>
    <table id="166" parent="163" name="EMAIL_VERIFICATION_TOKEN"/>
    <table id="167" parent="163" name="STUDY_GROUP"/>
    <table id="168" parent="163" name="STUDY_GROUP_USER"/>
    <table id="169" parent="163" name="STUDY_GROUP_WEEKDAY"/>
    <table id="170" parent="163" name="SUBJECT"/>
    <table id="171" parent="163" name="WEEKDAY"/>
    <column id="172" parent="165" name="APUS_IS_ENABLED">
      <DasType>BOOLEAN|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="173" parent="165" name="APUS_ID_DISCORD">
      <DasType>BIGINT|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="174" parent="165" name="APUS_SQ_USER">
      <ColumnKind>generated-stored</ColumnKind>
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <SequenceInline>1</SequenceInline>
      <SequenceDasType>BIGINT|0s</SequenceDasType>
      <SequenceSequenceIdentity>1(1)</SequenceSequenceIdentity>
      <SequenceStartValue>1</SequenceStartValue>
    </column>
    <column id="175" parent="165" name="APUS_DS_EMAIL">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="176" parent="165" name="APUS_DS_NAME">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="177" parent="165" name="APUS_DS_PASSWORD">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="178" parent="165" name="PRIMARY_KEY_1">
      <ColNames>APUS_SQ_USER</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="179" parent="165" name="CONSTRAINT_INDEX_1">
      <ColNames>APUS_DS_EMAIL</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <key id="180" parent="165" name="CONSTRAINT_12">
      <ColNames>APUS_SQ_USER</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
    </key>
    <key id="181" parent="165" name="CONSTRAINT_1">
      <ColNames>APUS_DS_EMAIL</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </key>
    <column id="182" parent="166" name="APUS_SQ_USER">
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="183" parent="166" name="EMVT_DT_VALIDITY">
      <DasType>TIMESTAMP|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="184" parent="166" name="EMVT_SQ_EMAIL_VERIFICATION_TOKEN">
      <ColumnKind>generated-stored</ColumnKind>
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <SequenceInline>1</SequenceInline>
      <SequenceDasType>BIGINT|0s</SequenceDasType>
      <SequenceSequenceIdentity>1(1)</SequenceSequenceIdentity>
      <SequenceStartValue>1</SequenceStartValue>
    </column>
    <column id="185" parent="166" name="EMVT_DS_TOKEN">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <foreign-key id="186" parent="166" name="FKHNVE4ECWWYISBMU836CH9B944">
      <ColNames>APUS_SQ_USER</ColNames>
      <OnDelete>restrict</OnDelete>
      <OnUpdate>restrict</OnUpdate>
      <RefKeyName>CONSTRAINT_12</RefKeyName>
      <RefTableName>APPLICATION_USER</RefTableName>
    </foreign-key>
    <index id="187" parent="166" name="CONSTRAINT_INDEX_9">
      <ColNames>APUS_SQ_USER</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="188" parent="166" name="PRIMARY_KEY_9">
      <ColNames>EMVT_SQ_EMAIL_VERIFICATION_TOKEN</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="189" parent="166" name="CONSTRAINT_INDEX_94">
      <ColNames>EMVT_DS_TOKEN</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <key id="190" parent="166" name="CONSTRAINT_94F">
      <ColNames>EMVT_SQ_EMAIL_VERIFICATION_TOKEN</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
    </key>
    <key id="191" parent="166" name="CONSTRAINT_9">
      <ColNames>APUS_SQ_USER</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </key>
    <key id="192" parent="166" name="CONSTRAINT_94">
      <ColNames>EMVT_DS_TOKEN</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </key>
    <check id="193" parent="167" name="CONSTRAINT_4">
      <NameSurrogate>1</NameSurrogate>
      <Predicate>&quot;STGR_DS_MODALITY&quot; IN(&apos;PRESENTIAL&apos;, &apos;REMOTE&apos;, &apos;HYBRID&apos;)</Predicate>
    </check>
    <column id="194" parent="167" name="STGR_DT_MEETING_TIME">
      <DasType>TIME(6)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="195" parent="167" name="STGR_IS_PRIVATE">
      <DasType>BOOLEAN|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="196" parent="167" name="STGR_QT_MAX_STUDENTS">
      <DasType>INTEGER|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="197" parent="167" name="STGR_ID_DISCORD">
      <DasType>BIGINT|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="198" parent="167" name="STGR_SQ_STUDY_GROUP">
      <ColumnKind>generated-stored</ColumnKind>
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <SequenceInline>1</SequenceInline>
      <SequenceDasType>BIGINT|0s</SequenceDasType>
      <SequenceSequenceIdentity>1(1)</SequenceSequenceIdentity>
      <SequenceStartValue>1</SequenceStartValue>
    </column>
    <column id="199" parent="167" name="STGR_USER_SQ_USER_OWNER">
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="200" parent="167" name="SUBJ_SQ_SUBJECT">
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="201" parent="167" name="STGR_DS_DESCRIPTION">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="202" parent="167" name="STGR_DS_MODALITY">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="203" parent="167" name="STGR_DS_TITLE">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <foreign-key id="204" parent="167" name="FK4GOHBQWQQ8GVI0KSW0HJQ0IES">
      <ColNames>SUBJ_SQ_SUBJECT</ColNames>
      <OnDelete>restrict</OnDelete>
      <OnUpdate>restrict</OnUpdate>
      <RefKeyName>CONSTRAINT_BB</RefKeyName>
      <RefTableName>SUBJECT</RefTableName>
    </foreign-key>
    <index id="205" parent="167" name="PRIMARY_KEY_4">
      <ColNames>STGR_SQ_STUDY_GROUP</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="206" parent="167" name="FK4GOHBQWQQ8GVI0KSW0HJQ0IES_INDEX_4">
      <ColNames>SUBJ_SQ_SUBJECT</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </index>
    <key id="207" parent="167" name="CONSTRAINT_4A">
      <ColNames>STGR_SQ_STUDY_GROUP</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
    </key>
    <column id="208" parent="168" name="APUS_SQ_USER">
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="209" parent="168" name="STGR_SQ_STUDY_GROUP">
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="210" parent="168" name="STGU_SQ_STUDY_GROUP_USER">
      <ColumnKind>generated-stored</ColumnKind>
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <SequenceInline>1</SequenceInline>
      <SequenceDasType>BIGINT|0s</SequenceDasType>
      <SequenceSequenceIdentity>1(1)</SequenceSequenceIdentity>
      <SequenceStartValue>1</SequenceStartValue>
    </column>
    <foreign-key id="211" parent="168" name="FK_APPLICATION_USER_STUDY_GROUP_USER">
      <ColNames>APUS_SQ_USER</ColNames>
      <OnDelete>restrict</OnDelete>
      <OnUpdate>restrict</OnUpdate>
      <RefKeyName>CONSTRAINT_12</RefKeyName>
      <RefTableName>APPLICATION_USER</RefTableName>
    </foreign-key>
    <foreign-key id="212" parent="168" name="FK_STUDY_GROUP_STUDY_GROUP_USER">
      <ColNames>STGR_SQ_STUDY_GROUP</ColNames>
      <OnDelete>restrict</OnDelete>
      <OnUpdate>restrict</OnUpdate>
      <RefKeyName>CONSTRAINT_4A</RefKeyName>
      <RefTableName>STUDY_GROUP</RefTableName>
    </foreign-key>
    <index id="213" parent="168" name="UK_STUDY_GROUP_USER_INDEX_5">
      <ColNames>APUS_SQ_USER
STGR_SQ_STUDY_GROUP</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="214" parent="168" name="PRIMARY_KEY_5">
      <ColNames>STGU_SQ_STUDY_GROUP_USER</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="215" parent="168" name="FK_APPLICATION_USER_STUDY_GROUP_USER_INDEX_5">
      <ColNames>APUS_SQ_USER</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </index>
    <index id="216" parent="168" name="FK_STUDY_GROUP_STUDY_GROUP_USER_INDEX_5">
      <ColNames>STGR_SQ_STUDY_GROUP</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </index>
    <key id="217" parent="168" name="CONSTRAINT_5">
      <ColNames>STGU_SQ_STUDY_GROUP_USER</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
    </key>
    <key id="218" parent="168" name="UK_STUDY_GROUP_USER">
      <ColNames>APUS_SQ_USER
STGR_SQ_STUDY_GROUP</ColNames>
    </key>
    <column id="219" parent="169" name="STGR_SQ_STUDY_GROUP">
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="220" parent="169" name="STWD_SQ_STUDY_GROUP_WEEKDAY">
      <ColumnKind>generated-stored</ColumnKind>
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <SequenceInline>1</SequenceInline>
      <SequenceDasType>BIGINT|0s</SequenceDasType>
      <SequenceSequenceIdentity>1(1)</SequenceSequenceIdentity>
      <SequenceStartValue>1</SequenceStartValue>
    </column>
    <column id="221" parent="169" name="WEEK_SQ_WEEKDAY">
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <foreign-key id="222" parent="169" name="FK_STUDY_GROUP_STUDY_GROUP_WEEKDAY">
      <ColNames>STGR_SQ_STUDY_GROUP</ColNames>
      <OnDelete>restrict</OnDelete>
      <OnUpdate>restrict</OnUpdate>
      <RefKeyName>CONSTRAINT_4A</RefKeyName>
      <RefTableName>STUDY_GROUP</RefTableName>
    </foreign-key>
    <foreign-key id="223" parent="169" name="FK_WEEKDAY_STUDY_GROUP_WEEKDAY">
      <ColNames>WEEK_SQ_WEEKDAY</ColNames>
      <OnDelete>restrict</OnDelete>
      <OnUpdate>restrict</OnUpdate>
      <RefKeyName>CONSTRAINT_7</RefKeyName>
      <RefTableName>WEEKDAY</RefTableName>
    </foreign-key>
    <index id="224" parent="169" name="UK_STUDY_GROUP_WEEKDAY_INDEX_F">
      <ColNames>STGR_SQ_STUDY_GROUP
WEEK_SQ_WEEKDAY</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="225" parent="169" name="PRIMARY_KEY_F">
      <ColNames>STWD_SQ_STUDY_GROUP_WEEKDAY</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="226" parent="169" name="FK_STUDY_GROUP_STUDY_GROUP_WEEKDAY_INDEX_F">
      <ColNames>STGR_SQ_STUDY_GROUP</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </index>
    <index id="227" parent="169" name="FK_WEEKDAY_STUDY_GROUP_WEEKDAY_INDEX_F">
      <ColNames>WEEK_SQ_WEEKDAY</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </index>
    <key id="228" parent="169" name="CONSTRAINT_F">
      <ColNames>STWD_SQ_STUDY_GROUP_WEEKDAY</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
    </key>
    <key id="229" parent="169" name="UK_STUDY_GROUP_WEEKDAY">
      <ColNames>STGR_SQ_STUDY_GROUP
WEEK_SQ_WEEKDAY</ColNames>
    </key>
    <column id="230" parent="170" name="SUBJ_SQ_SUBJECT">
      <ColumnKind>generated-stored</ColumnKind>
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <SequenceInline>1</SequenceInline>
      <SequenceDasType>BIGINT|0s</SequenceDasType>
      <SequenceSequenceIdentity>1(1)</SequenceSequenceIdentity>
      <SequenceStartValue>1</SequenceStartValue>
    </column>
    <column id="231" parent="170" name="SUBJ_CD_CODE">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="232" parent="170" name="SUBJ_DS_NAME">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="233" parent="170" name="PRIMARY_KEY_B">
      <ColNames>SUBJ_SQ_SUBJECT</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <index id="234" parent="170" name="CONSTRAINT_INDEX_B">
      <ColNames>SUBJ_CD_CODE</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <key id="235" parent="170" name="CONSTRAINT_BB">
      <ColNames>SUBJ_SQ_SUBJECT</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
    </key>
    <key id="236" parent="170" name="CONSTRAINT_B">
      <ColNames>SUBJ_CD_CODE</ColNames>
      <NameSurrogate>1</NameSurrogate>
    </key>
    <column id="237" parent="171" name="WEEK_SQ_WEEKDAY">
      <ColumnKind>generated-stored</ColumnKind>
      <DasType>BIGINT|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <SequenceInline>1</SequenceInline>
      <SequenceDasType>BIGINT|0s</SequenceDasType>
      <SequenceSequenceIdentity>1(1)</SequenceSequenceIdentity>
      <SequenceStartValue>1</SequenceStartValue>
    </column>
    <column id="238" parent="171" name="WEEK_DS_NAME">
      <DasType>CHARACTER VARYING(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <index id="239" parent="171" name="PRIMARY_KEY_7">
      <ColNames>WEEK_SQ_WEEKDAY</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Unique>1</Unique>
    </index>
    <key id="240" parent="171" name="CONSTRAINT_7">
      <ColNames>WEEK_SQ_WEEKDAY</ColNames>
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
    </key>
  </database-model>
</dataSource>