.header_toolbar {
  position: fixed;
}

h1.app_name {
  margin-left: 8px;
}

.sidenav_container {
  margin-top: 65px;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.mat_sidenav_content {
  width: 60%;
}

.header_container .sidenav_container {
  flex: 1 0 auto;
}

.example-spacer {
  flex: 1 1 auto;
}

::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{
  width: 100% !important;
}

::ng-deep .p-progress-spinner-circle {
  stroke: #3f51b5 !important;
}

.user-account-button {
  margin-right: 8px;
}

.user-account-button mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
}
