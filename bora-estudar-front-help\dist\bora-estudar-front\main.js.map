{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA8C;AAEM;AACH;AACC;AACoB;AACZ;AACa;AACjB;AACQ;AACN;AACA;AACI;AACI;AACC;AACX;AACE;AACF;AACA;AACU;AACa;AACnB;AACE;AACG;AACT;AACE;AACF;AACM;AACN;AACE;AACO;AACP;AACU;AACN;;AAE5D,MAAMiC,eAAe,GAAG,CACtB/B,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB,CACjB;AAMK,MAAOE,qBAAqB;EAAA,QAAAC,CAAA;qBAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA;UAArBF;EAAqB;EAAA,QAAAG,EAAA;cAHtBrC,0DAAY,EAAKiC,eAAe,EAnC1C/B,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB;EAAA;;;uHAOLE,qBAAqB;IAAAI,OAAA,GAHtBtC,0DAAY,EAnCtBE,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB;IAAAO,OAAA,GA/BhBrC,4DAAa,EACbE,iFAAqB,EACrBC,qEAAe,EACfE,iEAAa,EACbC,yEAAiB,EACjBC,mEAAc,EACdE,uEAAgB,EAChBC,2EAAkB,EAClBE,iEAAa,EACbC,mEAAc,EACdC,kEAAa,EACbC,kEAAa,EACbE,yFAAwB,EACxBD,4EAAkB,EAClBR,oEAAe,EACfU,sEAAe,EACfC,wEAAgB,EAChBC,2EAAiB,EACjBC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,wEAAgB,EAChBb,6EAAkB,EAClBP,mFAAqB,EACrBqB,kEAAa,EACb1B,gEAAa,EACbE,8DAAY,EACZyB,oEAAc,EACdC,2EAAiB,EACjBC,oEAAc,EACdC,8EAAmB,EACnBC,wEAAgB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AClEqC;AAC2B;AACT;AACuB;AACiB;AACL;AACT;AACO;AACA;AACZ;AACY;AACS;;;AAEnH,MAAMqB,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,mBAAmB;EACzBC,SAAS,EAAEV,qHAAyB;EACpCW,WAAW,EAAE,CAACT,0EAAa;CAC5B,EACD;EAAEO,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEb,kFAAc;EAAEc,WAAW,EAAE,CAACT,0EAAa;AAAC,CAAE;AAC1E;AACA;EAAEO,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEd,2FAAiB;EAAEe,WAAW,EAAE,CAACT,0EAAa;AAAC,CAAC;AAC/E;AACA;EAAEO,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEZ,yGAAqB;EAAEa,WAAW,EAAE,CAACT,0EAAa;AAAC,CAAE,EACnF;EACEO,IAAI,EAAE,EAAE;EACRE,WAAW,EAAE,CAACV,sEAAS,CAAC;EACxBW,QAAQ,EAAE,CACR;IAAEH,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEX,0HAA4BA;EAAA,CAAE,EAC3D;IAAEU,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEN,mHAAyBA;EAAA,CAAE,EACxD;IAAEK,IAAI,EAAE,iBAAiB;IAAEC,SAAS,EAAEP,mHAAyBA;EAAA,CAAE,EACjE;IAAEM,IAAI,EAAE,gBAAgB;IAAEC,SAAS,EAAEL,uGAAqBA;EAAA,CAAE,EAC5D;IAAEI,IAAI,EAAE,MAAM;IAAEC,SAAS,EAAEJ,mHAAyBA;EAAA,CAAE,EACtD;IAAEG,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEH,6HAA4BA;EAAA,CAAE;CAEjE,CACF;AAMK,MAAOM,gBAAgB;EAAA,QAAAvB,CAAA;qBAAhBuB,gBAAgB;EAAA;EAAA,QAAAtB,EAAA;UAAhBsB;EAAgB;EAAA,QAAArB,EAAA;cAHjBG,0DAAY,CAACmB,OAAO,CAACN,MAAM,CAAC,EAC5Bb,0DAAY;EAAA;;;uHAEXkB,gBAAgB;IAAApB,OAAA,GAAAsB,0DAAA;IAAArB,OAAA,GAFjBC,0DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;ACzCgE;AACxB;AACG;AAEF;AAC8B;AAC9C;AACQ;AACF;AACuB;AACpB;AAE0B;;;;;;;ICP5EwC,4DAAA,gBAAqD;IAA7BA,wDAAA,mBAAAG,4DAAA;MAAAH,2DAAA,CAAAK,GAAA;MAAA,MAAAC,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAClDT,4DAAA,eAAU;IAAAA,oDAAA,iBAAU;IACtBA,0DADsB,EAAW,EACxB;;;;;;IAaLA,4DAFJ,cAAiC,iBACoD,eACjD;IAAAA,oDAAA,GAA6B;IAIjEA,0DAJiE,EAAO,EAE7D,EAEL;IAIJA,4DADF,wBAA8B,iBACa;IAAnBA,wDAAA,mBAAAY,4DAAA;MAAAZ,2DAAA,CAAAa,GAAA;MAAA,MAAAP,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IACtCd,4DAAA,eAAU;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,WAAM;IAAAA,oDAAA,YAAI;IAEdA,0DAFc,EAAO,EACV,EACA;IAEXA,4DAAA,iBAAgD;IAAxBA,wDAAA,mBAAAe,6DAAA;MAAAf,2DAAA,CAAAa,GAAA;MAAAb,2DAAA;MAAA,MAAAgB,OAAA,GAAAhB,yDAAA;MAAA,OAAAA,yDAAA,CAASgB,OAAA,CAAAE,MAAA,EAAa;IAAA,EAAC;IAC7ClB,4DAAA,gBAAU;IAAAA,oDAAA,YAAI;IAChBA,0DADgB,EAAW,EAClB;;;;;IAjBiBA,uDAAA,EAA8B;IAA9BA,wDAAA,sBAAAqB,WAAA,CAA8B;IACpBrB,uDAAA,GAA6B;IAA7BA,+DAAA,EAAAM,MAAA,CAAAiB,IAAA,kBAAAjB,MAAA,CAAAiB,IAAA,CAAAC,IAAA,oBAA6B;;;;;;IAyB3DxB,4DAAA,YAAwD;IAAlBA,wDAAA,mBAAAyB,sEAAA;MAAAzB,2DAAA,CAAA0B,GAAA;MAAA,MAAApB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAqB,KAAA,EAAO;IAAA,EAAC;IAAC3B,oDAAA,WAAI;IAAAA,0DAAA,EAAI;IAChEA,4DAAA,YAAwD;IAAlBA,wDAAA,mBAAA4B,sEAAA;MAAA5B,2DAAA,CAAA0B,GAAA;MAAA,MAAApB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAqB,KAAA,EAAO;IAAA,EAAC;IAAC3B,oDAAA,mBAAY;IAAAA,0DAAA,EAAI;IACxEA,4DAAA,YAAgE;IAAlBA,wDAAA,mBAAA6B,sEAAA;MAAA7B,2DAAA,CAAA0B,GAAA;MAAA,MAAApB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAqB,KAAA,EAAO;IAAA,EAAC;IAAC3B,oDAAA,kBAAW;IAAAA,0DAAA,EAAI;;;;;;IAJnFA,4DAAA,mBAAc;IACZA,wDAAA,IAAA+B,kDAAA,OAAsC;IAKtC/B,4DAAA,YAAoC;IAAnBA,wDAAA,mBAAAgC,wDAAA;MAAAhC,2DAAA,CAAAiC,GAAA;MAAA,MAAA3B,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAACd,oDAAA,WAAI;IAC1CA,0DAD0C,EAAI,EAC/B;;;;IANbA,uDAAA,EAIC;IAJDA,2DAAA,IAAAM,MAAA,CAAA6B,MAAA,CAAAC,GAAA,2BAIC;;;;;IAKDpC,4DADF,mBAAc,YACyB;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAI;IAC9CA,4DAAA,YAAwC;IAAAA,oDAAA,eAAQ;IAClDA,0DADkD,EAAI,EACvC;;;ADpBnB,MAAOqC,YAAY;EAcvBC,YACUC,QAAkB;IAAlB,KAAAA,QAAQ,GAARA,QAAQ;IAdlB,KAAAC,OAAO,GAAW,kBAAkB;IACpC,KAAAC,KAAK,GAAG,oBAAoB;IAC5B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAnB,IAAI,GAAqCoB,SAAS;IAClD,KAAAC,YAAY,GAAG,KAAK;IAGZ,KAAAC,QAAQ,GAAG/D,qDAAM,CAACgB,oEAAW,CAAC;IAC9B,KAAAgD,WAAW,GAAGhE,qDAAM,CAACC,yEAAW,CAAC;IAClC,KAAAoD,MAAM,GAAGrD,qDAAM,CAACE,mDAAM,CAAC;IACtB,KAAA+D,GAAG,GAAGjE,qDAAM,CAACD,4DAAiB,CAAC;IAC/B,KAAAmE,iBAAiB,GAAGlE,qDAAM,CAACiB,6FAAwB,CAAC;EAIzD;EAEHkD,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACJ,UAAU,EAAE,CAACQ,SAAS,CAAER,UAAU,IAAI;MACrD,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAIA,UAAU,EAAE;QACd,IAAI,CAACS,OAAO,EAAE;MAChB;MACA,IAAI,CAACJ,GAAG,CAACK,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACjB,MAAM,CAACkB,MAAM,CAACH,SAAS,CAAC,MAAK;MAChC,IAAI,CAACN,YAAY,GAAG,IAAI,CAACT,MAAM,CAACC,GAAG,KAAK,SAAS;MAEjD,IAAG,IAAI,CAACD,MAAM,CAACC,GAAG,KAAK,SAAS,EAAC;QAC/B,IAAI,CAACI,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACL,MAAM,CAACC,GAAG,KAAK,iBAAiB,EAAC;QAC9C,IAAI,CAACI,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACL,MAAM,CAACC,GAAG,CAACkB,UAAU,CAAC,OAAO,CAAC,EAAC;QAC5C,IAAI,CAACd,OAAO,GAAG,QAAQ;MACzB,CAAC,MAAM,IAAG,IAAI,CAACL,MAAM,CAACC,GAAG,CAACkB,UAAU,CAAC,SAAS,CAAC,EAAC;QAC9C,IAAI,CAACd,OAAO,GAAG,UAAU;MAC3B,CAAC,MAAM;QACL,IAAI,CAACA,OAAO,GAAG,kBAAkB;MACnC;IACF,CAAC,CAAC;EACJ;EAEO1B,MAAMA,CAAA;IACX,IAAI,CAACgC,WAAW,CAAChC,MAAM,EAAE,CAACoC,SAAS,CAAC;MAClCK,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACrB,MAAM,CAACwB,aAAa,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAChC,KAAK,EAAE;QAEZ,IAAI,CAACkB,QAAQ,CAACe,IAAI,CAChB,2BAA2B,EAC3B,GAAG,EACH;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEOX,OAAOA,CAAA;IACZ,IAAI,CAACL,WAAW,CAACK,OAAO,EAAE,CAACD,SAAS,CAAC;MACnCK,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACjC,IAAI,GAAGiC,IAAI;QAChB,IAAI,CAACT,GAAG,CAACK,aAAa,EAAE;MAC1B,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEArD,gBAAgBA,CAAA;IACd,IAAI,CAAC0B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEApC,KAAKA,CAAA;IACH,IAAI,IAAI,CAACqC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACrC,KAAK,EAAE;IACtB;EACF;EAAC,QAAAxE,CAAA;qBA9FUkF,YAAY,EAAArC,+DAAA,CAAApB,qDAAA;EAAA;EAAA,QAAAxB,EAAA;UAAZiF,YAAY;IAAA8B,SAAA;IAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCjCrBtE,4DAFJ,WAAM,aACuB,qBAC4C;QAEnEA,wDAAA,IAAAwE,mCAAA,oBAAgF;QAMhFxE,uDAAA,cAA4B;QAE5BA,4DAAA,YAAqB;QAAAA,oDAAA,GAAW;QAAAA,0DAAA,EAAK;QAErCA,uDAAA,cAA4B;QAE5BA,wDAAA,IAAA0E,mCAAA,QAA2B;QAsB7B1E,0DAAA,EAAc;QAGZA,4DADF,+BAAiD,yBACY;QAUvDA,wDATF,KAAA2E,oCAAA,uBAA2B,KAAAC,oCAAA,OASlB;QAMX5E,0DAAA,EAAc;QAGZA,4DADF,2BAAqB,qBACJ;QAAAA,uDAAA,YAAa;QAIpCA,0DAJoC,EAAgB,EACxB,EACA,EACpB,EACD;;;QA5DDA,uDAAA,GAIC;QAJDA,2DAAA,IAAAuE,GAAA,CAAA3B,YAAA,IAAA2B,GAAA,CAAA7B,UAAA,aAAA6B,GAAA,CAAApC,MAAA,CAAAC,GAAA,2BAIC;QAIoBpC,uDAAA,GAAW;QAAXA,+DAAA,CAAAuE,GAAA,CAAA/B,OAAA,CAAW;QAIhCxC,uDAAA,GAqBC;QArBDA,2DAAA,IAAAuE,GAAA,CAAA7B,UAAA,mBAqBC;QAKC1C,uDAAA,GAcC;QAdDA,2DAAA,KAAAuE,GAAA,CAAA7B,UAAA,oBAcC;;;mBDpCDhD,iEAAU,EACVD,mEAAa,EACbD,2DAAO,EACPH,0EAAmB,EACnBC,iEAAU,EACVH,+DAAU,EACVC,gEAAW,EACXH,uDAAU,EACVM,wEAAiB,EACjBL,yDAAY,EACZS,4DAAO,EACPC,gEAAW,EACXC,mEAAc;IAAAgF,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;AEtBhB,MAAOlH,qBAAqB;EAGhC2E,YAAoBwC,KAAqB,EAAUhC,WAAwB;IAAvD,KAAAgC,KAAK,GAALA,KAAK;IAA0B,KAAAhC,WAAW,GAAXA,WAAW;IAF9D,KAAAiC,OAAO,GAAW,EAAE;EAE0D;EAE9E9B,QAAQA,CAAA;IAEN,IAAI,CAAC6B,KAAK,CAACE,WAAW,CAAC9B,SAAS,CAAC+B,MAAM,IAAG;MACxC,MAAMC,KAAK,GAAGD,MAAM,CAAC,OAAO,CAAC;MAE7B,IAAI,CAACnC,WAAW,CAACqC,YAAY,CAACD,KAAK,CAAC,CAAChC,SAAS,CAAC;QAC7CK,IAAI,EAAGC,IAAI,IAAI;UACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;UACjB,IAAI,CAACuB,OAAO,GAAGvB,IAAI;QACrB,CAAC;QACDM,KAAK,EAAGsB,GAAG,IAAI;UACb3B,OAAO,CAACK,KAAK,CAACsB,GAAG,CAAC;UAClB,IAAI,CAACL,OAAO,GAAG,4DAA4D;QAC7E;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAAC,QAAA5H,CAAA;qBArBUQ,qBAAqB,EAAAqC,+DAAA,CAAApB,2DAAA,GAAAoB,+DAAA,CAAAsF,yEAAA;EAAA;EAAA,QAAAlI,EAAA;UAArBO,qBAAqB;IAAAwG,SAAA;IAAAoB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,+BAAAvB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVlCtE,4DAAA,QAAG;QAAAA,oDAAA,2BAAoB;QAAAA,0DAAA,EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;ACA+B;AACa;AAC+B;AAEjD;AACW;AACb;AACmB;AACS;;;AAqBzE,MAAOtC,cAAc;EAMzB;EACA4E,YAAA;IANQ,KAAAH,MAAM,GAAGrD,qDAAM,CAACE,mDAAM,CAAC;IACvB,KAAA0H,EAAE,GAAG5H,qDAAM,CAACgH,uDAAW,CAAC;IACxB,KAAAhD,WAAW,GAAGhE,qDAAM,CAACC,yEAAW,CAAC;IAE/B,KAAA4H,YAAY,GAAG,EAAE;EAEZ;EAEf1D,QAAQA,CAAA;IACN;IACA;IACA;IAEA;IACA;IACA;IAEA,IAAI,CAAC2D,SAAS,GAAG,IAAI,CAACF,EAAE,CAACG,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACf,sDAAU,CAACgB,QAAQ,EAAEhB,sDAAU,CAACe,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjB,sDAAU,CAACgB,QAAQ,CAAC;KACrC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;IAE3B,MAAMC,IAAI,GAAe;MACvBL,KAAK,EAAE,IAAI,CAACF,SAAS,CAACQ,QAAQ,CAAC,OAAO,CAAC,CAACC,KAAK;MAC7CL,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAACQ,QAAQ,CAAC,UAAU,CAAC,CAACC;KAC/C;IAED,IAAI,CAACvE,WAAW,CAACwE,KAAK,CAACH,IAAI,CAAC,CAACjE,SAAS,CAAC;MACrCK,IAAI,EAAGC,IAAS,IAAI;QAClBC,OAAO,CAACK,KAAK,CAAC,OAAO,EAAEN,IAAI,CAAC;QAC5B,MAAM+D,SAAS,GAAG/D,IAAI,CAACgE,EAAE,CAACC,QAAQ,EAAE;QACpCC,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEJ,SAAS,CAAC;QAC5C,IAAIK,kBAAkB,GAAGpE,IAAI,CAACoE,kBAAkB;QAChDnE,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAE8D,kBAAkB,CAAC;QACvD;QAEA,IAAGA,kBAAkB,KAAK,IAAI,EAAC;UAC7B,IAAI,CAACzF,MAAM,CAACwB,aAAa,CAAC,SAAS,CAAC;QACtC,CAAC,MAAM;UACL,IAAI,CAACxB,MAAM,CAACwB,aAAa,CAAC,YAAY,CAAC;QACzC;MACF,CAAC;MACDG,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;QAClB,IAAI,CAAC6C,YAAY,GAAG7C,KAAK,CAACA,KAAK,CAACiB,OAAO;MACzC;KACD,CAAC;EACJ;EAAC,QAAA5H,CAAA;qBApDUO,cAAc;EAAA;EAAA,QAAAN,EAAA;UAAdM,cAAc;IAAAyG,SAAA;IAAAoB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAkC,wBAAAxD,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3BvBtE,4DAFJ,aAAuB,eACX,qBACQ;QAACA,oDAAA,4BAAoB;QAAAA,0DAAA,EAAiB;QAGpDA,4DADF,uBAAkB,cACyD;QAAxBA,wDAAA,sBAAA+H,iDAAA;UAAA,OAAYxD,GAAA,CAAA0C,QAAA,EAAU;QAAA,EAAC;QAEpEjH,4DADF,wBAAqC,gBACxB;QAAAA,oDAAA,YAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,eAAgE;QAClEA,0DAAA,EAAiB;QAGfA,4DADF,yBAAqC,iBACxB;QAAAA,oDAAA,aAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,gBAAsE;QACxEA,0DAAA,EAAiB;QAEjBA,4DAAA,iBAKC;QACCA,oDAAA,eACF;QAAAA,0DAAA,EAAS;QAETA,4DAAA,cAAqB;QACnBA,uDAAA,eAA4B;QAC5BA,4DAAA,eAAiB;QAAAA,oDAAA,UAAE;QAAAA,0DAAA,EAAO;QAC1BA,uDAAA,eAA4B;QAC9BA,0DAAA,EAAM;QAENA,4DAAA,YAKG;QAAAA,oDAAA,mBAAW;QAAAA,0DAAA,EACb;QAEDA,4DAAA,aAKG;QAAAA,oDAAA,yBAAiB;QAK5BA,0DAL4B,EACnB,EACI,EACU,EACV,EACP;;;QA5CyBA,uDAAA,GAAuB;QAAvBA,wDAAA,cAAAuE,GAAA,CAAAqC,SAAA,CAAuB;;;mBDW9CL,2DAAO,EACPC,gEAAY,EACZC,kEAAc,EACdT,uDAAW,EAAApH,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,6DAAA,EACXqH,+DAAmB,EAAArH,8DAAA,EAAAA,2DAAA,EACnByH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACTC,+DAAS,EACTlH,uDAAU;IAAA4F,MAAA;EAAA;;;;;;;;;;;;;;;;;AEjBZ,MAAOhH,yBAAyB;EAAA,QAAAV,CAAA;qBAAzBU,yBAAyB;EAAA;EAAA,QAAAT,EAAA;UAAzBS,yBAAyB;IAAAsG,SAAA;IAAAoB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAA2C,mCAAAjE,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTtCtE,4DAAA,QAAG;QAAAA,oDAAA,+BAAwB;QAAAA,0DAAA,EAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACA2B;AACa;AAC+B;AAE5C;AACL;AACW;AACb;AACmB;AACS;;;AAqBzE,MAAOvC,iBAAiB;EAQ5B6E,YAAA;IAPQ,KAAAH,MAAM,GAAGrD,qDAAM,CAACE,mDAAM,CAAC;IACvB,KAAA0H,EAAE,GAAG5H,qDAAM,CAACgH,uDAAW,CAAC;IACxB,KAAAhD,WAAW,GAAGhE,qDAAM,CAACC,yEAAW,CAAC;IACjC,KAAA8D,QAAQ,GAAG/D,qDAAM,CAACgB,oEAAW,CAAC;IAE5B,KAAA6G,YAAY,GAAG,EAAE;IAC3B,KAAAjE,UAAU,GAAG,KAAK;EACH;EAEfO,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACJ,UAAU,EAAE,CAACQ,SAAS,CAAEsF,QAAQ,IAAI;MACnD,IAAI,CAAC9F,UAAU,GAAG8F,QAAQ;IAC5B,CAAC,CAAC;IAEF;IACA;IACA;IAEA,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC/B,EAAE,CAACG,KAAK,CAAC;MAC9BrF,IAAI,EAAE,CAAC,EAAE,EAAE,CAACuE,sDAAU,CAACgB,QAAQ,CAAC,CAAC;MACjCD,KAAK,EAAE,CAAC,EAAE,EAAE,CAACf,sDAAU,CAACgB,QAAQ,EAAEhB,sDAAU,CAACe,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACjB,sDAAU,CAACgB,QAAQ,CAAC;KACrC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACwB,UAAU,CAACvB,KAAK,EAAE;IAE5BzD,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC+E,UAAU,CAACpB,KAAK,CAAC;IAE5C,MAAMF,IAAI,GAAe;MACvB3F,IAAI,EAAE,IAAI,CAACiH,UAAU,EAAEpB,KAAK,CAAC7F,IAAI;MACjCsF,KAAK,EAAE,IAAI,CAAC2B,UAAU,EAAEpB,KAAK,CAACP,KAAK;MACnCE,QAAQ,EAAE,IAAI,CAACyB,UAAU,EAAEpB,KAAK,CAACL;KAClC;IAED,IAAI,CAAClE,WAAW,CAAC4F,QAAQ,CAACvB,IAAI,CAAC,CAACjE,SAAS,CAAC;MACxCK,IAAI,EAAEA,CAAA,KAAM,IAAI,CAACoF,SAAS,EAAE;MAC5B7E,KAAK,EAAEA,CAAA,KAAM,IAAI,CAAC8E,OAAO;KAC1B,CAAC;EACJ;EAEQD,SAASA,CAAA;IACf,IAAI,CAAC9F,QAAQ,CAACe,IAAI,CAChB,gFAAgF,EAChF,GAAG,EACH;MAAEC,QAAQ,EAAE;IAAI,CAAE,CACnB;IACD,IAAI,CAAC4E,UAAU,CAACI,KAAK,EAAE;IACvB,IAAI,CAAC1G,MAAM,CAACwB,aAAa,CAAC,QAAQ,CAAC;EACrC;EAEQiF,OAAOA,CAAA;IACb,IAAI,CAAC/F,QAAQ,CAACe,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAE,CAAC;EACpE;EAEQiF,UAAUA,CAAA;IAChBC,MAAM,CAACxG,QAAQ,CAACyG,MAAM,EAAE;EAC1B;EAAC,QAAA7L,CAAA;qBA3DUM,iBAAiB;EAAA;EAAA,QAAAL,EAAA;UAAjBK,iBAAiB;IAAA0G,SAAA;IAAAoB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAqD,2BAAA3E,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC5B1BtE,4DAFJ,aAAuB,eACX,qBACQ;QAACA,oDAAA,4BAAoB;QAAAA,0DAAA,EAAiB;QAGpDA,4DADF,uBAAkB,cACuC;QAAxBA,wDAAA,sBAAAkJ,oDAAA;UAAA,OAAY3E,GAAA,CAAA0C,QAAA,EAAU;QAAA,EAAC;QAElDjH,4DADF,wBAAqC,gBACxB;QAAAA,oDAAA,WAAI;QAAAA,0DAAA,EAAY;QAC3BA,uDAAA,eAA8D;QAChEA,0DAAA,EAAiB;QAGfA,4DADF,yBAAqC,iBACxB;QAAAA,oDAAA,aAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,gBAAgE;QAClEA,0DAAA,EAAiB;QAGfA,4DADF,yBAAqC,iBACxB;QAAAA,oDAAA,aAAK;QAAAA,0DAAA,EAAY;QAC5BA,uDAAA,gBAAsE;QACxEA,0DAAA,EAAiB;QAEjBA,4DAAA,iBAKC;QACCA,oDAAA,mBACF;QAAAA,0DAAA,EAAS;QAETA,4DAAA,YAKG;QAAAA,oDAAA,eACH;QAIRA,0DAJQ,EAAI,EACC,EACU,EACV,EACP;;;QAnCMA,uDAAA,GAAwB;QAAxBA,wDAAA,cAAAuE,GAAA,CAAAkE,UAAA,CAAwB;;;mBDY5BlC,2DAAO,EACPC,gEAAY,EACZC,kEAAc,EACdT,uDAAW,EAAApH,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,6DAAA,EACXqH,+DAAmB,EAAArH,8DAAA,EAAAA,2DAAA,EACnByH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACTC,+DAAS,EACTlH,uDAAU;IAAA4F,MAAA;EAAA;;;;;;;;;;;;;;;;;;AE1B6F;;AAIzG,MAAOuE,sBAAsB;EACjCC,SAASA,CACPC,GAAqB,EACrB/F,IAAiB;IAEjB+F,GAAG,GAAGA,GAAG,CAACC,KAAK,CAAC;MACdC,eAAe,EAAE;KAClB,CAAC;IAEF,OAAOjG,IAAI,CAACkG,MAAM,CAACH,GAAG,CAAC;EACzB;EAAC,QAAAnM,CAAA;qBAVUiM,sBAAsB;EAAA;EAAA,QAAAhM,EAAA;WAAtBgM,sBAAsB;IAAAM,OAAA,EAAtBN,sBAAsB,CAAAO;EAAA;;AAa5B,MAAMC,wBAAwB,GAAG,CACtC;EAAEC,OAAO,EAAEV,mEAAiB;EAAEW,QAAQ,EAAEV,sBAAsB;EAAEW,KAAK,EAAE;AAAI,CAAE,CAC9E;;;;;;;;;;;;;;;;;;;ACpB8D;AACZ;AACA;AAIA;;AAGnD,MAAMM,QAAQ,GAAG,EAAE;AAEnB,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,IAAIN,6DAAW,CAAC;IAAE,cAAc,EAAE;EAAkB,CAAE;CAChE;AAKK,MAAOlL,WAAW;EAHxBuD,YAAA;IAImB,KAAAkI,IAAI,GAAG1L,qDAAM,CAACkL,4DAAU,CAAC;IACzB,KAAAS,cAAc,GAAG3L,qDAAM,CAACsL,4DAAc,CAAC;;EAExD9C,KAAKA,CAACH,IAAgB;IACpB,OAAO,IAAI,CAACqD,IAAI,CACbE,IAAI,CAAuBL,QAAQ,CAACM,MAAM,CAAC,SAAS,CAAC,EAAExD,IAAI,EAAEmD,WAAW,CAAC,CACzEM,IAAI,CACHT,yCAAG,CAAE5I,IAAI,IAAI;MACX,IAAI,CAACkJ,cAAc,CAACI,QAAQ,CAACtJ,IAAI,CAAC;IACpC,CAAC,CAAC,EACF2I,gDAAU,CAAEpG,KAAK,IAAI;MACnBL,OAAO,CAACC,GAAG,CAAC,mBAAmBI,KAAK,CAACiB,OAAO,EAAE,CAAC;MAC/C,IAAI,CAAC0F,cAAc,CAACK,KAAK,EAAE;MAC3B,MAAMhH,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEA4E,QAAQA,CAACvB,IAAgB;IACvB,OAAO,IAAI,CAACqD,IAAI,CACbE,IAAI,CAAuBL,QAAQ,CAACM,MAAM,CAAC,SAAS,CAAC,EAAExD,IAAI,EAAEmD,WAAW,CAAC,CACzEM,IAAI,CACHV,gDAAU,CAAEpG,KAAK,IAAI;MACnBL,OAAO,CAACC,GAAG,CAAC,mBAAmBI,KAAK,CAACiB,OAAO,EAAE,CAAC;MAC/C,IAAI,CAAC0F,cAAc,CAACK,KAAK,EAAE;MAC3B,MAAMhH,KAAK;IACb,CAAC,CAAC,CACH;EACL;EAEAqB,YAAYA,CAACD,KAAa;IACxB,OAAO,IAAI,CAACsF,IAAI,CAACO,GAAG,CAClBV,QAAQ,CAACM,MAAM,CAAC,kBAAkBzF,KAAK,EAAE,CAAC,EAC1CoF,WAAW,CACZ;EACH;EAEAxJ,MAAMA,CAAA;IACJ,OAAO,IAAI,CAAC0J,IAAI,CAACE,IAAI,CAACL,QAAQ,GAAG,UAAU,EAAE,EAAE,EAAEC,WAAW,CAAC,CAACM,IAAI,CAChET,yCAAG,CAAC,MAAK;MACP,IAAI,CAACM,cAAc,CAACK,KAAK,EAAE;IAC7B,CAAC,CAAC,EACFZ,gDAAU,CAAEpG,KAAK,IAAI;MACnBL,OAAO,CAACC,GAAG,CAAC,mBAAmBI,KAAK,CAACiB,OAAO,EAAE,CAAC;MAC/C,IAAI,CAAC0F,cAAc,CAACK,KAAK,EAAE;MAC3B,MAAMhH,KAAK;IACb,CAAC,CAAC,CACH;EACH;EAEAX,OAAOA,CAAA;IACL,OAAO,IAAI,CAACsH,cAAc,CAACtH,OAAO,EAAE;EACtC;EAEAT,UAAUA,CAAA;IACR,OAAO,IAAI,CAAC+H,cAAc,CAAC/H,UAAU,EAAE;EACzC;EAAC,QAAAvF,CAAA;qBAzDU4B,WAAW;EAAA;EAAA,QAAA3B,EAAA;WAAX2B,WAAW;IAAA2K,OAAA,EAAX3K,WAAW,CAAA4K,IAAA;IAAAqB,UAAA,EAFV;EAAM;;;;;;;;;;;;;;;;;;ACf+B;AAEzB;;AAE1B,MAAMG,WAAW,GAAG,aAAa;AAK3B,MAAOf,cAAc;EAMzB9H,YAAA;IALQ,KAAA8I,UAAU,GAAG,IAAIH,iDAAe,CACtCtI,SAAS,CACV;IACO,KAAA0I,aAAa,GAAG,IAAIJ,iDAAe,CAAU,KAAK,CAAC;IAGzD,IAAI,CAACK,mBAAmB,EAAE;EAC5B;EAEAR,KAAKA,CAAA;IACHpD,YAAY,CAACoD,KAAK,EAAE;IACpB,IAAI,CAACM,UAAU,CAAC7H,IAAI,CAACZ,SAAS,CAAC;IAC/B,IAAI,CAAC0I,aAAa,CAAC9H,IAAI,CAAC,KAAK,CAAC;EAChC;EAEOsH,QAAQA,CAACtJ,IAA0B;IACxCmG,YAAY,CAAC6D,UAAU,CAACJ,WAAW,CAAC;IACpCzD,YAAY,CAACC,OAAO,CAACwD,WAAW,EAAEK,IAAI,CAACC,SAAS,CAAClK,IAAI,CAAC,CAAC;IACvD,IAAI,CAAC6J,UAAU,CAAC7H,IAAI,CAAChC,IAAI,CAAC;IAC1B,IAAI,CAAC8J,aAAa,CAAC9H,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEOJ,OAAOA,CAAA;IACZ,MAAMuI,gBAAgB,GAAGhE,YAAY,CAACiE,OAAO,CAACR,WAAW,CAAC;IAC1D,IAAI,CAACO,gBAAgB,EAAE;MACrB,MAAM,IAAIE,KAAK,CAAC,8CAA8C,CAAC;IACjE;IAEA,MAAMR,UAAU,GAAyBI,IAAI,CAACK,KAAK,CAACH,gBAAgB,CAAC;IACrE,OAAOR,wCAAE,CAACE,UAAU,CAAC;EACvB;EAEO1I,UAAUA,CAAA;IACf,OAAO,IAAI,CAAC2I,aAAa,CAACS,YAAY,EAAE;EAC1C;EAEQR,mBAAmBA,CAAA;IACzB,IAAI,CAAC5D,YAAY,CAACiE,OAAO,CAACR,WAAW,CAAC,EAAE;MACtC;IACF;IAEA,MAAMY,aAAa,GAAGrE,YAAY,CAACiE,OAAO,CAACR,WAAW,CAAE;IACxD,MAAMa,aAAa,GAAGR,IAAI,CAACK,KAAK,CAACE,aAAa,CAAC;IAE/C,IAAI,CAACX,UAAU,CAAC7H,IAAI,CAACyI,aAAa,CAAC;IACnC,IAAI,CAACX,aAAa,CAAC9H,IAAI,CAAC,IAAI,CAAC;EAC/B;EAAC,QAAApG,CAAA;qBA/CUiN,cAAc;EAAA;EAAA,QAAAhN,EAAA;WAAdgN,cAAc;IAAAV,OAAA,EAAdU,cAAc,CAAAT,IAAA;IAAAqB,UAAA,EAFb;EAAM;;;;;;;;;;;;;;;;;;;;ACFK;AACc;AACY;AAEnD;;;;;;;;AAQO,MAAMjN,aAAa,GAAkBA,CAC1CkO,MAA8B,EAC9BC,MAA2B,KACN;EACrBzI,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAC1D,MAAMZ,WAAW,GAAGhE,qDAAM,CAACC,2DAAW,CAAC;EACvC,MAAMoD,MAAM,GAAGrD,qDAAM,CAACE,mDAAM,CAAC;EAC7B,IAAI0D,UAAU,GAAG,KAAK;EAEtBI,WAAW,CAACJ,UAAU,EAAE,CAACQ,SAAS,CAAEiJ,CAAC,IAAI;IACvCzJ,UAAU,GAAGyJ,CAAC;EAChB,CAAC,CAAC;EAEF,IAAIzJ,UAAU,EAAE;IACdP,MAAM,CAACwB,aAAa,CAAC,SAAS,CAAC;IAC/B,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AAED;;;;;;;;AAQO,MAAM7F,SAAS,GAAkBA,CACtCmO,MAA8B,EAC9BC,MAA2B,KACN;EACrBzI,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;EACtD,MAAMZ,WAAW,GAAGhE,qDAAM,CAACC,2DAAW,CAAC;EACvC,MAAMoD,MAAM,GAAGrD,qDAAM,CAACE,mDAAM,CAAC;EAC7B,IAAI0D,UAAU,GAAG,KAAK;EAEtBI,WAAW,CAACJ,UAAU,EAAE,CAACQ,SAAS,CAAEiJ,CAAC,IAAI;IACvCzJ,UAAU,GAAGyJ,CAAC;EAChB,CAAC,CAAC;EAEF,IAAI,CAACzJ,UAAU,EAAE;IACfP,MAAM,CAACwB,aAAa,CAAC,QAAQ,CAAC;IAC9B,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb,CAAC;AAED;;;;;;;;AAQO,MAAMyI,qBAAqB,GAAkBA,CAClDH,MAA8B,EAC9BC,MAA2B,KACN;EACrBzI,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;EAClE,MAAMZ,WAAW,GAAGhE,qDAAM,CAACC,2DAAW,CAAC;EACvC,MAAMoD,MAAM,GAAGrD,qDAAM,CAACE,mDAAM,CAAC;EAE7B,MAAMqN,MAAM,GAAG3E,YAAY,CAACiE,OAAO,CAAC,aAAa,CAAC;EAElD,IAAIU,MAAM,EAAE;IACV,MAAMjB,UAAU,GAAGI,IAAI,CAACK,KAAK,CAACQ,MAAM,CAAC;IACrC,MAAMzE,kBAAkB,GAAGwD,UAAU,CAACxD,kBAAkB;IACxD,IAAG,CAACA,kBAAkB,EAAC;MACrBzF,MAAM,CAACwB,aAAa,CAAC,YAAY,CAAC;IACpC;EACF,CAAC,MAAM;IACLF,OAAO,CAACK,KAAK,CAAC,wCAAwC,CAAC;EACzD;EACA;EACA,OAAO,IAAI;AACb,CAAC;;;;;;;;;;;;;;;;;;;;AC/FgE;AACW;AAC3B;AACQ;AACF;;;;ICY/C9D,4DAAA,WAAgC;IAAAA,oDAAA,GAAS;IAAAA,0DAAA,EAAI;;;;IAAbA,uDAAA,EAAS;IAATA,+DAAA,CAAAsM,MAAA,CAAS;;;ADM3C,MAAOC,eAAe;EAhB5BjK,YAAA;IAkBE,KAAAkK,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACxP,CAAC,EAAEyP,CAAC,KAAK,YAAYA,CAAC,GAAG,CAAC,EAAE,CAAC;;EAYtE,QAAAzP,CAAA;qBAdYoP,eAAe;EAAA;EAAA,QAAAnP,EAAA;UAAfmP,eAAe;IAAApI,SAAA;IAAAoB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAiH,yBAAAvI,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCrBxBtE,4DAFJ,aAA8B,qBACyC,gBACnB;QAAxBA,wDAAA,mBAAA8M,iDAAA;UAAA9M,2DAAA,CAAAK,GAAA;UAAA,MAAA0M,OAAA,GAAA/M,yDAAA;UAAA,OAAAA,yDAAA,CAAS+M,OAAA,CAAA7L,MAAA,EAAa;QAAA,EAAC;QAC7ClB,4DAAA,eAAU;QAAAA,oDAAA,WAAI;QAChBA,0DADgB,EAAW,EAClB;QACTA,uDAAA,cAA4B;QAC5BA,4DAAA,YAAqB;QAAAA,oDAAA,uBAAgB;QACvCA,0DADuC,EAAK,EAC9B;QAQVA,4DANJ,+BAAiD,wBAK9C,oBACe;QACZA,8DAAA,KAAAiN,+BAAA,gBAAAjN,uEAAA,CAEC;QAITA,0DAHM,EAAe,EACH,EACQ,EACpB;;;QANEA,uDAAA,IAEC;QAFDA,wDAAA,CAAAuE,GAAA,CAAAiI,SAAA,CAEC;;;mBDJD9M,iEAAU,EACVD,mEAAa,EACbD,2DAAO,EACPH,0EAAmB,EACnBC,iEAAU,EACVH,8DAAU,EACVC,+DAAW;IAAAyF,MAAA;EAAA;;;;;;;;;;;;;;;;;;;AEnB4B;AACwB;AACJ;;AAM7D,MAAOuI,YAAY;EAAA,QAAAjQ,CAAA;qBAAZiQ,YAAY;EAAA;EAAA,QAAAhQ,EAAA;UAAZgQ;EAAY;EAAA,QAAA/P,EAAA;cAHXrC,yDAAY,EAAEkC,2EAAqB,EAAEqP,gFAAe;EAAA;;;sHAGrDa,YAAY;IAAA9P,OAAA,GAHXtC,yDAAY,EAAEkC,2EAAqB,EAAEqP,gFAAe;IAAAhP,OAAA,GACpDgP,gFAAe;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACJW;AAC+B;AAClB;AACuB;AACN;AACrB;AACE;AACI;AACsD;;;;;;;;;;;;ICLrGvM,4DAAA,qBAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAFsCA,wDAAA,UAAAwN,SAAA,CAAAC,IAAA,WAAAD,SAAA,CAAA/K,KAAA,CAA4C;IAC7FzC,uDAAA,EACF;IADEA,gEAAA,OAAAwN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAA/K,KAAA,MACF;;;;;IAgDJzC,4DAAA,UAAoC;IAClCA,uDAAA,sCAAiF;IACnFA,0DAAA,EAAM;;;;IADyBA,uDAAA,EAAqB;IAArBA,wDAAA,eAAA2N,SAAA,CAAqB;;;ADxBlD,MAAOzP,qBAAqB;EAShCoE,YACUS,GAAsB,EACvB6K,OAA0B,EACzBzL,MAAc;IAFd,KAAAY,GAAG,GAAHA,GAAG;IACJ,KAAA6K,OAAO,GAAPA,OAAO;IACN,KAAAzL,MAAM,GAANA,MAAM;IAXhB,KAAA0L,OAAO,GAAU,EAAE;IAEnB,KAAAC,YAAY,GAAgB,IAAIC,GAAG,EAAE;IACrC,KAAAC,YAAY,GAAW,EAAE;EAStB;EAEH/K,QAAQA,CAAA;IACN,MAAMsE,SAAS,GAAGG,YAAY,CAACiE,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMnE,EAAE,GAAGyG,MAAM,CAAC1G,SAAS,CAAC;IAE5B,IAAI,CAACqG,OAAO,CAACM,kBAAkB,CAAC1G,EAAE,CAAC,CAACtE,SAAS,CAAEiL,KAAK,IAAI;MACtD1K,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEyK,KAAK,CAAC;MACvC,IAAI,CAACP,OAAO,CAACQ,aAAa,GAAGD,KAAK;MAClC,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACrH,KAAK,CAACsH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAACQ,aAAa,CAACG,MAAM,CAACK,MAAM,IAC7DA,MAAM,CAACnM,KAAK,CAACkM,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CACpG;EACH;EAEAM,YAAYA,CAAA;IACV,MAAMN,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACrH,KAAK,CAACsH,WAAW,EAAE;IAEhE;IACA,MAAM,CAACI,UAAU,EAAEC,WAAW,CAAC,GAAGR,WAAW,CAACS,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAAC;IAEnF,MAAMb,MAAM,GAAG,IAAI,CAACX,OAAO,CAACQ,aAAa,EAAEG,MAAM,CAACK,MAAM,IACtD,IAAI,CAACS,iBAAiB,CAACT,MAAM,CAAC,IAC9B,IAAI,CAACU,YAAY,CAACV,MAAM,CAAC,KACxBA,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACE,UAAU,CAAC,IAC9CH,MAAM,CAACnM,KAAK,CAACkM,WAAW,EAAE,CAACE,QAAQ,CAACG,WAAW,CAAC,CAAC,CACnD,IAAI,EAAE;IAEP,IAAI,CAACnB,OAAO,GAAG,CAAC,GAAGU,MAAM,CAAC;IAC1B,IAAI,CAACxL,GAAG,CAACK,aAAa,EAAE;EAC1B;EAEAmM,YAAYA,CAAA;IACV,IAAI,CAACd,KAAK,CAACC,aAAa,CAACrH,KAAK,GAAG,EAAE;IACnC,IAAI,CAACmI,IAAI,CAACd,aAAa,CAACrH,KAAK,GAAG,EAAE;IAClC,IAAI,CAACoI,UAAU,CAACC,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAG,KAAK,CAAC;IAC7D,IAAI,CAAC7M,GAAG,CAACK,aAAa,EAAE;EAC1B;EAEAiM,iBAAiBA,CAACT,MAAW;IAC3B,IAAI,CAACA,MAAM,CAACiB,UAAU,IAAI,IAAI,CAAC/B,YAAY,CAACgC,IAAI,KAAK,CAAC,EAAE;MACtD,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOlB,MAAM,CAACiB,UAAU,CAACE,IAAI,CAAEC,GAAW,IAAK,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAACrB,WAAW,EAAE,CAAC,CAAC;EAC1F;EAEAW,YAAYA,CAACV,MAAW;IACtB,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE;MACtB,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOY,MAAM,CAACsB,IAAI,IAAI,IAAI,CAAClC,YAAY;EACzC;EAEAmC,IAAIA,CAACH,GAAW;IACd,IAAI,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAClC,YAAY,CAACsC,MAAM,CAACJ,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAAClC,YAAY,CAACuC,GAAG,CAACL,GAAG,CAAC;IAC5B;EACF;EAEAM,YAAYA,CAACC,KAAU;IACrB,IAAI,CAACvC,YAAY,GAAGuC,KAAK,CAACC,MAAM,CAACnJ,KAAK;EACxC;EAAC,QAAAlK,CAAA;qBAlFUe,qBAAqB,EAAA8B,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAApB,mEAAA,GAAAoB,+DAAA,CAAAsF,mDAAA;EAAA;EAAA,QAAAlI,EAAA;UAArBc,qBAAqB;IAAAiG,SAAA;IAAAC,SAAA,WAAAsM,4BAAApM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;kEAOlBgJ,mEAAW;;;;;;;;;;;;;;;;;QCrCrBtN,4DAHN,aAA8B,aACJ,wBAC6C,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAsH;QAArBA,wDAAnB,mBAAA2Q,sDAAA;UAAA3Q,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC,mBAAAqC,sDAAA;UAAA5Q,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC;QAAnHvO,0DAAA,EAAsH;QACtHA,4DAAA,6BAA2D;QACzDA,wDAAA,IAAA6Q,2CAAA,yBAAgG;QAIpG7Q,0DADE,EAAmB,EACJ;QAEjBA,4DAAA,kBAI2B;QAAzBA,wDAAA,mBAAA8Q,wDAAA;UAAA9Q,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAuK,YAAA,EAAc;QAAA,EAAC;QACxB9O,4DAAA,oBAAkC;QAAAA,oDAAA,cAAM;QAC1CA,0DAD0C,EAAW,EAC5C;QAETA,4DAAA,kBAIyB;QAAzBA,wDAAA,mBAAA+Q,wDAAA;UAAA/Q,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAgL,YAAA,EAAc;QAAA,EAAC;QAACvP,oDAAA,sBACzB;QACFA,0DADE,EAAS,EACL;QAGJA,4DADF,cAAwB,kBACgD;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAS;QAIvFA,4DAHN,uBAA8D,eACtC,mBACY,wBACgD;QAAhDA,wDAAA,mBAAAgR,8DAAAC,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAmR,8DAAAF,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAoR,8DAAAH,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAqR,8DAAAJ,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAsR,8DAAAL,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAuR,8DAAAN,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAwR,8DAAAP,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAGvFA,0DAHuF,EAAe,EACxF,EACN,EACG;QAEXA,4DAAA,kBAA0E;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAS;QAG7FA,4DAFJ,uBAA+C,eACvB,0BACmE;QAAnCA,wDAAA,mBAAAyR,gEAAAR,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASiR,MAAA,CAAAC,eAAA,EAAwB;QAAA,EAAC;QACpFlR,4DAAA,iBAAW;QAAAA,oDAAA,oBAAY;QAAAA,0DAAA,EAAY;QACnCA,4DAAA,oBAAmE;QAAjCA,wDAAA,oBAAA0R,wDAAAT,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUuE,GAAA,CAAA+L,YAAA,CAAAW,MAAA,CAAoB;QAAA,EAAC;QAIzEjR,0DAJQ,EAAmE,EACpD,EACb,EACG,EACP;QAENA,4DAAA,eAAuB;QACrBA,wDAAA,KAAA2R,qCAAA,kBAAoC;QAIxC3R,0DADE,EAAM,EACF;;;;;;QAxDqDA,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA4R,OAAA,CAAwB;QAE5C5R,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAuE,GAAA,CAAA8J,eAAA,CAAkB;QAuBlCrO,uDAAA,GAA0B;QAA1BA,wDAAA,sBAAA6R,OAAA,CAA0B;QAe1B7R,uDAAA,IAA8B;QAA9BA,wDAAA,sBAAA8R,WAAA,CAA8B;QAYzB9R,uDAAA,IAAU;QAAVA,wDAAA,YAAAuE,GAAA,CAAAsJ,OAAA,CAAU;;;mBDtClCxH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACT1G,2DAAO,EACPvD,iEAAa,EAAA8V,2DAAA,EAAAA,kEAAA,EACbvW,yEAAiB,EAAAwW,mEAAA,EAEjB3E,mDAAK,EACLE,qHAA6B,EAC7BnS,kFAAqB,EAAA6W,4EAAA,EAAAE,8DAAA,EAAAF,mFAAA;IAAApN,MAAA;EAAA;;;;;;;;;;;;;;;;;;AE3BiC;AAC5B;;;AAKxB,MAAO9E,wBAAwB;EAMnCuC,YAAoBH,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAHlB,KAAAoQ,YAAY,GAAa,EAAE;IAC3B,KAAAC,UAAU,GAAkB,IAAI;IAGtC,IAAI,CAACrQ,MAAM,CAACkB,MAAM,CACfuH,IAAI,CACH2D,4CAAM,CAACgC,KAAK,IAAIA,KAAK,YAAY+B,4DAAe,CAAC,CAClD,CACApP,SAAS,CAAEqN,KAAK,IAAI;MACnB,MAAMkC,oBAAoB,GAAGlC,KAAwB;MACrD,IAAI,IAAI,CAACiC,UAAU,EAAE;QACnB,IAAI,CAACD,YAAY,CAACG,IAAI,CAAC,IAAI,CAACF,UAAU,CAAC;MACzC;MACA,IAAI,CAACA,UAAU,GAAGC,oBAAoB,CAACrQ,GAAG;MAC1CqB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC8O,UAAU,CAAC,CAAC,CAAC;MAC9C/O,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC6O,YAAY,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC;EACN;EAEOI,cAAcA,CAAA;IACnB,OAAO,IAAI,CAACJ,YAAY,CAAC5F,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC4F,YAAY,CAAC,IAAI,CAACA,YAAY,CAAC5F,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC9F;EAEOiG,kBAAkBA,CAAA;IACvB,MAAMC,WAAW,GAAG,IAAI,CAACF,cAAc,EAAE;IACzC,IAAIE,WAAW,EAAE;MACf,IAAI,CAAC1Q,MAAM,CAACwB,aAAa,CAACkP,WAAW,CAAC,CAACC,KAAK,CAAChP,KAAK,IAAIL,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC,CAAC;MAChG;MACA,IAAI,CAACyO,YAAY,CAACQ,GAAG,EAAE;IACzB,CAAC,MAAM;MACL,IAAI,CAAC5Q,MAAM,CAAC4B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC+O,KAAK,CAAChP,KAAK,IAAIL,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC,CAAC;IAC7F;EACF;EAAC,QAAA3G,CAAA;qBAnCU4C,wBAAwB,EAAAC,sDAAA,CAAApB,mDAAA;EAAA;EAAA,QAAAxB,EAAA;WAAxB2C,wBAAwB;IAAA2J,OAAA,EAAxB3J,wBAAwB,CAAA4J,IAAA;IAAAqB,UAAA,EAFvB;EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLkC;AAEkD;AACjC;AACH;AACI;AACrB;AAEmB;AAEnB;AACE;AAGT;AACC;AAEmB;;;;;;;;;;;;;ICVtDhL,4DAAA,qBAAoE;IAElEA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAHsCA,wDAAA,UAAAwN,SAAA,CAAgB;IAEjExN,uDAAA,EACF;IADEA,gEAAA,OAAAwN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAAhM,IAAA,MACF;;;;;IA8BJxB,4DAAA,0BAA8D;IAC5DA,oDAAA,GACF;IAAAA,0DAAA,EAAkB;;;;IAF8BA,wDAAA,UAAAuT,MAAA,CAAa;IAC3DvT,uDAAA,EACF;IADEA,gEAAA,MAAAuT,MAAA,CAAA/R,IAAA,MACF;;;ADQA,MAAOvD,yBAAyB;EAwBpCqE,YACUO,QAAqB,EACrB4Q,cAA8B,EAC9BtR,MAAc,EACdY,GAAsB,EACvB6K,OAA0B,EACzB8F,OAA2B;IAL3B,KAAA7Q,QAAQ,GAARA,QAAQ;IACR,KAAA4Q,cAAc,GAAdA,cAAc;IACd,KAAAtR,MAAM,GAANA,MAAM;IACN,KAAAY,GAAG,GAAHA,GAAG;IACJ,KAAA6K,OAAO,GAAPA,OAAO;IACN,KAAA8F,OAAO,GAAPA,OAAO;IA5BjB,KAAA7F,OAAO,GAAU,EAAE;IAIT,KAAAgC,UAAU,GAAmC,CACrD;MAAErI,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,CACvB;IAES,KAAAmS,UAAU,GAA2C,CAC7D;MAAEtM,KAAK,EAAE,YAAY;MAAEuM,SAAS,EAAE;IAAY,CAAE,EAChD;MAAEvM,KAAK,EAAE,QAAQ;MAAEuM,SAAS,EAAE;IAAQ,CAAE,EACxC;MAAEvM,KAAK,EAAE,SAAS;MAAEuM,SAAS,EAAE;IAAS,CAAE,CAC3C;IAES,KAAAC,gBAAgB,GAAkB,IAAI;IAgDxC,KAAAC,YAAY,GAAwB,IAAI;IAtC9C,IAAI,CAACC,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC7M,KAAK,CAAC;MACnCpE,KAAK,EAAE,CAAC,EAAE,CAAC;MACXuR,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,QAAQ,CAAC;MACpBC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEArR,QAAQA,CAAA;IACN,IAAI,CAAC2K,OAAO,CAAC2G,WAAW,EAAE,CAACrR,SAAS,CAAEiL,KAAK,IAAI;MAC7C1K,OAAO,CAACK,KAAK,CAAC,mBAAmB,EAAEqK,KAAK,CAAC;MACzC,IAAI,CAACP,OAAO,CAAC4G,QAAQ,GAAGrG,KAAK;MAC7B,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACrH,KAAK,CAACsH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAAC4G,QAAQ,CAACjG,MAAM,CAChDK,MAAM,IACLA,MAAM,CAACpH,EAAE,CAACC,QAAQ,EAAE,CAACoH,QAAQ,CAACL,WAAW,CAAC,IAC1CI,MAAM,CAACpN,IAAI,CAACmN,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAC/CI,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CAClD;EACH;EAEAiG,gBAAgBA,CAAC7F,MAAW;IAC1B,IAAI,CAACmF,UAAU,CAAChJ,GAAG,CAAC,SAAS,CAAC,EAAE2J,UAAU,CAAC9F,MAAM,CAAC;IAClD,IAAI,CAACmF,UAAU,CACZhJ,GAAG,CAAC,aAAa,CAAC,EACjB2J,UAAU,CAAC9F,MAAM,CAACnB,IAAI,GAAG,KAAK,GAAGmB,MAAM,CAACpN,IAAI,CAAC;EACnD;EAIAmT,YAAYA,CAAA;IACV,MAAMpN,SAAS,GAAGG,YAAY,CAACiE,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMnE,EAAE,GAAGyG,MAAM,CAAC1G,SAAS,CAAC;IAC5B,MAAM9E,KAAK,GACT,IAAI,CAACsR,UAAU,EAAE1M,KAAK,CAAC6M,OAAO,CAACzG,IAAI,GACnC,GAAG,GACH,IAAI,CAACsG,UAAU,EAAE1M,KAAK,CAAC6M,OAAO,CAAC1S,IAAI;IAErC,MAAMoT,cAAc,GAAG;MACrBnS,KAAK,EAAEA,KAAK;MACZuR,WAAW,EAAE,IAAI,CAACD,UAAU,EAAE1M,KAAK,CAAC2M,WAAW;MAC/Ca,OAAO,EAAErN,EAAE;MACX0M,OAAO,EAAE,IAAI,CAACH,UAAU,EAAE1M,KAAK,CAAC6M,OAAO;MACvCI,QAAQ,EAAE,IAAI,CAACP,UAAU,EAAE1M,KAAK,CAACiN,QAAQ;MACzCF,WAAW,EAAE,IAAI,CAACL,UAAU,EAAE1M,KAAK,CAAC+M,WAAW;MAC/CD,WAAW,EAAE,IAAI,CAACJ,UAAU,EAAE1M,KAAK,CAAC8M,WAAW;MAC/CE,QAAQ,EAAE,IAAI,CAACN,UAAU,EAAE1M,KAAK,CAACgN;KAClC;IAED,IAAI,CAACP,YAAY,GAAG,IAAI,CAAClG,OAAO,CAC7BkH,gBAAgB,CAACF,cAAc,CAAC,CAChC1R,SAAS,CAAC;MACTK,IAAI,EAAGwR,QAAQ,IAAI;QACjBtR,OAAO,CAACK,KAAK,CAAC,qCAAqC,EAAEiR,QAAQ,CAAC;QAE9D,MAAMC,gBAAgB,GAAG,IAAI,CAACpH,OAAO,CAACqH,iBAAiB,CAACF,QAAQ,CAAC;QACjE,IAAI,CAACnH,OAAO,CAACsH,aAAa,CAACF,gBAAgB,CAAC;QAE5C,IAAI,CAACnS,QAAQ,CAACe,IAAI,CAAC,qCAAqC,EAAE,GAAG,EAAE;UAC7DC,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAAC1B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,WAAWgR,QAAQ,CAACvN,EAAE,EAAE,CAAC,CAAC;MAClD,CAAC;MACD1D,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACN;EAEAqR,WAAWA,CAAA;IACT,IAAI,CAACrB,YAAY,EAAEsB,WAAW,EAAE,CAAC,CAAC;EACpC;EAAC,QAAAjY,CAAA;qBAjHUc,yBAAyB,EAAA+B,+DAAA,CAAApB,oEAAA,GAAAoB,+DAAA,CAAAsF,uDAAA,GAAAtF,+DAAA,CAAA+R,mDAAA,GAAA/R,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAgS,mEAAA,GAAAhS,+DAAA,CAAAiS,8DAAA;EAAA;EAAA,QAAA7U,EAAA;UAAzBa,yBAAyB;IAAAkG,SAAA;IAAAC,SAAA,WAAAkR,gCAAAhR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;gFAHzB,CAAC+O,uDAAc,CAAC,GAAArT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAA2P,mCAAAjR,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC3CrBtE,4DAJR,aAA8B,cACI,aACb,wBACoD,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAoJ;QAArBA,wDAAnB,mBAAAwV,0DAAA;UAAAxV,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC,mBAAAkH,0DAAA;UAAAzV,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC;QAAjJvO,0DAAA,EAAoJ;QACpJA,4DAAA,6BAAoH;QAAzDA,wDAAA,4BAAA0V,8EAAAzE,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAkBuE,GAAA,CAAAkQ,gBAAA,CAAAxD,MAAA,CAAArC,MAAA,CAAAvH,KAAA,CAAqC;QAAA,EAAC;QACjHrH,wDAAA,KAAA2V,gDAAA,wBAAoE;QAM1E3V,0DAFI,EAAmB,EACJ,EACb;QAGJA,4DADF,yBAAiC,iBACpB;QAAAA,oDAAA,2BAAS;QAAAA,0DAAA,EAAY;QAChCA,uDAAA,oBAA4D;QAC9DA,0DAAA,EAAiB;QAIbA,4DAFJ,cAAiB,sBACC,iBACH;QAAAA,oDAAA,6BAAgB;QAAAA,0DAAA,EAAY;QAErCA,4DADF,sBAA0C,sBAChB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAE7BA,0DAF6B,EAAa,EAC3B,EACE;QAGfA,4DADF,0BAAuC,iBAC1B;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAY;QACrCA,uDAAA,iBAA2D;QAE/DA,0DADE,EAAiB,EACb;QAENA,4DAAA,UAAI;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAK;QACvBA,4DAAA,4BAAsD;QACpDA,wDAAA,KAAA4V,qDAAA,6BAA8D;QAGhE5V,0DAAA,EAAmB;QAEnBA,4DAAA,eAAuB;QACrBA,uDAAA,eAAW;QAEXA,4DAAA,kBAAoF;QAAzBA,wDAAA,mBAAA6V,4DAAA;UAAA7V,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAoQ,YAAA,EAAc;QAAA,EAAC;QAAC3U,oDAAA,mBAAW;QAGrGA,0DAHqG,EAAS,EACpG,EACD,EACH;;;;QAlDEA,uDAAA,EAAwB;QAAxBA,wDAAA,cAAAuE,GAAA,CAAAwP,UAAA,CAAwB;QAI2D/T,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA4R,OAAA,CAAwB;QAE1E5R,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAuE,GAAA,CAAA8J,eAAA,CAAkB;QAiBrCrO,uDAAA,IAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QAYMA,uDAAA,GAAa;QAAbA,wDAAA,YAAAuE,GAAA,CAAAsL,UAAA,CAAa;;;mBDdhD7U,yDAAY,EAAAmX,oDAAA,EACZlM,+DAAmB,EAAAgM,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAInB5L,sEAAY,EACZC,kEAAQ,EACR6M,+DAAS,EACTnN,uDAAW,EAEXoM,6DAAS,EACTa,oEAAc,EACdC,mEAAa,EACb9M,8DAAQ,EACRF,gEAAS,EACT9K,kFAAqB,EAAA2a,4EAAA,EAAAA,mFAAA,EACrB3C,uDAAW,EAAA4C,iDAAA,EACX1C,2EAAqB;IAAAzO,MAAA;EAAA;;;;;;;;;;;;;;;;;;;AE1C4B;AAEW;;;AAW1D,MAAOzG,4BAA4B;EAGvCkE,YACSsL,OAA0B;IAA1B,KAAAA,OAAO,GAAPA,OAAO;IAHR,KAAAuI,aAAa,GAAGD,kEAAW,CAACC,aAAa;EAGZ;EAErClT,QAAQA,CAAA,GAER;EAEAmT,SAASA,CAAA;IACP,MAAM7O,SAAS,GAAGG,YAAY,CAACiE,OAAO,CAAC,WAAW,CAAC;IACnD;IACA,MAAMvJ,GAAG,GAAG,sGAAsG,IAAI,CAAC+T,aAAa,2CAA2C5O,SAAS,EAAE;IAC1L8O,KAAK,EAAEtN,MAAM,CAACnF,IAAI,CAACxB,GAAG,EAAE,QAAQ,CAAC;EACnC;EAAC,QAAAjF,CAAA;qBAfUiB,4BAA4B,EAAA4B,+DAAA,CAAApB,mEAAA;EAAA;EAAA,QAAAxB,EAAA;UAA5BgB,4BAA4B;IAAA+F,SAAA;IAAAoB,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAA0Q,sCAAAhS,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZnBtE,4DAFtB,aAAuB,aACE,WACH,QAAG;QAAAA,oDAAA,uEAAgE;QACvFA,0DADuF,EAAI,EAAI,EACzF;QAGJA,4DADF,aAAqB,QAChB;QAAAA,oDAAA,4NAA6K;QAClLA,0DADkL,EAAI,EAChL;QAENA,4DAAA,gBAAyF;QAAtBA,wDAAA,mBAAAuW,8DAAA;UAAA,OAAShS,GAAA,CAAA6R,SAAA,EAAW;QAAA,EAAC;QAACpW,oDAAA,yBAAkB;QAC7GA,0DAD6G,EAAS,EAChH;;;mBDDFkG,+DAAS;IAAArB,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AEJuD;AACY;AAClB;AACiE;AAI9E;AAGe;AACpB;AACC;;;;;;;;;;;;;;;;;ICQrC7E,4DADF,kBAAwG,WACN;IAAAA,oDAAA,GAAqB;;IACvHA,0DADuH,EAAI,EAChH;;;;;IAF8BA,wDAAA,YAAAA,6DAAA,IAAA4W,GAAA,EAAAtW,MAAA,CAAAuW,UAAA,kBAAAvW,MAAA,CAAAuW,UAAA,CAAAhH,UAAA,CAAAhB,QAAA,CAAAiI,MAAA,GAA8D;IAClG9W,uDAAA,EAAkE;IAAlEA,wDAAA,YAAAA,6DAAA,IAAA+W,GAAA,EAAAzW,MAAA,CAAAuW,UAAA,kBAAAvW,MAAA,CAAAuW,UAAA,CAAAhH,UAAA,CAAAhB,QAAA,CAAAiI,MAAA,GAAkE;IAA2B9W,uDAAA,EAAqB;IAArBA,+DAAA,CAAAA,yDAAA,OAAA8W,MAAA,EAAqB;;;;;IAMzH9W,uDAAA,2BAA8D;;;;;;IAa1DA,4DAAA,iBAAwF;IAAtBA,wDAAA,mBAAAiX,sGAAA;MAAAjX,2DAAA,CAAAkX,GAAA;MAAA,MAAA5W,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA6W,SAAA,EAAW;IAAA,EAAC;IAACnX,oDAAA,aAAM;IAAAA,0DAAA,EAAS;;;;;;IAVzGA,4DAAA,gBAIuC;IAD/BA,wDAAA,mBAAAoX,wFAAA;MAAApX,2DAAA,CAAAa,GAAA;MAAA,MAAAP,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA+W,WAAA,EAAa;IAAA,EAAC;IAE7BrX,uDAAA,kBAAwC;IACxCA,oDAAA,0BACF;IAAAA,0DAAA,EAAS;IAETA,wDAAA,IAAAsX,6EAAA,qBAA6B;IAG7BtX,4DAAA,iBAAyE;IAAvBA,wDAAA,mBAAAuX,wFAAA;MAAAvX,2DAAA,CAAAa,GAAA;MAAA,MAAAP,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAkX,UAAA,EAAY;IAAA,EAAC;IAACxX,oDAAA,WAAI;IAAAA,0DAAA,EAAS;;;;IAR9EA,wDAAA,cAAAM,MAAA,CAAAmX,gBAAA,CAA8B;IAKtCzX,uDAAA,GAEC;IAFDA,2DAAA,IAAAM,MAAA,CAAAoX,SAAA,mBAEC;;;;;;IAGD1X,4DAAA,iBAAwF;IAAtBA,wDAAA,mBAAA2X,wFAAA;MAAA3X,2DAAA,CAAA4X,GAAA;MAAA,MAAAtX,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAuX,SAAA,EAAW;IAAA,EAAC;IAAC7X,oDAAA,aAAM;IAAAA,0DAAA,EAAS;;;;;IADvGA,wDAdF,IAAA8X,+DAAA,OAA+B,IAAAC,+DAAA,OActB;;;;IAdT/X,2DAAA,IAAAM,MAAA,CAAA0X,WAAA,kBAgBC;;;ADGC,MAAOha,yBAAyB;EAQpCsE,YACUO,QAAqB,EACrBV,MAAc,EACfyL,OAA0B,EACzB9I,KAAqB,EACrB2O,cAA8B;IAJ9B,KAAA5Q,QAAQ,GAARA,QAAQ;IACR,KAAAV,MAAM,GAANA,MAAM;IACP,KAAAyL,OAAO,GAAPA,OAAO;IACN,KAAA9I,KAAK,GAALA,KAAK;IACL,KAAA2O,cAAc,GAAdA,cAAc;IAXxB,KAAAwE,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAG9D,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAT,gBAAgB,GAAkB,IAAI;EAQnC;EAEHxU,QAAQA,CAAA;IACN,IAAI,CAAC4T,UAAU,GAAG,IAAI,CAACjJ,OAAO,CAACuK,aAAa,EAAE;IAC9C,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAf,WAAWA,CAAA;IACT,IAAI,IAAI,CAACI,gBAAgB,EAAE;MACzB1O,MAAM,CAACnF,IAAI,CAAC,IAAI,CAAC6T,gBAAgB,EAAE,QAAQ,EAAE,qBAAqB,CAAC;IACrE,CAAC,MAAM;MACL,IAAI,CAAC5U,QAAQ,CAACe,IAAI,CAAC,gCAAgC,EAAE,QAAQ,EAAE;QAC7DC,QAAQ,EAAE,IAAI;QACdwU,UAAU,EAAE,CAAC,gBAAgB;OAC9B,CAAC;IACJ;EACF;EAEAR,SAASA,CAAA;IACP,MAAMtQ,SAAS,GAAGG,YAAY,CAACiE,OAAO,CAAC,WAAW,CAAC;IAEnD,IAAIpE,SAAS,KAAK,IAAI,EAAE;MACtB,MAAMC,EAAE,GAAGyG,MAAM,CAAC1G,SAAS,CAAC;MAC5B,IAAI,CAACqG,OAAO,CAAC0K,gBAAgB,CAAC,IAAI,CAACzB,UAAU,CAACrP,EAAE,EAAEA,EAAE,CAAC,CAACtE,SAAS,CAAC;QAC9DK,IAAI,EAAGwR,QAAQ,IAAI;UACjB,IAAI,CAAClS,QAAQ,CAACe,IAAI,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtDC,QAAQ,EAAE;WACX,CAAC;UAEF,IAAI,CAACqU,OAAO,GAAG,KAAK;UAEpB,IAAI,CAACE,SAAS,EAAE;QAClB,CAAC;QACDtU,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;OACD,CAAC;IACJ,CAAC,MAAM;MACLL,OAAO,CAACK,KAAK,CAAC,8CAA8C,CAAC;IAC/D;EACF;EAEA0T,UAAUA,CAAA;IACR,MAAMjQ,SAAS,GAAGG,YAAY,CAACiE,OAAO,CAAC,WAAW,CAAC;IACnD,IAAI,CAACuM,OAAO,GAAG,IAAI;IAEnB,IAAI3Q,SAAS,KAAK,IAAI,EAAE;MACtB,MAAMC,EAAE,GAAGyG,MAAM,CAAC1G,SAAS,CAAC;MAC5B,IAAI,CAACqG,OAAO,CAAC2K,iBAAiB,CAAC,IAAI,CAAC1B,UAAU,CAACrP,EAAE,EAAEA,EAAE,CAAC,CAACtE,SAAS,CAAC;QAC/DK,IAAI,EAAGiV,QAAQ,IAAI;UACjB,IAAI,CAAC3V,QAAQ,CAACe,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACpDC,QAAQ,EAAE;WACX,CAAC;UACF,IAAI,CAACqU,OAAO,GAAG,KAAK;UAEpB,IAAI,CAACE,SAAS,EAAE;QAClB,CAAC;QACDtU,KAAK,EAAGA,KAAK,IAAI;UACfL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACoU,OAAO,GAAG,KAAK;UACpB,IAAI,CAACrV,QAAQ,CAACe,IAAI,CAAC,wBAAwB,EAAE,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QACtE;OACD,CAAC;IACJ,CAAC,MAAM;MACLJ,OAAO,CAACK,KAAK,CAAC,8CAA8C,CAAC;IAC/D;EACF;EAEAqT,SAASA,CAAA;IACP,IAAI,IAAI,CAACN,UAAU,EAAE;MACnB,IAAI,CAAC1U,MAAM,CAAC4B,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAC9BiB,WAAW,EAAE;UAAEwC,EAAE,EAAE,IAAI,CAACqP,UAAU,CAACrP;QAAE;OACtC,CAAC;IACJ;EACF;EAEA4Q,SAASA,CAAA;IACP,MAAMK,OAAO,GAAG,IAAI,CAAC3T,KAAK,CAAC4T,QAAQ,CAACC,QAAQ,CAAC5N,GAAG,CAAC,SAAS,CAAC;IAC3D,MAAM6N,QAAQ,GAAGH,OAAO,GAAGxK,MAAM,CAACwK,OAAO,CAAC,GAAG,IAAI;IAEjD,IAAIG,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAI,CAAChL,OAAO,CAACiL,eAAe,CAACD,QAAQ,CAAC,CAAC1V,SAAS,CAAC;QAC/CK,IAAI,EAAGwR,QAAQ,IAAI;UACjBtR,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEiR,QAAQ,CAAC;UACtD,MAAMC,gBAAgB,GAAG,IAAI,CAACpH,OAAO,CAACqH,iBAAiB,CAACF,QAAQ,CAAC;UACjE,IAAI,CAAC8B,UAAU,GAAG7B,gBAAgB;UAClC,IAAI,CAACyC,gBAAgB,GAAG,IAAI,CAACZ,UAAU,CAACY,gBAAgB,CAAC,CAAC;UAE1DhU,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEkR,gBAAgB,CAAC;UAE7D,MAAMzN,SAAS,GAAGG,YAAY,CAACiE,OAAO,CAAC,WAAW,CAAC;UACnD,MAAMnE,EAAE,GAAGyG,MAAM,CAAC1G,SAAS,CAAC;UAE5B,IAAI,IAAI,CAACsP,UAAU,IAAIpK,KAAK,CAACqM,OAAO,CAAC,IAAI,CAACjC,UAAU,CAACkC,QAAQ,CAAC,EAAE;YAC9D,MAAMC,gBAAgB,GAAG,IAAI,CAACnC,UAAU,CAACkC,QAAQ,CAAChJ,IAAI,CACnDkJ,OAAY,IAAKA,OAAO,CAACzR,EAAE,KAAKA,EAAE,CACpC;YACD,IAAI,CAACwQ,WAAW,GAAGgB,gBAAgB;YAEnC,MAAME,OAAO,GAAG,IAAI,CAACrC,UAAU,CAAChC,OAAO,KAAKrN,EAAE;YAC9C,IAAI,CAACkQ,SAAS,GAAGwB,OAAO;UAC1B;QACF,CAAC;QACDpV,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACnC;OACD,CAAC;IACJ;EACF;EAAC,QAAA5G,CAAA;qBA1HUa,yBAAyB,EAAAgC,+DAAA,CAAApB,oEAAA,GAAAoB,+DAAA,CAAAsF,mDAAA,GAAAtF,+DAAA,CAAA+R,mEAAA,GAAA/R,+DAAA,CAAAsF,2DAAA,GAAAtF,+DAAA,CAAAgS,uDAAA;EAAA;EAAA,QAAA5U,EAAA;UAAzBY,yBAAyB;IAAAmG,SAAA;IAAAoB,UAAA;IAAAC,QAAA,GAAAxF,gEAAA,CAFzB,CAACqT,uDAAc,CAAC,GAAArT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAwT,mCAAA9U,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrC3BtE,4DADF,aAA2B,wBACM;QAAAA,oDAAA,GAAkD;QAAAA,0DAAA,EAAiB;QAClGA,4DAAA,2BAAqC;QAAAA,oDAAA,GAAkC;QAAAA,0DAAA,EAAoB;QAGtFA,4DADL,aAA8B,QACzB,aAAQ;QAAAA,oDAAA,cAAO;QAAAA,0DAAA,EAAS;QAACA,oDAAA,GAA8B;QAAAA,0DAAA,EAAI;QAC3DA,4DAAH,SAAG,cAAQ;QAAAA,oDAAA,4BAAe;QAAAA,0DAAA,EAAS;QAACA,oDAAA,IAAsB;QAC5DA,0DAD4D,EAAI,EAC1D;QAGDA,4DADL,WAAK,SACA,cAAQ;QAAAA,oDAAA,sBAAc;QAASA,0DAAT,EAAS,EAAI;QACtCA,4DAAA,oBAAc;QACZA,wDAAA,KAAAqZ,8CAAA,sBAAwG;QAI5GrZ,0DADE,EAAe,EACX;QAIJA,wDAFF,KAAAsZ,iDAAA,+BAAsB,KAAAC,iDAAA,OAEb;QAmBXvZ,0DAAA,EAAM;QAENA,uDAAA,eAAW;;;QAxCsBA,uDAAA,GAAkD;QAAlDA,+DAAA,EAAAuE,GAAA,CAAAsS,UAAA,kBAAAtS,GAAA,CAAAsS,UAAA,CAAApJ,IAAA,aAAAlJ,GAAA,CAAAsS,UAAA,kBAAAtS,GAAA,CAAAsS,UAAA,CAAApU,KAAA,EAAkD;QAC5CzC,uDAAA,GAAkC;QAAlCA,+DAAA,CAAAuE,GAAA,CAAAsS,UAAA,kBAAAtS,GAAA,CAAAsS,UAAA,CAAA2C,gBAAA,CAAkC;QAGzCxZ,uDAAA,GAA8B;QAA9BA,gEAAA,MAAAuE,GAAA,CAAAsS,UAAA,kBAAAtS,GAAA,CAAAsS,UAAA,CAAA4C,YAAA,KAA8B;QACtBzZ,uDAAA,GAAsB;QAAtBA,gEAAA,MAAAuE,GAAA,CAAAsS,UAAA,kBAAAtS,GAAA,CAAAsS,UAAA,CAAA3G,IAAA,KAAsB;QAM9BlQ,uDAAA,GAAa;QAAbA,wDAAA,YAAAuE,GAAA,CAAA0T,UAAA,CAAa;QAM3CjY,uDAAA,EAoBC;QApBDA,2DAAA,KAAAuE,GAAA,CAAA2T,OAAA,oBAoBC;;;mBDxBCld,yDAAY,EAAAiX,oDAAA,EAAAA,oDAAA,EAAAA,0DAAA,EAIZzL,gEAAY,EACZkQ,mEAAe,EAEfF,+DAAU,EAEVC,4DAAO,EAEPvQ,+DAAS,EAIT1G,2DAAO,EAMP4T,uDAAW,EAAAjB,iDAAA,EACXmB,2EAAqB,EAAAyC,qEAAA;IAAAlR,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AE5CwB;AACI;AACF;AACmB;AAEf;AACqB;AACiC;AACrE;AAC+B;AAE3B;AACC;;;;;;;;;;;;;ICVrC7E,4DAAA,qBAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAFsCA,wDAAA,UAAAwN,SAAA,CAAAC,IAAA,WAAAD,SAAA,CAAA/K,KAAA,CAA4C;IAC7FzC,uDAAA,EACF;IADEA,gEAAA,OAAAwN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAA/K,KAAA,MACF;;;;;IAsDJzC,4DAAA,UAAoC;IAClCA,uDAAA,sCAE8B;IAChCA,0DAAA,EAAM;;;;IAFJA,uDAAA,EAAqB;IAArBA,wDAAA,eAAA2N,SAAA,CAAqB;;;ADzBrB,MAAO/P,4BAA4B;EASvC0E,YACUS,GAAsB,EACvB6K,OAA0B,EACzBzL,MAAc;IAFd,KAAAY,GAAG,GAAHA,GAAG;IACJ,KAAA6K,OAAO,GAAPA,OAAO;IACN,KAAAzL,MAAM,GAANA,MAAM;IAXhB,KAAA0L,OAAO,GAAU,EAAE;IAEnB,KAAAC,YAAY,GAAgB,IAAIC,GAAG,EAAE;IACrC,KAAAC,YAAY,GAAW,EAAE;EAStB;EAEH/K,QAAQA,CAAA;IACN,IAAI,CAAC2K,OAAO,CAACiM,cAAc,EAAE,CAAC3W,SAAS,CAAEiL,KAAK,IAAI;MAChD,IAAI,CAACP,OAAO,CAACkM,WAAW,GAAG3L,KAAK;MAChC,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACrH,KAAK,CAACsH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAACkM,WAAW,CAACvL,MAAM,CAACK,MAAM,IAC3DA,MAAM,CAACnM,KAAK,CAACkM,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CACpG;EACH;EAEAM,YAAYA,CAAA;IACV,MAAMN,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACrH,KAAK,CAACsH,WAAW,EAAE;IAEhE;IACA,MAAM,CAACI,UAAU,EAAEC,WAAW,CAAC,GAAGR,WAAW,CAACS,KAAK,CAAC,KAAK,CAAC,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAAC;IAEnF,MAAMb,MAAM,GAAG,IAAI,CAACX,OAAO,CAACkM,WAAW,EAAEvL,MAAM,CAACK,MAAM,IACpD,IAAI,CAACS,iBAAiB,CAACT,MAAM,CAAC,IAC9B,IAAI,CAACU,YAAY,CAACV,MAAM,CAAC,KACxBA,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACE,UAAU,CAAC,IAC9CH,MAAM,CAACnM,KAAK,CAACkM,WAAW,EAAE,CAACE,QAAQ,CAACG,WAAW,CAAC,CAAC,CACnD,IAAI,EAAE;IAEP,IAAI,CAACnB,OAAO,GAAG,CAAC,GAAGU,MAAM,CAAC;IAC1B,IAAI,CAACxL,GAAG,CAACK,aAAa,EAAE;EAC1B;EAEAmM,YAAYA,CAAA;IACV,IAAI,CAACd,KAAK,CAACC,aAAa,CAACrH,KAAK,GAAG,EAAE;IACnC,IAAI,CAACmI,IAAI,CAACd,aAAa,CAACrH,KAAK,GAAG,EAAE;IAClC,IAAI,CAACoI,UAAU,CAACC,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAACC,OAAO,GAAG,KAAK,CAAC;IAC7D,IAAI,CAAC7M,GAAG,CAACK,aAAa,EAAE;IAExB,IAAI,CAACwK,OAAO,CAACiM,cAAc,EAAE,CAAC3W,SAAS,CAAEiL,KAAK,IAAI;MAChD,IAAI,CAACP,OAAO,CAACkM,WAAW,GAAG3L,KAAK;MAChC,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEAe,iBAAiBA,CAACT,MAAW;IAC3B,IAAI,CAACA,MAAM,CAACiB,UAAU,IAAI,IAAI,CAAC/B,YAAY,CAACgC,IAAI,KAAK,CAAC,EAAE;MACtD,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOlB,MAAM,CAACiB,UAAU,CAACE,IAAI,CAAEC,GAAW,IAAK,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAACrB,WAAW,EAAE,CAAC,CAAC;EAC1F;EAEAW,YAAYA,CAACV,MAAW;IACtB,IAAI,CAAC,IAAI,CAACZ,YAAY,EAAE;MACtB,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAOY,MAAM,CAACsB,IAAI,IAAI,IAAI,CAAClC,YAAY;EACzC;EAEAmC,IAAIA,CAACH,GAAW;IACd,IAAI,IAAI,CAAClC,YAAY,CAACmC,GAAG,CAACD,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAClC,YAAY,CAACsC,MAAM,CAACJ,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAAClC,YAAY,CAACuC,GAAG,CAACL,GAAG,CAAC;IAC5B;EACF;EAEAM,YAAYA,CAACC,KAAU;IACrB,IAAI,CAACvC,YAAY,GAAGuC,KAAK,CAACC,MAAM,CAACnJ,KAAK;EACxC;EAEA0S,cAAcA,CAAA;IACZ,IAAI,CAAC5X,MAAM,CAAC4B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAAC,QAAA5G,CAAA;qBAxFUS,4BAA4B,EAAAoC,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAApB,mEAAA,GAAAoB,+DAAA,CAAAsF,mDAAA;EAAA;EAAA,QAAAlI,EAAA;UAA5BQ,4BAA4B;IAAAuG,SAAA;IAAAC,SAAA,WAAA4V,mCAAA1V,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;kEAOzBgJ,mEAAW;;;;;;;;;;gFATZ,CAAC+F,uDAAc,CAAC,GAAArT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAqU,sCAAA3V,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QClCzBtE,4DAHN,aAA8B,aACJ,wBAC6C,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAsH;QAArBA,wDAAnB,mBAAAka,6DAAA;UAAAla,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC,mBAAA4L,6DAAA;UAAAna,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC;QAAnHvO,0DAAA,EAAsH;QACtHA,4DAAA,8BAA2D;QACzDA,wDAAA,IAAAoa,kDAAA,yBAAgG;QAIpGpa,0DADE,EAAmB,EACJ;QAEjBA,4DAAA,kBAI2B;QAAzBA,wDAAA,mBAAAqa,+DAAA;UAAAra,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAuK,YAAA,EAAc;QAAA,EAAC;QACxB9O,4DAAA,oBAAkC;QAAAA,oDAAA,cAAM;QAC1CA,0DAD0C,EAAW,EAC5C;QAETA,4DAAA,kBAIyB;QAAzBA,wDAAA,mBAAAsa,+DAAA;UAAAta,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAgL,YAAA,EAAc;QAAA,EAAC;QAACvP,oDAAA,sBACzB;QACFA,0DADE,EAAS,EACL;QAGJA,4DADF,cAAwB,kBACgD;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAS;QAIvFA,4DAHN,uBAA8D,eACtC,sBACqB,wBACuC;QAAhDA,wDAAA,mBAAAua,qEAAAtJ,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAwa,qEAAAvJ,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAAya,qEAAAxJ,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA0a,qEAAAzJ,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA2a,qEAAA1J,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA4a,qEAAA3J,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAAAA,0DAAA,EAAe;QAChGA,4DAAA,wBAA8E;QAAhDA,wDAAA,mBAAA6a,qEAAA5J,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAS4Q,MAAA,CAAAC,eAAA,EAAwB;UAAA,OAAAlR,yDAAA,CAAEuE,GAAA,CAAA4L,IAAA,CAAK,KAAK,CAAC;QAAA,EAAC;QAACnQ,oDAAA,WAAG;QAGvFA,0DAHuF,EAAe,EACxF,EACN,EACG;QAEXA,4DAAA,kBAA0E;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAS;QAG7FA,4DAFJ,uBAA+C,eACvB,0BACmE;QAAnCA,wDAAA,mBAAA8a,uEAAA7J,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASiR,MAAA,CAAAC,eAAA,EAAwB;QAAA,EAAC;QACpFlR,4DAAA,iBAAW;QAAAA,oDAAA,oBAAY;QAAAA,0DAAA,EAAY;QACnCA,4DAAA,oBAAmE;QAAjCA,wDAAA,oBAAA+a,+DAAA9J,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUuE,GAAA,CAAA+L,YAAA,CAAAW,MAAA,CAAoB;QAAA,EAAC;QAIzEjR,0DAJQ,EAAmE,EACpD,EACb,EACG,EACP;QAGJA,4DADF,eAAsC,kBAC4B;QAA3BA,wDAAA,mBAAAgb,+DAAA;UAAAhb,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAwV,cAAA,EAAgB;QAAA,EAAC;QAC7D/Z,4DAAA,gBAAU;QAAAA,oDAAA,WAAG;QAEjBA,0DAFiB,EAAW,EACjB,EACL;QAENA,4DAAA,eAAuB;QACrBA,wDAAA,KAAAib,4CAAA,kBAAoC;QAMxCjb,0DADE,EAAM,EACF;QAENA,uDAAA,eAAW;;;;;;QAlEgDA,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA4R,OAAA,CAAwB;QAE5C5R,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAuE,GAAA,CAAA8J,eAAA,CAAkB;QAuBlCrO,uDAAA,GAA0B;QAA1BA,wDAAA,sBAAA6R,OAAA,CAA0B;QAe1B7R,uDAAA,IAA8B;QAA9BA,wDAAA,sBAAA8R,WAAA,CAA8B;QAkBzB9R,uDAAA,IAAU;QAAVA,wDAAA,YAAAuE,GAAA,CAAAsJ,OAAA,CAAU;;;mBDtC9BxH,sEAAY,EACZC,kEAAQ,EACRF,6DAAQ,EACRF,+DAAS,EACT1G,2DAAO,EACPvD,kEAAa,EAAA8V,4DAAA,EAAAA,mEAAA,EACbvW,yEAAiB,EAAAwW,mEAAA,EAEjB3E,mDAAK,EACLE,qHAA6B,EAC7BnS,kFAAqB,EAAA6W,4EAAA,EAAAE,8DAAA,EAAAF,mFAAA,EACrBmB,uDAAW,EAAA2C,iDAAA;IAAAlR,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;;;AEhCkC;AACE;AACO;AACiE;;;;;;ICmBtH7E,4DADD,kBAAqE,WAC5C;IAAAA,oDAAA,GAAmB;;IAC5CA,0DAD4C,EAAI,EACrC;;;;IADcA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAA,yDAAA,OAAA8W,MAAA,EAAmB;;;ADI9C,MAAOvJ,6BAA6B;EAGxCjL,YACSsL,OAA0B,EACzBzL,MAAc;IADf,KAAAyL,OAAO,GAAPA,OAAO;IACN,KAAAzL,MAAM,GAANA,MAAM;EAAW;EAE3BiZ,iBAAiBA,CAACvE,UAAe;IAC/B,IAAI,CAACjJ,OAAO,CAACsH,aAAa,CAAC2B,UAAU,CAAC;IACtC,IAAI,CAAC1U,MAAM,CAAC4B,QAAQ,CAAC,CAAC,WAAW8S,UAAU,CAACrP,EAAE,EAAE,CAAC,CAAC;IAElD;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAAC,QAAArK,CAAA;qBAlBUoQ,6BAA6B,EAAAvN,+DAAA,CAAApB,mEAAA,GAAAoB,+DAAA,CAAAsF,mDAAA;EAAA;EAAA,QAAAlI,EAAA;UAA7BmQ,6BAA6B;IAAApJ,SAAA;IAAAkX,MAAA;MAAAxE,UAAA;IAAA;IAAAtR,UAAA;IAAAC,QAAA,GAAAxF,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAA0V,uCAAAhX,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1BpCtE,4DAHN,aAAuB,eACX,sBACS,qBACC;QAAAA,oDAAA,GAAsB;QAAAA,0DAAA,EAAiB;QACvDA,4DAAA,wBAAmB;QAAAA,oDAAA,GAAiC;QACtDA,0DADsD,EAAoB,EACxD;QAGdA,4DAFJ,uBAAkB,QACb,aACO;QAAAA,oDAAA,gBAAQ;QAAAA,0DAAA,EAAS;QACzBA,oDAAA,IACF;QAAAA,0DAAA,EAAI;QAEFA,4DADF,SAAG,cACO;QAAAA,oDAAA,eAAO;QAAAA,0DAAA,EAAS;QACxBA,oDAAA,IACF;QAAAA,0DAAA,EAAI;QAEFA,4DADF,SAAG,cACO;QAAAA,oDAAA,mBAAW;QAAAA,0DAAA,EAAS;QAC5BA,oDAAA,IACF;;QAAAA,0DAAA,EAAI;QAEFA,4DADF,SAAG,cACO;QAAAA,oDAAA,4BAAe;QAAAA,0DAAA,EAAS;QAChCA,oDAAA,IACF;QAAAA,0DAAA,EAAI;QACJA,4DAAA,oBAAc;QACZA,wDAAA,KAAAub,kDAAA,sBAAqE;QAIzEvb,0DADE,EAAe,EACE;QAGjBA,4DADF,wBAAkB,iBAK0B;QAAxCA,wDAAA,mBAAAwb,gEAAA;UAAA,OAASjX,GAAA,CAAA6W,iBAAA,CAAA7W,GAAA,CAAAsS,UAAA,CAA6B;QAAA,EAAC;QACvC7W,oDAAA,kBACF;QAGNA,0DAHM,EAAS,EACQ,EACV,EACP;;;QArCgBA,uDAAA,GAAsB;QAAtBA,+DAAA,CAAAuE,GAAA,CAAAsS,UAAA,CAAApU,KAAA,CAAsB;QACnBzC,uDAAA,GAAiC;QAAjCA,+DAAA,CAAAuE,GAAA,CAAAsS,UAAA,CAAA2C,gBAAA,CAAiC;QAKlDxZ,uDAAA,GACF;QADEA,gEAAA,MAAAuE,GAAA,CAAAsS,UAAA,CAAA4E,OAAA,MACF;QAGEzb,uDAAA,GACF;QADEA,gEAAA,MAAAuE,GAAA,CAAAsS,UAAA,CAAA4C,YAAA,MACF;QAGEzZ,uDAAA,GACF;QADEA,gEAAA,MAAAA,yDAAA,QAAAuE,GAAA,CAAAsS,UAAA,CAAAxC,QAAA,OACF;QAGErU,uDAAA,GACF;QADEA,gEAAA,MAAAuE,GAAA,CAAAsS,UAAA,CAAA3G,IAAA,MACF;QAE6ClQ,uDAAA,GAAwB;QAAxBA,wDAAA,YAAAuE,GAAA,CAAAsS,UAAA,CAAAhH,UAAA,CAAwB;;;mBDTnEtJ,2DAAO,EACP2U,iEAAa,EACb1U,gEAAY,EACZkQ,mEAAe,EACfjQ,kEAAc,EACd+P,+DAAU,EACVnJ,kDAAK,EACLoJ,4DAAO,EACP0E,kEAAc,EACdjV,+DAAS,EAETyT,0DAAa;IAAA9U,MAAA;EAAA;;;;;;;;;;;;;;;;;;;;AE1BuC;AACT;AACY;;AAI/D,MAAMwF,QAAQ,GAAG,EAAE;AAEnB,MAAMC,WAAW,GAAG;EAClBC,OAAO,EAAE,IAAIN,6DAAW,CAAC;IAAE,cAAc,EAAE;EAAkB,CAAE;CAChE;AAKK,MAAOwG,iBAAiB;EAO5BnO,YAAA;IALA,KAAAwX,WAAW,GAAiB,EAAE;IAC9B,KAAA1L,aAAa,GAAiB,EAAE;IAChC,KAAAoG,QAAQ,GAAU,EAAE;IAEH,KAAAhK,IAAI,GAAG1L,qDAAM,CAACkL,4DAAU,CAAC;EAC3B;EAEf6P,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACrP,IAAI,CACbE,IAAI,CAAQ,GAAGL,QAAQ,sBAAsB,EAAE,EAAE,EAAEC,WAAW,CAAC,CAC/DM,IAAI,CACHsE,yCAAG,CAAE4K,WAAW,IAAKA,WAAW,CAAC5K,GAAG,CAAC,IAAI,CAAC+F,iBAAiB,CAAC,CAAC,EAC7D/K,gDAAU,CAAEpG,KAAK,IAAI;MACnBL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOoH,wCAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACL;EAEAgD,kBAAkBA,CAACwN,SAAiB;IAClC,MAAMC,WAAW,GAAG;MAAED;IAAS,CAAE;IAEjC,OAAO,IAAI,CAAClR,IAAI,CAACE,IAAI,CAAQ,GAAGL,QAAQ,sBAAsB,EAAEsR,WAAW,EAAErR,WAAW,CAAC,CACxFM,IAAI,CACHsE,yCAAG,CAAE4K,WAAW,IAAKA,WAAW,CAAC5K,GAAG,CAAC,IAAI,CAAC+F,iBAAiB,CAAC,CAAC,EAC7D/K,gDAAU,CAAEpG,KAAK,IAAI;MACnBL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOoH,wCAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;IAAC;EACJ;EAEO+J,iBAAiBA,CAAC2G,IAAS;IAChC,MAAM;MACJpU,EAAE;MACFwM,WAAW;MACX6H,KAAK;MACL3H,OAAO;MACP6E,QAAQ;MACR5E,WAAW;MACXC,WAAW;MACXC,QAAQ;MACRC,QAAQ;MACRO,OAAO;MACP4C;IAAgB,CACjB,GAAGmE,IAAI;IAER;IACA;IAEA,IAAIE,UAAU,GAAG,CAAC;IAClB,IAAID,KAAK,EAAE;MACTC,UAAU,GAAG,CAAC;IAChB;IACA,MAAMC,WAAW,GAAG,GAAGD,UAAU,IAAI,CAAC,EAAE;IAExC,MAAME,iBAAiB,GAAGjD,QAAQ,CAACpM,MAAM;IACzC,MAAMsP,gBAAgB,GAAG,GAAGD,iBAAiB,IAAI7H,WAAW,EAAE;IAC9D,MAAM+H,UAAU,GAAG9H,WAAW,CAAC+H,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAE3C,IAAIC,cAAc,GAAG,EAAE;IACvB,QAAQ/H,QAAQ;MACd,KAAK,QAAQ;QACX+H,cAAc,GAAG,QAAQ;QACzB;MACF,KAAK,YAAY;QACfA,cAAc,GAAG,YAAY;QAC7B;MACF;QACEA,cAAc,GAAG,SAAS;IAC9B;IAEA,OAAO;MACL5U,EAAE,EAAEA,EAAE;MACN/E,KAAK,EAAEyR,OAAO,CAAC1S,IAAI;MACnBiM,IAAI,EAAEyG,OAAO,CAACzG,IAAI;MAClBoH,OAAO,EAAEA,OAAO;MAChB2E,gBAAgB,EAAExF,WAAW;MAC7BK,QAAQ,EAAE+H,cAAc;MACxBlM,IAAI,EAAEgM,UAAU;MAChBT,OAAO,EAAEM,WAAW;MACpBtC,YAAY,EAAEwC,gBAAgB;MAC9BlD,QAAQ,EAAEA,QAAQ;MAClBlJ,UAAU,EAAEyE,QAAQ,CAACpF,GAAG,CAAEc,GAAqB,IAC7CA,GAAG,CAACxO,IAAI,CAACmN,WAAW,EAAE,CAAC0N,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CACvC;MACD5E,gBAAgB,EAAEA;KACnB;EACH;EAEAa,gBAAgBA,CAACgE,OAAe,EAAC9U,EAAU;IACzC,OAAO,IAAI,CAACgD,IAAI,CAACE,IAAI,CAAQ,GAAGL,QAAQ,iBAAiBiS,OAAO,aAAa9U,EAAE,OAAO,EAAE,EAAE,EAAE8C,WAAW,CAAC;EAC1G;EAEAiO,iBAAiBA,CAAC+D,OAAe,EAAC9U,EAAU;IAC1C,OAAO,IAAI,CAACgD,IAAI,CAACE,IAAI,CAAQ,GAAGL,QAAQ,iBAAiBiS,OAAO,aAAa9U,EAAE,QAAQ,EAAE,EAAE,EAAE8C,WAAW,CAAC;EAC3G;EAEAiK,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC/J,IAAI,CAACO,GAAG,CAAQ,GAAGV,QAAQ,WAAW,CAAC;EACrD;EAEAwO,eAAeA,CAAC0D,YAAoB;IAClC,OAAO,IAAI,CAAC/R,IAAI,CAACO,GAAG,CAAQ,GAAGV,QAAQ,iBAAiBkS,YAAY,EAAE,CAAC;EACzE;EAEAzH,gBAAgBA,CAACF,cAAmB;IAClC,OAAO,IAAI,CAACpK,IAAI,CAACE,IAAI,CAAM,GAAGL,QAAQ,eAAe,EAAEuK,cAAc,CAAC;EACxE;EAEA4H,cAAcA,CAAC5H,cAAmB,EAAE0H,OAAe;IACjD,OAAO,IAAI,CAAC9R,IAAI,CAACiS,GAAG,CAAM,GAAGpS,QAAQ,iBAAiBiS,OAAO,EAAE,EAAE1H,cAAc,CAAC;EAClF;EAEAM,aAAaA,CAAC1R,IAAS;IACrB,IAAI,CAACkZ,eAAe,GAAGlZ,IAAI;EAC7B;EAEA2U,aAAaA,CAAA;IACX,OAAO,IAAI,CAACuE,eAAe;EAC7B;EAAC,QAAAvf,CAAA;qBA1HUsT,iBAAiB;EAAA;EAAA,QAAArT,EAAA;WAAjBqT,iBAAiB;IAAA/G,OAAA,EAAjB+G,iBAAiB,CAAA9G,IAAA;IAAAqB,UAAA,EAFhB;EAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbkC;AAEkD;AACjC;AACH;AACI;AACrB;AACmB;AAEnB;AACE;AAIW;AACpB;AACC;;;;;;;;;;;;;;ICTnChL,4DAAA,qBAAoE;IAElEA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAHsCA,wDAAA,UAAAwN,SAAA,CAAgB;IAEjExN,uDAAA,EACF;IADEA,gEAAA,OAAAwN,SAAA,CAAAC,IAAA,QAAAD,SAAA,CAAAhM,IAAA,MACF;;;;;IA8BJxB,4DAAA,0BAA8D;IAC5DA,oDAAA,GACF;IAAAA,0DAAA,EAAkB;;;;IAF8BA,wDAAA,UAAAuT,MAAA,CAAa;IAC3DvT,uDAAA,EACF;IADEA,gEAAA,MAAAuT,MAAA,CAAA/R,IAAA,MACF;;;;;IAOExB,uDAAA,4BAAyC;;;;;;IAEzCA,4DAAA,iBAAkF;IAAvBA,wDAAA,mBAAA2c,0EAAA;MAAA3c,2DAAA,CAAAkX,GAAA;MAAA,MAAA0F,MAAA,GAAA5c,2DAAA;MAAA,OAAAA,yDAAA,CAAS4c,MAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAAC7c,oDAAA,sBAAe;IAAAA,0DAAA,EAAS;;;ADL5G,MAAO7B,yBAAyB;EAyBpCmE,YACUO,QAAqB,EACrB4Q,cAA8B,EAC9B3O,KAAqB,EACrB3C,MAAc,EACdY,GAAsB,EACvB6K,OAA0B,EACzB8F,OAA2B;IAN3B,KAAA7Q,QAAQ,GAARA,QAAQ;IACR,KAAA4Q,cAAc,GAAdA,cAAc;IACd,KAAA3O,KAAK,GAALA,KAAK;IACL,KAAA3C,MAAM,GAANA,MAAM;IACN,KAAAY,GAAG,GAAHA,GAAG;IACJ,KAAA6K,OAAO,GAAPA,OAAO;IACN,KAAA8F,OAAO,GAAPA,OAAO;IA9BjB,KAAA7F,OAAO,GAAU,EAAE;IAEnB,KAAAqK,OAAO,GAAY,KAAK;IAGd,KAAArI,UAAU,GAAmC,CACrD;MAAErI,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,EACtB;MAAEgG,EAAE,EAAE,CAAC;MAAEhG,IAAI,EAAE;IAAK,CAAE,CACvB;IAES,KAAAmS,UAAU,GAA2C,CAC7D;MAAEtM,KAAK,EAAE,YAAY;MAAEuM,SAAS,EAAE;IAAY,CAAE,EAChD;MAAEvM,KAAK,EAAE,QAAQ;MAAEuM,SAAS,EAAE;IAAQ,CAAE,EACxC;MAAEvM,KAAK,EAAE,SAAS;MAAEuM,SAAS,EAAE;IAAS,CAAE,CAC3C;IAES,KAAAC,gBAAgB,GAAkB,IAAI;IAU9C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC7M,KAAK,CAAC;MACnCW,EAAE,EAAE,CAAC,EAAE,CAAC;MACR/E,KAAK,EAAE,CAAC,EAAE,CAAC;MACXuR,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,OAAO,EAAE,CAAC,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,QAAQ,EAAE,CAAC,QAAQ,CAAC;MACpBC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEArR,QAAQA,CAAA;IACN,IAAI,CAAC2K,OAAO,CAAC2G,WAAW,EAAE,CAACrR,SAAS,CAAEiL,KAAK,IAAI;MAC7C,IAAI,CAACP,OAAO,CAAC4G,QAAQ,GAAGrG,KAAK;MAC7B,IAAI,CAACN,OAAO,GAAGM,KAAK;MACpB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACR,OAAO,CAACS,KAAK,EAAE;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACxJ,KAAK,CAACE,WAAW,CAAC9B,SAAS,CAAC+B,MAAM,IAAG;MACxC,MAAMqX,OAAO,GAAGrX,MAAM,CAAC,IAAI,CAAC;MAE5B,IAAI,CAAC2I,OAAO,CAACiL,eAAe,CAACyD,OAAO,CAAC,CAACpZ,SAAS,CAAEiL,KAAU,IAAI;QAC7D,IAAI,CAAC4F,UAAU,CAACW,UAAU,CAACvG,KAAK,CAAC;QACjC,IAAI,CAAC4F,UAAU,CAAChJ,GAAG,CAAC,aAAa,CAAC,EAAE2J,UAAU,CAACvG,KAAK,CAAC+F,OAAO,CAACzG,IAAI,GAAG,KAAK,GAAGU,KAAK,CAAC+F,OAAO,CAAC1S,IAAI,CAAC;QAE/F,MAAMsM,YAAY,GAAGK,KAAK,CAACmG,QAAQ,CAACpF,GAAG,CAAEc,GAAiC,IAAI;UAC5E,OAAO,IAAI,CAACH,UAAU,CAACiN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvV,EAAE,KAAKwI,GAAG,CAACxI,EAAE,CAAC;QACnD,CAAC,CAAC;QACF,IAAI,CAACuM,UAAU,CAAChJ,GAAG,CAAC,UAAU,CAAC,EAAE2J,UAAU,CAAC5G,YAAY,CAAC;MAC3D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAS,MAAMA,CAAA;IACJ,MAAMC,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,aAAa,CAACrH,KAAK,CAACsH,WAAW,EAAE;IAChE,IAAI,CAACN,eAAe,GAAG,IAAI,CAACT,OAAO,CAAC4G,QAAQ,CAACjG,MAAM,CAACK,MAAM,IACxDA,MAAM,CAACpH,EAAE,CAACC,QAAQ,EAAE,CAACoH,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACpN,IAAI,CAACmN,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,IAAII,MAAM,CAACnB,IAAI,CAACkB,WAAW,EAAE,CAACE,QAAQ,CAACL,WAAW,CAAC,CACjJ;EACH;EAEAiG,gBAAgBA,CAAC7F,MAAW;IAC1B,IAAI,CAACmF,UAAU,CAAChJ,GAAG,CAAC,SAAS,CAAC,EAAE2J,UAAU,CAAC9F,MAAM,CAAC;IAClD,IAAI,CAACmF,UAAU,CAAChJ,GAAG,CAAC,aAAa,CAAC,EAAE2J,UAAU,CAAC9F,MAAM,CAACnB,IAAI,GAAG,KAAK,GAAGmB,MAAM,CAACpN,IAAI,CAAC;EACnF;EAEAqb,UAAUA,CAAA;IACR,MAAMtV,SAAS,GAAGG,YAAY,CAACiE,OAAO,CAAC,WAAW,CAAC;IACnD,MAAMnE,EAAE,GAAGyG,MAAM,CAAC1G,SAAS,CAAC;IAC5B,MAAM9E,KAAK,GAAG,IAAI,CAACsR,UAAU,EAAE1M,KAAK,CAAC6M,OAAO,CAACzG,IAAI,GAAG,GAAG,GAAG,IAAI,CAACsG,UAAU,EAAE1M,KAAK,CAAC6M,OAAO,CAAC1S,IAAI;IAE7F,MAAM8a,OAAO,GAAG,IAAI,CAACvI,UAAU,EAAE1M,KAAK,CAACG,EAAE;IAEzC,MAAMoN,cAAc,GAAG;MACrBpN,EAAE,EAAE,IAAI,CAACuM,UAAU,EAAE1M,KAAK,CAACG,EAAE;MAC7B/E,KAAK,EAAEA,KAAK;MACZuR,WAAW,EAAE,IAAI,CAACD,UAAU,EAAE1M,KAAK,CAAC2M,WAAW;MAC/CgJ,MAAM,EAAExV,EAAE;MACVqN,OAAO,EAAErN,EAAE;MACX0M,OAAO,EAAE,IAAI,CAACH,UAAU,EAAE1M,KAAK,CAAC6M,OAAO;MACvCI,QAAQ,EAAE,IAAI,CAACP,UAAU,EAAE1M,KAAK,CAACiN,QAAQ;MACzCF,WAAW,EAAE,IAAI,CAACL,UAAU,EAAE1M,KAAK,CAAC+M,WAAW;MAC/CD,WAAW,EAAE,IAAI,CAACJ,UAAU,EAAE1M,KAAK,CAAC8M,WAAW;MAC/CE,QAAQ,EAAE,IAAI,CAACN,UAAU,EAAE1M,KAAK,CAACgN;KAClC;IAED,IAAI,CAAC6D,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACtK,OAAO,CAAC4O,cAAc,CAAC5H,cAAc,EAAE0H,OAAO,CAAC,CAACpZ,SAAS,CAC5D6R,QAAQ,IAAG;MACTtR,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEqR,QAAQ,CAAC;MAE7D,IAAI,CAAClS,QAAQ,CAACe,IAAI,CAChB,sCAAsC,EACtC,GAAG,EACH;QAAEC,QAAQ,EAAE;MAAI,CAAE,CACnB;MACD,IAAI,CAAC+J,OAAO,CAACsH,aAAa,CAACH,QAAQ,CAAC;MACpC,IAAI,CAAC5S,MAAM,CAAC4B,QAAQ,CAAC,CAAC,WAAWgR,QAAQ,CAACvN,EAAE,EAAE,CAAC,CAAC;IAClD,CAAC,EACD1D,KAAK,IAAG;MACN,IAAI,CAACoU,OAAO,GAAG,KAAK;MACpBzU,OAAO,CAACK,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,CACF;EACH;EAAC,QAAA3G,CAAA;qBAvHUgB,yBAAyB,EAAA6B,+DAAA,CAAApB,oEAAA,GAAAoB,+DAAA,CAAAsF,uDAAA,GAAAtF,+DAAA,CAAA+R,2DAAA,GAAA/R,+DAAA,CAAA+R,mDAAA,GAAA/R,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAgS,mEAAA,GAAAhS,+DAAA,CAAAiS,8DAAA;EAAA;EAAA,QAAA7U,EAAA;UAAzBe,yBAAyB;IAAAgG,SAAA;IAAAC,SAAA,WAAA6Y,gCAAA3Y,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;gFAFzB,CAAC+O,uDAAc,CAAC,GAAArT,iEAAA;IAAA0F,KAAA;IAAAC,IAAA;IAAAkC,MAAA;IAAAjC,QAAA,WAAAsX,mCAAA5Y,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCxCrBtE,4DAJR,aAA8B,cACI,aACb,wBACoD,gBACtD;QAAAA,oDAAA,iBAAU;QAAAA,0DAAA,EAAY;QACjCA,4DAAA,kBAAoJ;QAArBA,wDAAnB,mBAAAmd,0DAAA;UAAAnd,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAASuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC,mBAAA6O,0DAAA;UAAApd,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAUuE,GAAA,CAAAgK,MAAA,EAAQ;QAAA,EAAC;QAAjJvO,0DAAA,EAAoJ;QACpJA,4DAAA,6BAAoH;QAAzDA,wDAAA,4BAAAqd,8EAAApM,MAAA;UAAAjR,2DAAA,CAAAK,GAAA;UAAA,OAAAL,yDAAA,CAAkBuE,GAAA,CAAAkQ,gBAAA,CAAAxD,MAAA,CAAArC,MAAA,CAAAvH,KAAA,CAAqC;QAAA,EAAC;QACjHrH,wDAAA,KAAAsd,gDAAA,wBAAoE;QAM1Etd,0DAFI,EAAmB,EACJ,EACb;QAGJA,4DADF,yBAAiC,iBACpB;QAAAA,oDAAA,2BAAS;QAAAA,0DAAA,EAAY;QAChCA,uDAAA,oBAA4D;QAC9DA,0DAAA,EAAiB;QAIbA,4DAFJ,cAAiB,sBACC,iBACH;QAAAA,oDAAA,6BAAgB;QAAAA,0DAAA,EAAY;QAErCA,4DADF,sBAA0C,sBAChB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAAAA,0DAAA,EAAa;QACtCA,4DAAA,sBAAwB;QAAAA,oDAAA,SAAC;QAE7BA,0DAF6B,EAAa,EAC3B,EACE;QAGfA,4DADF,0BAAuC,iBAC1B;QAAAA,oDAAA,2BAAc;QAAAA,0DAAA,EAAY;QACrCA,uDAAA,iBAA2D;QAE/DA,0DADE,EAAiB,EACb;QAENA,4DAAA,UAAI;QAAAA,oDAAA,sBAAc;QAAAA,0DAAA,EAAK;QACvBA,4DAAA,4BAAsD;QACpDA,wDAAA,KAAAud,qDAAA,6BAA8D;QAGhEvd,0DAAA,EAAmB;QAEnBA,4DAAA,eAAuB;QACrBA,uDAAA,eAAW;QAITA,wDAFF,KAAAwd,iDAAA,gCAAsB,KAAAC,iDAAA,OAEb;QAKfzd,0DAFI,EAAM,EACD,EACH;;;;QAtDEA,uDAAA,EAAwB;QAAxBA,wDAAA,cAAAuE,GAAA,CAAAwP,UAAA,CAAwB;QAI2D/T,uDAAA,GAAwB;QAAxBA,wDAAA,oBAAA0d,OAAA,CAAwB;QAE1E1d,uDAAA,GAAkB;QAAlBA,wDAAA,YAAAuE,GAAA,CAAA8J,eAAA,CAAkB;QAiBrCrO,uDAAA,IAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QACXA,uDAAA,GAAW;QAAXA,wDAAA,YAAW;QAYMA,uDAAA,GAAa;QAAbA,wDAAA,YAAAuE,GAAA,CAAAsL,UAAA,CAAa;QAQ9C7P,uDAAA,GAIC;QAJDA,2DAAA,KAAAuE,GAAA,CAAA2T,OAAA,oBAIC;;;mBD7BHld,yDAAY,EAAAmX,oDAAA,EACZlM,+DAAmB,EAAAgM,4DAAA,EAAAA,gEAAA,EAAAA,2DAAA,EAAAA,gEAAA,EAAAA,8DAAA,EAAAA,2DAAA,EAInB5L,sEAAY,EACZC,kEAAQ,EACR6M,+DAAS,EACTnN,uDAAW,EAEXoM,6DAAS,EACTa,oEAAc,EACdC,mEAAa,EACb9M,8DAAQ,EACRF,gEAAS,EACT9K,kFAAqB,EAAA2a,4EAAA,EAAAA,mFAAA,EACrB3C,uDAAW,EAAA4C,iDAAA,EACX1C,2EAAqB,EAAAqK,qEAAA;IAAA9Y,MAAA;EAAA;;;;;;;;;;;;;;;AExClB,MAAMqR,WAAW,GAAG;EACvB0H,WAAW,EAAE,IAAI;EACjBzH,aAAa,EAAE,+BAA+B;EAC9C;EACA0H,OAAO,EAAE;CACZ;;;;;;;;;;;;;;;;;;;;;;ACFmD;AACD;AACO;AACH;AACsB;AACI;AACf;AACc;AACV;AACV;AACmB;AAG/EM,+EAAoB,CAAC9b,4DAAY,EAAE;EAC/B+b,SAAS,EAAE,CACPN,kEAAmB,CAACpf,qEAAgB,EAAExB,+EAAqB,EAAEghB,oEAAa,EAAElY,uDAAW,EAAEC,+DAAmB,EAAE1K,iEAAa,EAAE6R,mEAAY,CAAC,EAC1IxD,wFAAwB,EACxBqU,uEAAiB,CAACD,4EAAsB,EAAE,CAAC,EAC3CD,4FAAqB,EAAE;CAE9B,CAAC,CACCjL,KAAK,CAAC1N,GAAG,IAAI3B,OAAO,CAACK,KAAK,CAACsB,GAAG,CAAC,CAAC", "sources": ["./src/app/angular-material.module.ts", "./src/app/app-routing.module.ts", "./src/app/app.component.ts", "./src/app/app.component.html", "./src/app/auth/components/email-confirm/email-confirm.component.ts", "./src/app/auth/components/email-confirm/email-confirm.component.html", "./src/app/auth/components/login/login.component.ts", "./src/app/auth/components/login/login.component.html", "./src/app/auth/components/password-recovery/password-recovery.component.ts", "./src/app/auth/components/password-recovery/password-recovery.component.html", "./src/app/auth/components/register/register.component.ts", "./src/app/auth/components/register/register.component.html", "./src/app/core/helpers/http.interceptor.ts", "./src/app/core/security/auth/auth.service.ts", "./src/app/core/security/auth/storage.service.ts", "./src/app/core/security/guard/auth.guard.ts", "./src/app/shared/components/header/header.component.ts", "./src/app/shared/components/header/header.component.html", "./src/app/shared/shared.module.ts", "./src/app/study-group/my-study-group/my-study-group.component.ts", "./src/app/study-group/my-study-group/my-study-group.component.html", "./src/app/study-group/navigation-service.service.ts", "./src/app/study-group/study-create-group/study-create-group.component.ts", "./src/app/study-group/study-create-group/study-create-group.component.html", "./src/app/study-group/study-group-associate/study-group-associate.component.ts", "./src/app/study-group/study-group-associate/study-group-associate.component.html", "./src/app/study-group/study-group-detail/study-group-detail.component.ts", "./src/app/study-group/study-group-detail/study-group-detail.component.html", "./src/app/study-group/study-group-search-bar/study-group-search.component.ts", "./src/app/study-group/study-group-search-bar/study-group-search-bar.component.html", "./src/app/study-group/study-group-search-item/study-group-search-item.component.ts", "./src/app/study-group/study-group-search-item/study-group-search-item.component.html", "./src/app/study-group/study-group.service.ts", "./src/app/study-group/study-update-group/study-update-group.component.ts", "./src/app/study-group/study-update-group/study-update-group.component.html", "./src/environments/environment.development.ts", "./src/main.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common'\r\nimport { NgModule } from '@angular/core'\r\nimport { OverlayModule } from '@angular/cdk/overlay'\r\nimport { CdkTreeModule } from '@angular/cdk/tree'\r\nimport { PortalModule } from '@angular/cdk/portal'\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete'\r\nimport { MatButtonModule } from '@angular/material/button'\r\nimport { MatButtonToggleModule } from '@angular/material/button-toggle'\r\nimport { MatCardModule } from '@angular/material/card'\r\nimport { MatCheckboxModule } from '@angular/material/checkbox'\r\nimport { MatChipsModule } from '@angular/material/chips'\r\nimport { MatRippleModule } from '@angular/material/core'\r\nimport { MatDividerModule } from '@angular/material/divider'\r\nimport { MatExpansionModule } from '@angular/material/expansion'\r\nimport { MatFormFieldModule } from '@angular/material/form-field'\r\nimport { MatIconModule } from '@angular/material/icon'\r\nimport { MatInputModule } from '@angular/material/input'\r\nimport { MatListModule } from '@angular/material/list'\r\nimport { MatMenuModule } from '@angular/material/menu'\r\nimport { MatPaginatorModule } from '@angular/material/paginator'\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner'\r\nimport { MatSelectModule } from '@angular/material/select'\r\nimport { MatSidenavModule } from '@angular/material/sidenav'\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar'\r\nimport { MatSortModule } from '@angular/material/sort'\r\nimport { MatTableModule } from '@angular/material/table'\r\nimport { MatTabsModule } from '@angular/material/tabs'\r\nimport { MatToolbarModule } from '@angular/material/toolbar'\r\nimport { MatTreeModule } from '@angular/material/tree'\r\nimport { MatBadgeModule } from '@angular/material/badge'\r\nimport { MatGridListModule } from '@angular/material/grid-list'\r\nimport { MatRadioModule } from '@angular/material/radio'\r\nimport { MatDatepickerModule } from '@angular/material/datepicker'\r\nimport { MatTooltipModule } from '@angular/material/tooltip'\r\n\r\nconst materialModules = [\r\n  CdkTreeModule,\r\n  MatAutocompleteModule,\r\n  MatButtonModule,\r\n  MatCardModule,\r\n  MatCheckboxModule,\r\n  MatChipsModule,\r\n  MatDividerModule,\r\n  MatExpansionModule,\r\n  MatIconModule,\r\n  MatInputModule,\r\n  MatListModule,\r\n  MatMenuModule,\r\n  MatProgressSpinnerModule,\r\n  MatPaginatorModule,\r\n  MatRippleModule,\r\n  MatSelectModule,\r\n  MatSidenavModule,\r\n  MatSnackBarModule,\r\n  MatSortModule,\r\n  MatTableModule,\r\n  MatTabsModule,\r\n  MatToolbarModule,\r\n  MatFormFieldModule,\r\n  MatButtonToggleModule,\r\n  MatTreeModule,\r\n  OverlayModule,\r\n  PortalModule,\r\n  MatBadgeModule,\r\n  MatGridListModule,\r\n  MatRadioModule,\r\n  MatDatepickerModule,\r\n  MatTooltipModule\r\n]\r\n\r\n@NgModule({\r\n  imports: [CommonModule, ...materialModules],\r\n  exports: [...materialModules]\r\n})\r\nexport class AngularMaterialModule {}\r\n", "import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { RegisterComponent } from './auth/components/register/register.component';\r\nimport { LoginComponent } from './auth/components/login/login.component';\r\nimport { EmailConfirmComponent } from './auth/components/email-confirm/email-confirm.component';\r\nimport { StudyGroupSearchBarComponent } from './study-group/study-group-search-bar/study-group-search.component';\r\nimport { PasswordRecoveryComponent } from './auth/components/password-recovery/password-recovery.component';\r\nimport { authGuard, discordAssociateGuard, loggedInGuard } from './core/security/guard/auth.guard';\r\nimport { StudyGroupDetailComponent } from './study-group/study-group-detail/study-group-detail.component';\r\nimport { StudyCreateGroupComponent } from './study-group/study-create-group/study-create-group.component';\r\nimport { MyStudyGroupComponent } from './study-group/my-study-group/my-study-group.component';\r\nimport { StudyUpdateGroupComponent } from './study-group/study-update-group/study-update-group.component';\r\nimport { StudyGroupAssociateComponent } from './study-group/study-group-associate/study-group-associate.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'password-recovery',\r\n    component: PasswordRecoveryComponent,\r\n    canActivate: [loggedInGuard],\r\n  },\r\n  { path: 'login', component: LoginComponent, canActivate: [loggedInGuard] },\r\n  // prettier-ignore\r\n  { path: 'register', component: RegisterComponent, canActivate: [loggedInGuard]},\r\n  // prettier-ignore\r\n  { path: 'confirm', component: EmailConfirmComponent, canActivate: [loggedInGuard] },\r\n  {\r\n    path: '',\r\n    canActivate: [authGuard],\r\n    children: [\r\n      { path: 'search', component: StudyGroupSearchBarComponent },\r\n      { path: 'create', component: StudyCreateGroupComponent },\r\n      { path: 'detail/:groupId', component: StudyGroupDetailComponent },\r\n      { path: 'my-study-group', component: MyStudyGroupComponent },\r\n      { path: 'edit', component: StudyUpdateGroupComponent },\r\n      { path: 'associate', component: StudyGroupAssociateComponent },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule {}\r\n", "import { ChangeDetectorRef, Component, OnInit, ViewChild, inject } from '@angular/core';\r\nimport { AuthService } from './core/security/auth/auth.service';\r\nimport { Router, RouterLink, RouterOutlet } from '@angular/router';\r\nimport { UserResponseBasicDto } from './shared/models/user/user-response-basic-dto';\r\nimport { MatNavList, MatListItem } from '@angular/material/list';\r\nimport { MatSidenavContainer, MatSidenav, MatSidenavContent } from '@angular/material/sidenav';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatIconButton } from '@angular/material/button';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Location } from '@angular/common'\r\nimport { NavigationServiceService } from './study-group/navigation-service.service';\r\n\r\n@Component({\r\n    selector: 'app-root',\r\n    templateUrl: './app.component.html',\r\n    styleUrls: ['./app.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        MatToolbar,\r\n        MatIconButton,\r\n        MatIcon,\r\n        MatSidenavContainer,\r\n        MatSidenav,\r\n        MatNavList,\r\n        MatListItem,\r\n        RouterLink,\r\n        MatSidenavContent,\r\n        RouterOutlet,\r\n        MatMenu,\r\n        MatMenuItem,\r\n        MatMenuTrigger,\r\n    ],\r\n})\r\nexport class AppComponent implements OnInit {\r\n  appName: string = 'Bora Estudar UFF';\r\n  title = 'bora-estudar-front';\r\n  isLoggedIn = false;\r\n  user: UserResponseBasicDto | undefined = undefined;\r\n  showBackIcon = false;\r\n  @ViewChild('snav') sidenav!: MatSidenav;\r\n\r\n  private snackBar = inject(MatSnackBar);\r\n  private authService = inject(AuthService);\r\n  public router = inject(Router);\r\n  private cdr = inject(ChangeDetectorRef);\r\n  private navigationService = inject(NavigationServiceService);\r\n\r\n  constructor(\r\n    private location: Location\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.authService.isLoggedIn().subscribe((isLoggedIn) => {\r\n      this.isLoggedIn = isLoggedIn;\r\n      if (isLoggedIn) {\r\n        this.getUser();\r\n      }\r\n      this.cdr.detectChanges();\r\n    });\r\n\r\n    this.router.events.subscribe(() => {\r\n      this.showBackIcon = this.router.url !== '/search';\r\n\r\n      if(this.router.url === '/create'){\r\n        this.appName = 'Criar Grupo';\r\n      } else if(this.router.url === '/my-study-group'){\r\n        this.appName = 'Meus Grupos';\r\n      } else if(this.router.url.startsWith('/edit')){\r\n        this.appName = 'Editar';\r\n      } else if(this.router.url.startsWith('/detail')){\r\n        this.appName = 'Detalhes';\r\n      } else {\r\n        this.appName = 'Bora Estudar UFF';\r\n      }\r\n    });\r\n  }\r\n\r\n  public logout() {\r\n    this.authService.logout().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.router.navigateByUrl('/login');\r\n        this.close();\r\n\r\n        this.snackBar.open(\r\n          'Desconectado com sucesso!',\r\n          'X',\r\n          { duration: 2500 }\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public getUser() {\r\n    this.authService.getUser().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.user = data;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  navigateToSearch(): void {\r\n    this.router.navigate(['/search']);\r\n  }\r\n\r\n  // navigateToSearch(): void {\r\n  //   const previousUrl = this.navigationService.getPreviousUrl();\r\n\r\n  //   if (previousUrl) {\r\n  //       this.router.navigate([previousUrl]);\r\n  //   } else {\r\n  //       this.router.navigate(['/home']);\r\n  //   }\r\n  // }\r\n\r\n  close(){\r\n    if (this.sidenav) {\r\n      this.sidenav.close();\r\n    }\r\n  }\r\n}\r\n", "<body>\r\n  <div class=\"app_container\">\r\n    <mat-toolbar color=\"primary\" position=\"start\" class=\"header_toolbar\">\r\n\r\n      @if (showBackIcon && isLoggedIn === true && this.router.url !== '/associate' ) {\r\n        <button mat-icon-button (click)=\"navigateToSearch()\">\r\n          <mat-icon>arrow_back</mat-icon>\r\n        </button>\r\n      }\r\n\r\n      <span class=\"spacer\"></span>\r\n\r\n      <h1 class=\"app_name\">{{appName}}</h1>\r\n\r\n      <span class=\"spacer\"></span>\r\n\r\n      @if (isLoggedIn === true) {\r\n        <!-- Área do usuário logado com ícone e nome -->\r\n        <div class=\"user-info-container\">\r\n          <button mat-icon-button [matMenuTriggerFor]=\"userMenu\" class=\"user-account-button\">\r\n            <span class=\"user-name-toolbar\">{{ user?.name || 'Usuário' }}</span>\r\n            <!-- <mat-icon>account_circle</mat-icon> -->\r\n          </button>\r\n          <!-- <span class=\"user-name-toolbar\">{{ user?.name || 'Usuário' }}</span> -->\r\n        </div>\r\n\r\n        <!-- Menu dropdown para logout -->\r\n        <mat-menu #userMenu=\"matMenu\">\r\n          <button mat-menu-item (click)=\"logout()\">\r\n            <mat-icon>logout</mat-icon>\r\n            <span>Sair</span>\r\n          </button>\r\n        </mat-menu>\r\n\r\n        <button mat-icon-button (click)=\"snav.toggle()\">\r\n          <mat-icon>menu</mat-icon>\r\n        </button>\r\n      }\r\n    </mat-toolbar>\r\n\r\n    <mat-sidenav-container class=\"sidenav_container\">\r\n      <mat-sidenav #snav mode=\"over\" class=\"mat_sidenav_content\">\r\n        @if (isLoggedIn === true) {\r\n          <mat-nav-list>\r\n            @if(this.router.url !== '/associate'){\r\n              <a mat-list-item routerLink=\"/search\" (click)=\"close()\">Home</a>\r\n              <a mat-list-item routerLink=\"/create\" (click)=\"close()\">Criar Grupos</a>\r\n              <a mat-list-item routerLink=\"/my-study-group\" (click)=\"close()\">Meus Grupos</a>\r\n            }\r\n            <a mat-list-item (click)=\"logout()\">Sair</a>\r\n          </mat-nav-list>\r\n        } @else {\r\n          <mat-nav-list>\r\n            <a mat-list-item routerLink=\"/login\">Login</a>\r\n            <a mat-list-item routerLink=\"/register\">Register</a>\r\n          </mat-nav-list>\r\n        }\r\n      </mat-sidenav>\r\n\r\n      <mat-sidenav-content>\r\n        <router-outlet><main></main></router-outlet>\r\n      </mat-sidenav-content>\r\n    </mat-sidenav-container>\r\n  </div>\r\n</body>\r\n", "import {Component, OnInit} from '@angular/core';\r\nimport {ActivatedRoute} from \"@angular/router\";\r\nimport {AuthService} from \"../../../core/security/auth/auth.service\";\r\n\r\n@Component({\r\n    selector: 'app-email-confirm',\r\n    templateUrl: './email-confirm.component.html',\r\n    styleUrls: ['./email-confirm.component.scss'],\r\n    standalone: true\r\n})\r\nexport class EmailConfirmComponent implements OnInit {\r\n  message: string = '';\r\n\r\n  constructor(private route: ActivatedRoute, private authService: AuthService) {}\r\n\r\n  ngOnInit() {\r\n\r\n    this.route.queryParams.subscribe(params => {\r\n      const token = params['token'];\r\n\r\n      this.authService.confirmEmail(token).subscribe({\r\n        next: (data) => {\r\n          console.log(data);\r\n          this.message = data;\r\n        },\r\n        error: (err) => {\r\n          console.error(err);\r\n          this.message = 'Confirmation failed. The link might be expired or invalid.';\r\n        },\r\n      });\r\n    });\r\n  }\r\n}\r\n", "<p>email-confirm works!</p>\r\n", "import { Component, OnInit, inject } from '@angular/core';\r\nimport { AuthService } from '../../../core/security/auth/auth.service';\r\nimport { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { SigninBody } from '../../models/signin-body';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { MatButton, MatAnchor } from '@angular/material/button';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { Mat<PERSON><PERSON>, MatCardTitle, MatCardContent } from '@angular/material/card';\r\n\r\n@Component({\r\n    selector: 'app-login',\r\n    templateUrl: './login.component.html',\r\n    styleUrls: ['./login.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        Mat<PERSON>ard,\r\n        MatCardTitle,\r\n        MatCardContent,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        MatFormField,\r\n        Mat<PERSON>abe<PERSON>,\r\n        <PERSON>In<PERSON>,\r\n        Mat<PERSON>utton,\r\n        MatAnchor,\r\n        RouterLink,\r\n    ],\r\n})\r\nexport class LoginComponent implements OnInit {\r\n  private router = inject(Router);\r\n  private fb = inject(FormBuilder);\r\n  private authService = inject(AuthService);\r\n  protected loginForm!: FormGroup;\r\n  protected errorMessage = '';\r\n  // isLoggedIn$ = this.authService.isLoggedIn();\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {\r\n    // this.authService.isLoggedIn().subscribe((isLogged) => {\r\n    //   this.isLoggedIn$ = isLogged;\r\n    // });\r\n\r\n    // if (this.isLoggedIn) {\r\n    //   this.router.navigateByUrl('/search');\r\n    // }\r\n\r\n    this.loginForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required]],\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (!this.loginForm.valid) return;\r\n\r\n    const body: SigninBody = {\r\n      email: this.loginForm.controls['email'].value,\r\n      password: this.loginForm.controls['password'].value,\r\n    };\r\n\r\n    this.authService.login(body).subscribe({\r\n      next: (data: any) => {\r\n        console.error('login', data)\r\n        const idUsuario = data.id.toString();\r\n        localStorage.setItem('idUsuario', idUsuario);\r\n        let isDiscordAssociate = data.isDiscordAssociate;\r\n        console.error('isDiscordAssociate', isDiscordAssociate)\r\n        // this.router.navigateByUrl('/search');\r\n\r\n        if(isDiscordAssociate === true){\r\n          this.router.navigateByUrl('/search');\r\n        } else {\r\n          this.router.navigateByUrl('/associate');\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n        this.errorMessage = error.error.message;\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <mat-card>\r\n    <mat-card-title> <PERSON><PERSON> - UFF! </mat-card-title>\r\n\r\n    <mat-card-content>\r\n      <form class=\"login-form\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Email</mat-label>\r\n          <input matInput formControlName=\"email\" type=\"email\" required />\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Senha</mat-label>\r\n          <input matInput formControlName=\"password\" type=\"password\" required />\r\n        </mat-form-field>\r\n\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          class=\"form-button\"\r\n          type=\"submit\"\r\n        >\r\n          Login\r\n        </button>\r\n\r\n        <div class=\"linhaou\">\r\n          <span class=\"linha\"> </span>\r\n          <span class=\"ou\">OU</span>\r\n          <span class=\"linha\"> </span>\r\n        </div>\r\n\r\n        <a\r\n          mat-stroked-button\r\n          color=\"link\"\r\n          class=\"form-button\"\r\n          routerLink=\"/register\"\r\n          >Cadastre-se</a\r\n        >\r\n\r\n        <a\r\n          mat-button\r\n          class=\"form-button\"\r\n          color=\"primary\"\r\n          routerLink=\"/password-recovery\"\r\n          >Esqueceu a senha?</a\r\n        >\r\n      </form>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n", "import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-password-recovery',\r\n  standalone: true,\r\n  imports: [],\r\n  templateUrl: './password-recovery.component.html',\r\n  styleUrl: './password-recovery.component.scss'\r\n})\r\nexport class PasswordRecoveryComponent {\r\n\r\n}\r\n", "<p>password-recovery works!</p>\r\n", "import { Component, OnInit, inject } from '@angular/core';\r\nimport { AuthService } from '../../../core/security/auth/auth.service';\r\nimport { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { SignupBody } from '../../models/signup-body';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { MatButton, MatAnchor } from '@angular/material/button';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { Mat<PERSON>ard, MatCardTitle, MatCardContent } from '@angular/material/card';\r\n\r\n@Component({\r\n    selector: 'app-register',\r\n    templateUrl: './register.component.html',\r\n    styleUrls: ['./register.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        <PERSON><PERSON><PERSON>,\r\n        Mat<PERSON>ardTit<PERSON>,\r\n        Mat<PERSON>ardContent,\r\n        FormsModule,\r\n        ReactiveFormsModule,\r\n        MatFormField,\r\n        MatLabel,\r\n        MatInput,\r\n        MatButton,\r\n        MatAnchor,\r\n        RouterLink,\r\n    ],\r\n})\r\nexport class RegisterComponent implements OnInit {\r\n  private router = inject(Router);\r\n  private fb = inject(FormBuilder);\r\n  private authService = inject(AuthService);\r\n  private snackBar = inject(MatSnackBar);\r\n  protected signupForm!: FormGroup;\r\n  protected errorMessage = '';\r\n  isLoggedIn = false;\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {\r\n    this.authService.isLoggedIn().subscribe((isLogged) => {\r\n      this.isLoggedIn = isLogged;\r\n    });\r\n\r\n    // if (this.authService.isLoggedIn$.pipe()) {\r\n    //   this.router.navigateByUrl('/search');\r\n    // }\r\n\r\n    this.signupForm = this.fb.group({\r\n      name: ['', [Validators.required]],\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required]],\r\n    });\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (!this.signupForm.valid) return;\r\n\r\n    console.log('signup', this.signupForm.value);\r\n\r\n    const body: SignupBody = {\r\n      name: this.signupForm?.value.name,\r\n      email: this.signupForm?.value.email,\r\n      password: this.signupForm?.value.password\r\n    };\r\n\r\n    this.authService.register(body).subscribe({\r\n      next: () => this.onSuccess(),\r\n      error: () => this.onError(),\r\n    });\r\n  }\r\n\r\n  private onSuccess() {\r\n    this.snackBar.open(\r\n      'Registrado com sucesso! Por favor, acesse seu e-mail para confirmar sua conta.',\r\n      'X',\r\n      { duration: 5000 }\r\n    );\r\n    this.signupForm.reset();\r\n    this.router.navigateByUrl('/login');\r\n  }\r\n\r\n  private onError() {\r\n    this.snackBar.open('Erro ao cadastrar.', 'X', { duration: 10000 });\r\n  }\r\n\r\n  private reloadPage(): void {\r\n    window.location.reload();\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <mat-card>\r\n    <mat-card-title> <PERSON><PERSON>uda<PERSON> - UFF! </mat-card-title>\r\n\r\n    <mat-card-content>\r\n      <form [formGroup]=\"signupForm\" (ngSubmit)=\"onSubmit()\">\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Nome</mat-label>\r\n          <input matInput formControlName=\"name\" type=\"text\" required />\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Email</mat-label>\r\n          <input matInput formControlName=\"email\" type=\"email\" required />\r\n        </mat-form-field>\r\n\r\n        <mat-form-field appearance=\"outline\">\r\n          <mat-label>Senha</mat-label>\r\n          <input matInput formControlName=\"password\" type=\"password\" required />\r\n        </mat-form-field>\r\n\r\n        <button\r\n          mat-raised-button\r\n          color=\"primary\"\r\n          class=\"form-button\"\r\n          type=\"submit\"\r\n        >\r\n          Cadastrar\r\n        </button>\r\n\r\n        <a\r\n          mat-stroked-button\r\n          color=\"link\"\r\n          class=\"form-button\"\r\n          routerLink=\"/login\"\r\n          >Voltar\r\n        </a>\r\n      </form>\r\n    </mat-card-content>\r\n  </mat-card>\r\n</div>\r\n", "import { Injectable } from \"@angular/core\";\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { Observable } from \"rxjs\";\r\n\r\n@Injectable()\r\nexport class HttpRequestInterceptor implements HttpInterceptor {\r\n  intercept(\r\n    req: HttpRequest<any>,\r\n    next: HttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    req = req.clone({\r\n      withCredentials: true,\r\n    });\r\n\r\n    return next.handle(req);\r\n  }\r\n}\r\n\r\nexport const httpInterceptorProviders = [\r\n  { provide: HTTP_INTERCEPTORS, useClass: HttpRequestInterceptor, multi: true },\r\n];\r\n", "import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable, inject } from '@angular/core';\r\nimport { Observable, catchError, tap } from 'rxjs';\r\nimport { SigninBody } from 'src/app/auth/models/signin-body';\r\nimport { SignupBody } from 'src/app/auth/models/signup-body';\r\nimport { UserResponseBasicDto } from 'src/app/shared/models/user/user-response-basic-dto';\r\nimport { StorageService } from './storage.service';\r\nimport { environment } from '../../../../environments/environment';\r\n\r\nconst AUTH_API = '';\r\n\r\nconst httpOptions = {\r\n  headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n};\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  private readonly http = inject(HttpClient);\r\n  private readonly storageService = inject(StorageService);\r\n\r\n  login(body: SigninBody): Observable<UserResponseBasicDto> {\r\n    return this.http\r\n      .post<UserResponseBasicDto>(AUTH_API.concat('/signin'), body, httpOptions)\r\n      .pipe(\r\n        tap((user) => {\r\n          this.storageService.saveUser(user);\r\n        }),\r\n        catchError((error) => {\r\n          console.log(`Error on login: ${error.message}`);\r\n          this.storageService.clear();\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  register(body: SignupBody): Observable<UserResponseBasicDto> {\r\n    return this.http\r\n      .post<UserResponseBasicDto>(AUTH_API.concat('/signup'), body, httpOptions)\r\n      .pipe(\r\n        catchError((error) => {\r\n          console.log(`Error on login: ${error.message}`);\r\n          this.storageService.clear();\r\n          throw error;\r\n        })\r\n      );\r\n  }\r\n\r\n  confirmEmail(token: string): Observable<any> {\r\n    return this.http.get(\r\n      AUTH_API.concat(`/confirm?token=${token}`),\r\n      httpOptions\r\n    );\r\n  }\r\n\r\n  logout(): Observable<any> {\r\n    return this.http.post(AUTH_API + '/signout', {}, httpOptions).pipe(\r\n      tap(() => {\r\n        this.storageService.clear();\r\n      }),\r\n      catchError((error) => {\r\n        console.log(`Error on login: ${error.message}`);\r\n        this.storageService.clear();\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  getUser(): Observable<UserResponseBasicDto> {\r\n    return this.storageService.getUser();\r\n  }\r\n\r\n  isLoggedIn(): Observable<boolean> {\r\n    return this.storageService.isLoggedIn();\r\n  }\r\n}\r\n", "import { Injectable, inject } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { UserResponseBasicDto } from 'src/app/shared/models/user/user-response-basic-dto';\r\nimport { of } from 'rxjs';\r\n\r\nconst SIGNED_USER = 'signed-user';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class StorageService {\r\n  private signedUser = new BehaviorSubject<UserResponseBasicDto | undefined>(\r\n    undefined\r\n  );\r\n  private hasSignedUser = new BehaviorSubject<boolean>(false);\r\n\r\n  constructor() {\r\n    this.restoreLocalStorage();\r\n  }\r\n\r\n  clear(): void {\r\n    localStorage.clear();\r\n    this.signedUser.next(undefined);\r\n    this.hasSignedUser.next(false);\r\n  }\r\n\r\n  public saveUser(user: UserResponseBasicDto): void {\r\n    localStorage.removeItem(SIGNED_USER);\r\n    localStorage.setItem(SIGNED_USER, JSON.stringify(user));\r\n    this.signedUser.next(user);\r\n    this.hasSignedUser.next(true);\r\n  }\r\n\r\n  public getUser(): Observable<UserResponseBasicDto> {\r\n    const signedUserString = localStorage.getItem(SIGNED_USER);\r\n    if (!signedUserString) {\r\n      throw new Error(`No signed user found in the session storage.`);\r\n    }\r\n\r\n    const signedUser: UserResponseBasicDto = JSON.parse(signedUserString);\r\n    return of(signedUser);\r\n  }\r\n\r\n  public isLoggedIn(): Observable<boolean> {\r\n    return this.hasSignedUser.asObservable();\r\n  }\r\n\r\n  private restoreLocalStorage(): void {\r\n    if (!localStorage.getItem(SIGNED_USER)) {\r\n      return;\r\n    }\r\n\r\n    const signedUserStr = localStorage.getItem(SIGNED_USER)!;\r\n    const signedUserObj = JSON.parse(signedUserStr);\r\n\r\n    this.signedUser.next(signedUserObj);\r\n    this.hasSignedUser.next(true);\r\n  }\r\n}\r\n", "import {\r\n  ActivatedRouteSnapshot,\r\n  CanActivateFn,\r\n  Router,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from '@angular/router';\r\nimport { inject } from '@angular/core';\r\nimport { AuthService } from '../auth/auth.service';\r\n\r\n/**\r\n * A guard that prevents access to a route if the user is already logged in.\r\n * If the user is logged in, it redirects to the '/search' route.\r\n * If the user is not logged in, it allows access to the route.\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\r\n */\r\nexport const loggedInGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The loggedInGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n  let isLoggedIn = false;\r\n\r\n  authService.isLoggedIn().subscribe((b) => {\r\n    isLoggedIn = b;\r\n  });\r\n\r\n  if (isLoggedIn) {\r\n    router.navigateByUrl('/search');\r\n    return false;\r\n  }\r\n  return true;\r\n};\r\n\r\n/**\r\n * A guard that checks if the user is authenticated before allowing access to a route.\r\n * If the user is not authenticated, it redirects to the login page.\r\n *\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean indicating whether the user is authenticated or not, or a UrlTree to redirect to the login page.\r\n */\r\nexport const authGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The authGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n  let isLoggedIn = false;\r\n\r\n  authService.isLoggedIn().subscribe((b) => {\r\n    isLoggedIn = b;\r\n  });\r\n\r\n  if (!isLoggedIn) {\r\n    router.navigateByUrl('/login');\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\n/**\r\n * A guard that prevents access to a route if the user is already logged in.\r\n * If the user is logged in, it redirects to the '/search' route.\r\n * If the user is not logged in, it allows access to the route.\r\n * @param _route - The activated route snapshot.\r\n * @param _state - The router state snapshot.\r\n * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.\r\n */\r\nexport const discordAssociateGuard: CanActivateFn = (\r\n  _route: ActivatedRouteSnapshot,\r\n  _state: RouterStateSnapshot\r\n): boolean | UrlTree => {\r\n  console.log('The discordAssociateGuard is being called correctly');\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n\r\n  const signed = localStorage.getItem('signed-user');\r\n\r\n  if (signed) {\r\n    const signedUser = JSON.parse(signed);\r\n    const isDiscordAssociate = signedUser.isDiscordAssociate;\r\n    if(!isDiscordAssociate){\r\n      router.navigateByUrl('/associate');\r\n    }\r\n  } else {\r\n    console.error('Usuário não encontrado no localStorage');\r\n  }\r\n  // router.navigateByUrl('/search');\r\n  return true;\r\n};\r\n", "import { Component } from '@angular/core';\r\nimport { Mat<PERSON>av<PERSON>ist, MatListItem } from '@angular/material/list';\r\nimport { MatSidenavContainer, MatSidenav } from '@angular/material/sidenav';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatIconButton } from '@angular/material/button';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\n\r\n@Component({\r\n    selector: 'app-header',\r\n    // standalone: true,\r\n    templateUrl: './header.component.html',\r\n    styleUrl: './header.component.scss',\r\n    standalone: true,\r\n    imports: [\r\n        MatToolbar,\r\n        MatIconButton,\r\n        MatIcon,\r\n        MatSidenavContainer,\r\n        MatSidenav,\r\n        MatNavList,\r\n        MatListItem,\r\n    ],\r\n})\r\nexport class HeaderComponent {\r\n\r\n  fillerNav = Array.from({ length: 50 }, (_, i) => `Nav Item ${i + 1}`);\r\n\r\n  // fillerContent = Array.from(\r\n  //   { length: 3 },\r\n  //   () =>\r\n  //     `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut\r\n  //      labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco\r\n  //      laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in\r\n  //      voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat\r\n  //      cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.`\r\n  // );\r\n\r\n}\r\n", "<div class=\"header_container\">\r\n  <mat-toolbar color=\"primary\" position=\"start\" class=\"header_toolbar\">\r\n    <button mat-icon-button (click)=\"snav.toggle()\">\r\n      <mat-icon>menu</mat-icon>\r\n    </button>\r\n    <span class=\"spacer\"></span>\r\n    <h1 class=\"app_name\">Bora Estudar UFF</h1>\r\n  </mat-toolbar>\r\n\r\n  <mat-sidenav-container class=\"sidenav_container\">\r\n    <mat-sidenav\r\n      #snav\r\n      mode=\"side\"\r\n      class=\"mat_sidenav_content\"\r\n    >\r\n      <mat-nav-list>\r\n        @for (nav of fillerNav; track nav) {\r\n        <a mat-list-item routerLink=\".\">{{ nav }}</a>\r\n        }\r\n      </mat-nav-list>\r\n    </mat-sidenav>\r\n  </mat-sidenav-container>\r\n</div>\r\n", "import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HeaderComponent } from './components/header/header.component';\r\nimport { AngularMaterialModule } from '../angular-material.module';\r\n\r\n@NgModule({\r\n    imports: [CommonModule, AngularMaterialModule, HeaderComponent],\r\n    exports: [HeaderComponent],\r\n})\r\nexport class SharedModule {\r\n}\r\n", "import { ChangeDetector<PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON>ement<PERSON>ef, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { NgFor } from '@angular/common';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatButton } from '@angular/material/button';\r\nimport { MatCheckbox, MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { StudyGroupSearchItemComponent } from '../study-group-search-item/study-group-search-item.component';\r\nimport { StudyGroupSearchListComponent } from '../study-group-search-list/study-group-search-list.component';\r\n\r\n@Component({\r\n  selector: 'app-my-study-group',\r\n  standalone: true,\r\n  imports: [\r\n    MatFormField,\r\n    MatLabel,\r\n    MatInput,\r\n    MatButton,\r\n    MatIcon,\r\n    MatMenuModule,\r\n    MatCheckboxModule,\r\n    StudyGroupSearchListComponent,\r\n    NgFor,\r\n    StudyGroupSearchItemComponent,\r\n    MatAutocompleteModule\r\n  ],\r\n  templateUrl: './my-study-group.component.html',\r\n  styleUrl: './my-study-group.component.scss'\r\n})\r\nexport class MyStudyGroupComponent implements OnInit {\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  selectedDays: Set<string> = new Set();\r\n  selectedHour: string = '';\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('time') time!: ElementRef<HTMLInputElement>;\r\n  @ViewChildren(MatCheckbox) checkboxes!: QueryList<MatCheckbox>;\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    const id = Number(idUsuario);\r\n\r\n    this.service.getStudyGroupsFind(id).subscribe((dados) => {\r\n      console.log('Dados carregados:', dados);\r\n      this.service.myStudyGroups = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.myStudyGroups.filter(option =>\r\n      option.title.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  applyFilters(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n\r\n    // Dividir o valor do filtro em partes, se necessário\r\n    const [codeFilter, titleFilter] = filterValue.split(' - ').map(part => part.trim());\r\n\r\n    const filter = this.service.myStudyGroups?.filter(option =>\r\n      this.filterByDayOfWeek(option) &&\r\n      this.filterByHour(option) &&\r\n      (option.code.toLowerCase().includes(codeFilter) ||\r\n       option.title.toLowerCase().includes(titleFilter))\r\n    ) || [];\r\n\r\n    this.options = [...filter];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.input.nativeElement.value = '';\r\n    this.time.nativeElement.value = '';\r\n    this.checkboxes.forEach(checkbox => checkbox.checked = false);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  filterByDayOfWeek(option: any): boolean {\r\n    if (!option.daysOfWeek || this.selectedDays.size === 0) {\r\n      return true; // Sem filtro de dia da semana ou dados não definidos\r\n    }\r\n    return option.daysOfWeek.some((day: string) => this.selectedDays.has(day.toLowerCase()));\r\n  }\r\n\r\n  filterByHour(option: any): boolean {\r\n    if (!this.selectedHour) {\r\n      return true; // Sem filtro de horário\r\n    }\r\n    return option.hour >= this.selectedHour;\r\n  }\r\n\r\n  days(day: string): void {\r\n    if (this.selectedDays.has(day)) {\r\n      this.selectedDays.delete(day);\r\n    } else {\r\n      this.selectedDays.add(day);\r\n    }\r\n  }\r\n\r\n  onHourChange(event: any): void {\r\n    this.selectedHour = event.target.value;\r\n  }\r\n}\r\n", "<div class=\"search_container\">\r\n  <div class=\"search_row\">\r\n    <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n      <mat-label>Disciplina</mat-label>\r\n      <input #input matInput class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n      <mat-autocomplete requireSelection #auto=\"matAutocomplete\">\r\n        <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.title\">\r\n          ({{ option.code }}) {{ option.title }}\r\n        </mat-option>\r\n      </mat-autocomplete>\r\n    </mat-form-field>\r\n\r\n    <button\r\n      mat-raised-button\r\n      color=\"primary\"\r\n      class=\"search_row_search_item\"\r\n      (click)=\"applyFilters()\">\r\n      <mat-icon class=\"search_row_icon\">search</mat-icon>\r\n    </button>\r\n\r\n    <button\r\n    mat-raised-button\r\n    color=\"primary\"\r\n    class=\"search_row_search_item\"\r\n    (click)=\"clearFilters()\">Limpar Filtro\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"search_row\">\r\n    <button mat-button [matMenuTriggerFor]=\"menu\" class=\"pesquisarButton\"><PERSON><PERSON> da <PERSON></button>\r\n    <mat-menu #menu=\"matMenu\" style=\"max-width: auto !important;\">\r\n      <div class=\"position\">\r\n        <section style=\"display: flex;\">\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('dom')\">DOM</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('seg')\">SEG</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('ter')\">TER</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qua')\">QUA</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qui')\">QUI</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sex')\">SEX</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sab')\">SAB</mat-checkbox>\r\n        </section>\r\n      </div>\r\n    </mat-menu>\r\n\r\n    <button mat-button [matMenuTriggerFor]=\"menuHora\" class=\"pesquisarButton\">Hora de Início</button>\r\n    <mat-menu #menuHora=\"matMenu\" class=\"position\">\r\n      <div class=\"position\">\r\n        <mat-form-field appearance=\"outline\" class=\"input6\" (click)=\"$event.stopPropagation()\">\r\n          <mat-label>A partir de:</mat-label>\r\n          <input #time matInput type=\"time\" (change)=\"onHourChange($event)\"/>\r\n        </mat-form-field>\r\n      </div>\r\n    </mat-menu>\r\n  </div>\r\n\r\n  <div class=\"container\">\r\n    <div *ngFor=\"let groups of options\">\r\n      <app-study-group-search-item [studyGroup]=\"groups\"></app-study-group-search-item>\r\n    </div>\r\n  </div>\r\n</div>\r\n", "import { Injectable } from '@angular/core';\r\nimport { Router, NavigationStart } from '@angular/router';\r\nimport { filter } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NavigationServiceService {\r\n\r\n\r\n  private previousUrls: string[] = [];\r\n  private currentUrl: string | null = null;\r\n\r\n  constructor(private router: Router) {\r\n    this.router.events\r\n      .pipe(\r\n        filter(event => event instanceof NavigationStart)\r\n      )\r\n      .subscribe((event) => {\r\n        const navigationStartEvent = event as NavigationStart;\r\n        if (this.currentUrl) {\r\n          this.previousUrls.push(this.currentUrl);\r\n        }\r\n        this.currentUrl = navigationStartEvent.url;\r\n        console.log('Current URL:', this.currentUrl); // Debugging log\r\n        console.log('Previous URLs:', this.previousUrls); // Debugging log\r\n      });\r\n  }\r\n\r\n  public getPreviousUrl(): string | null {\r\n    return this.previousUrls.length > 0 ? this.previousUrls[this.previousUrls.length - 1] : null;\r\n  }\r\n\r\n  public navigateToPrevious(): void {\r\n    const previousUrl = this.getPreviousUrl();\r\n    if (previousUrl) {\r\n      this.router.navigateByUrl(previousUrl).catch(error => console.error('Navigation error:', error));\r\n      // Remove the last entry as we navigated to it\r\n      this.previousUrls.pop();\r\n    } else {\r\n      this.router.navigate(['/search']).catch(error => console.error('Navigation error:', error));\r\n    }\r\n  }\r\n}\r\n", "import { CommonModule, NgFor } from '@angular/common';\r\nimport { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatIconButton, MatButton } from '@angular/material/button';\r\nimport { MatChipListbox, MatChipOption } from '@angular/material/chips';\r\nimport { MatOption } from '@angular/material/core';\r\nimport { MatDialogRef } from '@angular/material/dialog';\r\nimport { MatForm<PERSON>ield, MatLabel } from '@angular/material/form-field';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatSelect } from '@angular/material/select';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { Router } from '@angular/router';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Subscription } from 'rxjs';\r\n\r\n\r\n@Component({\r\n  selector: 'app-study-create-group',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatToolbar,\r\n    MatIconButton,\r\n    MatIcon,\r\n    MatFormField,\r\n    MatLabel,\r\n    MatSelect,\r\n    FormsModule,\r\n    NgFor,\r\n    MatOption,\r\n    MatChipListbox,\r\n    MatChipOption,\r\n    MatInput,\r\n    MatButton,\r\n    MatAutocompleteModule,\r\n    ToastModule,\r\n    ProgressSpinnerModule,\r\n  ],\r\n  templateUrl: './study-create-group.component.html',\r\n  styleUrl: './study-create-group.component.scss',\r\n  providers: [MessageService],\r\n})\r\n\r\nexport class StudyCreateGroupComponent implements OnInit {\r\n  formulario!: UntypedFormGroup;\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n\r\n  protected daysOfWeek: { id: number; name: string }[] = [\r\n    { id: 1, name: 'Dom' },\r\n    { id: 2, name: 'Seg' },\r\n    { id: 3, name: 'Ter' },\r\n    { id: 4, name: 'Qua' },\r\n    { id: 5, name: 'Qui' },\r\n    { id: 6, name: 'Sex' },\r\n    { id: 7, name: 'Sáb' },\r\n  ];\r\n\r\n  protected modalities: { value: string; viewValue: string }[] = [\r\n    { value: 'presencial', viewValue: 'Presencial' },\r\n    { value: 'online', viewValue: 'Online' },\r\n    { value: 'hibrido', viewValue: 'Híbrido' },\r\n  ];\r\n\r\n  protected selectedModality: String | null = null;\r\n\r\n  constructor(\r\n    private snackBar: MatSnackBar,\r\n    private messageService: MessageService,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private builder: UntypedFormBuilder\r\n  ) {\r\n    this.formulario = this.builder.group({\r\n      title: [''],\r\n      description: [''],\r\n      campoOculto: [''],\r\n      subject: [''],\r\n      maxStudents: [''],\r\n      meetingTime: [''],\r\n      modality: ['REMOTE'],\r\n      weekdays: [''],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.service.getSubjects().subscribe((dados) => {\r\n      console.error('Dados carregados:', dados);\r\n      this.service.subjects = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    });\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.subjects.filter(\r\n      (option) =>\r\n        option.id.toString().includes(filterValue) ||\r\n        option.name.toLowerCase().includes(filterValue) ||\r\n        option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  onOptionSelected(option: any): void {\r\n    this.formulario.get('subject')?.patchValue(option);\r\n    this.formulario\r\n      .get('campoOculto')\r\n      ?.patchValue(option.code + ' - ' + option.name);\r\n  }\r\n\r\n  private subscription: Subscription | null = null;\r\n\r\n  createGroups() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    const id = Number(idUsuario);\r\n    const title =\r\n      this.formulario?.value.subject.code +\r\n      ' ' +\r\n      this.formulario?.value.subject.name;\r\n\r\n    const studyGroupData = {\r\n      title: title,\r\n      description: this.formulario?.value.description,\r\n      ownerId: id,\r\n      subject: this.formulario?.value.subject,\r\n      weekdays: this.formulario?.value.weekdays,\r\n      meetingTime: this.formulario?.value.meetingTime,\r\n      maxStudents: this.formulario?.value.maxStudents,\r\n      modality: this.formulario?.value.modality,\r\n    };\r\n\r\n    this.subscription = this.service\r\n      .createStudyGroup(studyGroupData)\r\n      .subscribe({\r\n        next: (response) => {\r\n          console.error('Grupo de estudo criado com sucesso:', response);\r\n\r\n          const mappedStudyGroup = this.service.mappingStudyGroup(response);\r\n          this.service.setStudyGroup(mappedStudyGroup);\r\n\r\n          this.snackBar.open('Grupo de estudo criado com sucesso!', 'X', {\r\n            duration: 2500,\r\n          });\r\n          this.router.navigate([`/detail/${response.id}`]);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erro ao criar grupo de estudo:', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscription?.unsubscribe(); // Cancela a assinatura ao destruir o componente\r\n  }\r\n}\r\n", "<div class=\"filter-container\">\r\n  <form [formGroup]=\"formulario\" >\r\n    <div class=\"row\">\r\n      <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n        <mat-label>Disciplina</mat-label>\r\n        <input #input matInput formControlName=\"campoOculto\" class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n        <mat-autocomplete requireSelection #auto=\"matAutocomplete\" (optionSelected)=\"onOptionSelected($event.option.value)\">\r\n          <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option\">\r\n          <!-- <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.name\"> -->\r\n            ({{ option.code }}) {{ option.name }}\r\n          </mat-option>\r\n        </mat-autocomplete>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <mat-form-field class=\"input100\">\r\n      <mat-label>Descrição</mat-label>\r\n      <textarea matInput formControlName=\"description\"></textarea>\r\n    </mat-form-field>\r\n\r\n    <div class=\"row\">\r\n      <mat-form-field>\r\n        <mat-label>Número de Alunos</mat-label>\r\n        <mat-select formControlName=\"maxStudents\">\r\n          <mat-option [value]=\"2\">2</mat-option>\r\n          <mat-option [value]=\"3\">3</mat-option>\r\n          <mat-option [value]=\"4\">4</mat-option>\r\n          <mat-option [value]=\"5\">5</mat-option>\r\n          <mat-option [value]=\"6\">6</mat-option>\r\n        </mat-select>\r\n      </mat-form-field>\r\n\r\n      <mat-form-field class=\"margin input30\">\r\n        <mat-label>Hora de Início</mat-label>\r\n        <input matInput type=\"time\" formControlName=\"meetingTime\"/>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <h2>Dias da Semana</h2>\r\n    <mat-chip-listbox formControlName=\"weekdays\" multiple>\r\n      <mat-chip-option *ngFor=\"let day of daysOfWeek\" [value]=\"day\">\r\n        {{ day.name }}\r\n      </mat-chip-option>\r\n    </mat-chip-listbox>\r\n\r\n    <div class=\"allCenter\">\r\n      <p-toast />\r\n\r\n      <button mat-raised-button color=\"primary\" class=\"input100\" (click)=\"createGroups()\">Criar Grupo</button>\r\n    </div>\r\n  </form>\r\n</div>\r\n", "import { Component, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON><PERSON> } from '@angular/material/button';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-study-group-associate',\r\n  standalone: true,\r\n  imports: [\r\n    Mat<PERSON>utt<PERSON>\r\n  ],\r\n  templateUrl: './study-group-associate.component.html',\r\n  styleUrl: './study-group-associate.component.scss'\r\n})\r\nexport class StudyGroupAssociateComponent implements OnInit {\r\n  private encodedApiUrl = environment.encodedApiUrl;\r\n\r\n  constructor(\r\n    public service: StudyGroupService){}\r\n\r\n  ngOnInit(): void {\r\n\r\n  }\r\n\r\n  associate() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    // const url = `https://discord.com/oauth2/authorize?client_id=1237632955145257021&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fdiscord%2Fusers&scope=identify&state=${idUsuario}`;\r\n    const url = `https://discord.com/oauth2/authorize?client_id=1237632955145257021&response_type=code&redirect_uri=${this.encodedApiUrl}%2Fdiscord%2Fusers&scope=identify&state=${idUsuario}`;\r\n    https: window.open(url, '_blank');\r\n  }\r\n\r\n}\r\n", "<div class=\"m-padding\">\r\n  <div class=\"allCenter\">\r\n    <p class=\"titulo\"><b>Associe sua conta no Discord para utilizar o Bora Estudar - UFF!</b></p>\r\n  </div>\r\n\r\n  <div class=\"justify\">\r\n    <p>Para utilizar todas as funções do Bora Estudar - UFF, é necessário que você associe uma conta no Discord. Clique no botão abaixo para fazer a associação e comece a utilizar.</p>\r\n  </div>\r\n\r\n  <button mat-raised-button color=\"primary\" class=\"full_width m-top\" (click)=\"associate()\">Associe sua Conta!</button>\r\n</div>\r\n", "import { Component, Inject, Input, OnInit, inject } from '@angular/core';\r\nimport { StudyGroup } from '../study-group';\r\nimport { StudyGroupMockService } from '../study-group-mock.service';\r\nimport { ActivatedRoute, ParamMap, Router, RouterLink } from '@angular/router';\r\nimport { Observable, catchError, of } from 'rxjs';\r\nimport { MatButton, MatIconButton } from '@angular/material/button';\r\nimport { NgFor, AsyncPipe, TitleCasePipe, CommonModule } from '@angular/common';\r\nimport { MatChipSet, MatChip } from '@angular/material/chips';\r\nimport { MatCard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, MatListItem } from '@angular/material/list';\r\nimport { <PERSON><PERSON>idenavContainer, MatSidenav } from '@angular/material/sidenav';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-study-group-detail',\r\n  templateUrl: './study-group-detail.component.html',\r\n  styleUrls: ['./study-group-detail.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    MatToolbar,\r\n    MatCard,\r\n    MatCardHeader,\r\n    MatCardTitle,\r\n    MatCardSubtitle,\r\n    MatCardContent,\r\n    MatChipSet,\r\n    NgFor,\r\n    MatChip,\r\n    MatCardActions,\r\n    MatButton,\r\n    RouterLink,\r\n    AsyncPipe,\r\n    TitleCasePipe,\r\n    MatIcon,\r\n    MatIconButton,\r\n    MatSidenavContainer,\r\n    MatSidenav,\r\n    MatNavList,\r\n    MatListItem,\r\n    ToastModule,\r\n    ProgressSpinnerModule,\r\n  ],\r\n  providers: [MessageService],\r\n})\r\nexport class StudyGroupDetailComponent implements OnInit {\r\n  studyGroup: any;\r\n  diasSemana = ['dom', 'seg', 'ter', 'qua', 'qui', 'sex', 'sáb'];\r\n  userInGroup!: boolean;\r\n  isOwnerId!: boolean;\r\n  loading: boolean = false;\r\n  discordInviteUrl: string | null = null;\r\n\r\n  constructor(\r\n    private snackBar: MatSnackBar,\r\n    private router: Router,\r\n    public service: StudyGroupService,\r\n    private route: ActivatedRoute,\r\n    private messageService: MessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.studyGroup = this.service.getStudyGroup();\r\n    this.callGroup();\r\n  }\r\n\r\n  openDiscord() {\r\n    if (this.discordInviteUrl) {\r\n      window.open(this.discordInviteUrl, '_blank', 'noopener,noreferrer');\r\n    } else {\r\n      this.snackBar.open('Link do Discord não disponível', 'Fechar', {\r\n        duration: 3000,\r\n        panelClass: ['error-snackbar'],\r\n      });\r\n    }\r\n  }\r\n\r\n  joinGroup() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n\r\n    if (idUsuario !== null) {\r\n      const id = Number(idUsuario);\r\n      this.service.joinGroupService(this.studyGroup.id, id).subscribe({\r\n        next: (response) => {\r\n          this.snackBar.open('Entrou no grupo com sucesso!', 'X', {\r\n            duration: 2500,\r\n          });\r\n\r\n          this.loading = false;\r\n\r\n          this.callGroup();\r\n        },\r\n        error: (error) => {\r\n          console.error('Erro ao entrar no grupo:', error);\r\n        },\r\n      });\r\n    } else {\r\n      console.error('ID do usuário não encontrado no localStorage');\r\n    }\r\n  }\r\n\r\n  leaveGroup() {\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    this.loading = true;\r\n\r\n    if (idUsuario !== null) {\r\n      const id = Number(idUsuario);\r\n      this.service.leaveGroupService(this.studyGroup.id, id).subscribe({\r\n        next: (resposta) => {\r\n          this.snackBar.open('Saiu do grupo com sucesso!', 'X', {\r\n            duration: 5000,\r\n          });\r\n          this.loading = false;\r\n\r\n          this.callGroup();\r\n        },\r\n        error: (error) => {\r\n          console.error('Erro ao sair do grupo:', error);\r\n          this.loading = false;\r\n          this.snackBar.open('Erro ao sair do grupo!', '', { duration: 5000 });\r\n        },\r\n      });\r\n    } else {\r\n      console.error('ID do usuário não encontrado no localStorage');\r\n    }\r\n  }\r\n\r\n  editGroup() {\r\n    if (this.studyGroup) {\r\n      this.router.navigate(['/edit'], {\r\n        queryParams: { id: this.studyGroup.id },\r\n      });\r\n    }\r\n  }\r\n\r\n  callGroup() {\r\n    const idParam = this.route.snapshot.paramMap.get('groupId');\r\n    const idDetail = idParam ? Number(idParam) : null;\r\n\r\n    if (idDetail !== null) {\r\n      this.service.getStudyGroupId(idDetail).subscribe({\r\n        next: (response) => {\r\n          console.error('grupo de estudo - response:', response);\r\n          const mappedStudyGroup = this.service.mappingStudyGroup(response);\r\n          this.studyGroup = mappedStudyGroup;\r\n          this.discordInviteUrl = this.studyGroup.discordInviteUrl; // Assumindo que o invite está nesta propriedade\r\n\r\n          console.error('grupo de estudo - detalhe:', mappedStudyGroup);\r\n\r\n          const idUsuario = localStorage.getItem('idUsuario');\r\n          const id = Number(idUsuario);\r\n\r\n          if (this.studyGroup && Array.isArray(this.studyGroup.students)) {\r\n            const isStudentInGroup = this.studyGroup.students.some(\r\n              (student: any) => student.id === id\r\n            );\r\n            this.userInGroup = isStudentInGroup;\r\n\r\n            const isOwner = this.studyGroup.ownerId === id;\r\n            this.isOwnerId = isOwner;\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.router.navigate([`/search`]);\r\n        },\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<!-- <mat-toolbar color=\"primary\" position=\"start\" class=\"header_toolbar\">\r\n  <button mat-icon-button (click)=\"close()\">\r\n    <mat-icon>arrow_back</mat-icon>\r\n  </button>\r\n\r\n  <span class=\"spacer\"></span>\r\n\r\n  <span>Detalhes</span>\r\n\r\n  <span class=\"spacer\"></span>\r\n</mat-toolbar> -->\r\n\r\n<div class=\"allGrid m-top\">\r\n  <mat-card-title class=\"titulo\">{{ studyGroup?.code + ' - ' + studyGroup?.title }}</mat-card-title>\r\n  <mat-card-subtitle class=\"subTitulo\">{{ studyGroup?.shortDescription }}</mat-card-subtitle>\r\n\r\n  <div class=\"allBetween m-top\">\r\n    <p><strong>Alunos:</strong> {{ studyGroup?.participants }}</p>\r\n    <p><strong><PERSON>ra de Início:</strong> {{ studyGroup?.hour }}</p>\r\n  </div>\r\n\r\n  <div>\r\n    <p><strong>Dias da Semana</strong></p>\r\n    <mat-chip-set>\r\n      <mat-chip *ngFor=\"let day of diasSemana\" [ngClass]=\"{'selected': studyGroup?.daysOfWeek.includes(day)}\">\r\n        <p [ngClass]=\"{'selectedText': studyGroup?.daysOfWeek.includes(day)}\" style=\"margin-top: 12px;\">{{ day | titlecase }}</p>\r\n      </mat-chip>\r\n    </mat-chip-set>\r\n  </div>\r\n\r\n  @if(loading === true){\r\n    <p-progressSpinner class=\"spinerCenter\" ariaLabel=\"loading\" />\r\n  } @else {\r\n    @if(this.userInGroup === true){\r\n      <button mat-raised-button\r\n              color=\"primary\"\r\n              class=\"full_width mtop\"\r\n              (click)=\"openDiscord()\"\r\n              [disabled]=\"!discordInviteUrl\">\r\n        <mat-icon fontIcon=\"discord\"></mat-icon>\r\n        Entrar no Discord\r\n      </button>\r\n\r\n      @if(this.isOwnerId === true){\r\n        <button mat-raised-button color=\"primary\" class=\"full_width mtop\" (click)=\"editGroup()\">Editar</button>\r\n      }\r\n      <button mat-raised-button class=\"full_width mtop\" (click)=\"leaveGroup()\">Sair</button>\r\n    } @else {\r\n      <button mat-raised-button color=\"primary\" class=\"full_width mtop\" (click)=\"joinGroup()\">Entrar</button>\r\n    }\r\n  }\r\n</div>\r\n\r\n<p-toast />\r\n\r\n<!-- <div class=\"container\">\r\n  @if( studyGroup$ | async ; as studyGroup) { @if(!!studyGroup){\r\n  <mat-card>\r\n    <mat-card-header>\r\n      <mat-card-title>{{ studyGroup.title }}</mat-card-title>\r\n      <mat-card-subtitle>{{ studyGroup.shortDescription }}</mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p><strong>Monitor:</strong> {{ studyGroup.monitor }}</p>\r\n      <p><strong>Alunos:</strong> {{ studyGroup.participants }}</p>\r\n      <p><strong>Modalidade:</strong> {{ studyGroup.modality | titlecase }}</p>\r\n      <p><strong>Horario:</strong> {{ studyGroup.hour }}</p>\r\n      <mat-chip-set>\r\n        <mat-chip *ngFor=\"let day of studyGroup.daysOfWeek\">{{\r\n          day | titlecase\r\n        }}</mat-chip>\r\n      </mat-chip-set>\r\n    </mat-card-content>\r\n    <mat-card-actions>\r\n      <button\r\n        mat-raised-button\r\n        color=\"secondary\"\r\n        class=\"full_width\"\r\n        [routerLink]=\"['/study-group', studyGroup.id]\"\r\n      >\r\n        Detalhes\r\n      </button>\r\n      <button\r\n        mat-raised-button\r\n        color=\"primary\"\r\n        class=\"full_width\"\r\n        [routerLink]=\"['/study-group', studyGroup.id]\"\r\n      >\r\n        Detalhes\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n  } @else {\r\n  <p>Error loading data</p>\r\n  } } @else {\r\n  <p>Loading...</p>\r\n  }\r\n</div> -->\r\n", "import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON>ement<PERSON><PERSON>, On<PERSON>nit, Query<PERSON>ist, ViewChild, ViewChildren, inject } from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { StudyGroupFilterDialogComponent } from '../study-group-filter-dialog/study-group-filter-dialog.component';\r\nimport { StudyGroupSearchListComponent } from '../study-group-search-list/study-group-search-list.component';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatButton } from '@angular/material/button';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatForm<PERSON>ield, MatLabel } from '@angular/material/form-field';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatCheckbox, MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { StudyGroupSearchItemComponent } from '../study-group-search-item/study-group-search-item.component';\r\nimport { <PERSON><PERSON><PERSON> } from '@angular/common';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { Router } from '@angular/router';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@Component({\r\n    selector: 'app-study-group-search-bar',\r\n    templateUrl: './study-group-search-bar.component.html',\r\n    styleUrl: './study-group-search-bar.component.scss',\r\n    standalone: true,\r\n    imports: [\r\n        MatFormField,\r\n        MatLabel,\r\n        MatInput,\r\n        MatButton,\r\n        MatIcon,\r\n        MatMenuModule,\r\n        MatCheckboxModule,\r\n        StudyGroupSearchListComponent,\r\n        NgFor,\r\n        StudyGroupSearchItemComponent,\r\n        MatAutocompleteModule,\r\n        ToastModule\r\n    ],\r\n    providers: [MessageService]\r\n})\r\nexport class StudyGroupSearchBarComponent implements OnInit {\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  selectedDays: Set<string> = new Set();\r\n  selectedHour: string = '';\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n  @ViewChild('time') time!: ElementRef<HTMLInputElement>;\r\n  @ViewChildren(MatCheckbox) checkboxes!: QueryList<MatCheckbox>;\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.service.getStudyGroups().subscribe((dados) => {\r\n      this.service.studyGroups = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.studyGroups.filter(option =>\r\n      option.title.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  applyFilters(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n\r\n    // Dividir o valor do filtro em partes, se necessário\r\n    const [codeFilter, titleFilter] = filterValue.split(' - ').map(part => part.trim());\r\n\r\n    const filter = this.service.studyGroups?.filter(option =>\r\n      this.filterByDayOfWeek(option) &&\r\n      this.filterByHour(option) &&\r\n      (option.code.toLowerCase().includes(codeFilter) ||\r\n       option.title.toLowerCase().includes(titleFilter))\r\n    ) || [];\r\n\r\n    this.options = [...filter];\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  clearFilters(): void {\r\n    this.input.nativeElement.value = '';\r\n    this.time.nativeElement.value = '';\r\n    this.checkboxes.forEach(checkbox => checkbox.checked = false);\r\n    this.cdr.detectChanges();\r\n\r\n    this.service.getStudyGroups().subscribe((dados) => {\r\n      this.service.studyGroups = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n  }\r\n\r\n  filterByDayOfWeek(option: any): boolean {\r\n    if (!option.daysOfWeek || this.selectedDays.size === 0) {\r\n      return true; // Sem filtro de dia da semana ou dados não definidos\r\n    }\r\n    return option.daysOfWeek.some((day: string) => this.selectedDays.has(day.toLowerCase()));\r\n  }\r\n\r\n  filterByHour(option: any): boolean {\r\n    if (!this.selectedHour) {\r\n      return true; // Sem filtro de horário\r\n    }\r\n    return option.hour >= this.selectedHour;\r\n  }\r\n\r\n  days(day: string): void {\r\n    if (this.selectedDays.has(day)) {\r\n      this.selectedDays.delete(day);\r\n    } else {\r\n      this.selectedDays.add(day);\r\n    }\r\n  }\r\n\r\n  onHourChange(event: any): void {\r\n    this.selectedHour = event.target.value;\r\n  }\r\n\r\n  navigateCreate(): void {\r\n    this.router.navigate(['/create']);\r\n  }\r\n}\r\n", "<div class=\"search_container\">\r\n  <div class=\"search_row\">\r\n    <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n      <mat-label>Disciplina</mat-label>\r\n      <input #input matInput class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n      <mat-autocomplete requireSelection #auto=\"matAutocomplete\">\r\n        <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.title\">\r\n          ({{ option.code }}) {{ option.title }}\r\n        </mat-option>\r\n      </mat-autocomplete>\r\n    </mat-form-field>\r\n\r\n    <button\r\n      mat-raised-button\r\n      color=\"primary\"\r\n      class=\"search_row_search_item\"\r\n      (click)=\"applyFilters()\">\r\n      <mat-icon class=\"search_row_icon\">search</mat-icon>\r\n    </button>\r\n\r\n    <button\r\n    mat-raised-button\r\n    color=\"primary\"\r\n    class=\"search_row_search_item\"\r\n    (click)=\"clearFilters()\">Limpar Filtro\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"search_row\">\r\n    <button mat-button [matMenuTriggerFor]=\"menu\" class=\"pesquisarButton\">Dias da <PERSON></button>\r\n    <mat-menu #menu=\"matMenu\" style=\"max-width: auto !important;\">\r\n      <div class=\"position\">\r\n        <section style=\"display: flex;\" #semanas>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('dom')\">DOM</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('seg')\">SEG</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('ter')\">TER</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qua')\">QUA</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('qui')\">QUI</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sex')\">SEX</mat-checkbox>\r\n          <mat-checkbox color=\"primary\" (click)=\"$event.stopPropagation(); days('sab')\">SAB</mat-checkbox>\r\n        </section>\r\n      </div>\r\n    </mat-menu>\r\n\r\n    <button mat-button [matMenuTriggerFor]=\"menuHora\" class=\"pesquisarButton\">Hora de Início</button>\r\n    <mat-menu #menuHora=\"matMenu\" class=\"position\">\r\n      <div class=\"position\">\r\n        <mat-form-field appearance=\"outline\" class=\"input6\" (click)=\"$event.stopPropagation()\">\r\n          <mat-label>A partir de:</mat-label>\r\n          <input #time matInput type=\"time\" (change)=\"onHourChange($event)\"/>\r\n        </mat-form-field>\r\n      </div>\r\n    </mat-menu>\r\n  </div>\r\n\r\n  <div class=\"example-button-container\">\r\n    <button mat-button class=\"fabButton\" (click)=\"navigateCreate()\">\r\n      <mat-icon>add</mat-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <div class=\"container\">\r\n    <div *ngFor=\"let groups of options\">\r\n      <app-study-group-search-item\r\n      [studyGroup]=\"groups\">\r\n      </app-study-group-search-item>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<p-toast />\r\n", "import { Component, EventEmitter, Input, Output } from '@angular/core';\r\nimport { StudyGroup } from '../study-group';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { MatButton } from '@angular/material/button';\r\nimport { <PERSON><PERSON><PERSON>, TitleCasePipe } from '@angular/common';\r\nimport { MatChipSet, MatChip } from '@angular/material/chips';\r\nimport { Mat<PERSON>ard, MatCardHeader, MatCardTitle, MatCardSubtitle, MatCardContent, MatCardActions } from '@angular/material/card';\r\nimport { StudyGroupService } from '../study-group.service';\r\n\r\n@Component({\r\n    selector: 'app-study-group-search-item',\r\n    templateUrl: './study-group-search-item.component.html',\r\n    styleUrls: ['./study-group-search-item.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        Mat<PERSON>ard,\r\n        MatCardHeader,\r\n        MatCardTitle,\r\n        MatCardSubtitle,\r\n        MatCardContent,\r\n        MatChipSet,\r\n        NgFor,\r\n        MatC<PERSON>,\r\n        <PERSON><PERSON>ard<PERSON><PERSON>,\r\n        Mat<PERSON>utton,\r\n        RouterLink,\r\n        TitleCasePipe,\r\n    ],\r\n})\r\nexport class StudyGroupSearchItemComponent {\r\n  @Input() studyGroup!: StudyGroup;\r\n\r\n  constructor(\r\n    public service: StudyGroupService,\r\n    private router: Router) {}\r\n\r\n  openDetalheDialog(studyGroup: any): void {\r\n    this.service.setStudyGroup(studyGroup);\r\n    this.router.navigate([`/detail/${studyGroup.id}`]);\r\n\r\n    // let dialogRef = this.dialog.open(StudyGroupDetailComponent, {\r\n    //   maxWidth: '100vw',\r\n    //   maxHeight: '100vh',\r\n    //   height: '100%',\r\n    //   width: '100%',\r\n    //   data: { id: id }\r\n    // });\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <mat-card>\r\n    <mat-card-header>\r\n      <mat-card-title>{{ studyGroup.title }}</mat-card-title>\r\n      <mat-card-subtitle>{{ studyGroup.shortDescription }}</mat-card-subtitle>\r\n    </mat-card-header>\r\n    <mat-card-content>\r\n      <p>\r\n        <strong>Monitor:</strong>\r\n        {{ studyGroup.monitor }}\r\n      </p>\r\n      <p>\r\n        <strong><PERSON><PERSON><PERSON>:</strong>\r\n        {{ studyGroup.participants }}\r\n      </p>\r\n      <p>\r\n        <strong>Modalidade:</strong>\r\n        {{ studyGroup.modality | titlecase }}\r\n      </p>\r\n      <p>\r\n        <strong><PERSON><PERSON> de Início:</strong>\r\n        {{ studyGroup.hour }}\r\n      </p>\r\n      <mat-chip-set>\r\n        <mat-chip class=\"selected\" *ngFor=\"let day of studyGroup.daysOfWeek\">\r\n         <p class=\"selectedText\">{{day | titlecase}}</p>\r\n        </mat-chip>\r\n      </mat-chip-set>\r\n    </mat-card-content>\r\n\r\n    <mat-card-actions>\r\n      <button\r\n        mat-raised-button\r\n        color=\"primary\"\r\n        class=\"full_width\"\r\n        (click)=\"openDetalheDialog(studyGroup)\">\r\n        Detalhes\r\n      </button>\r\n    </mat-card-actions>\r\n  </mat-card>\r\n</div>\r\n\r\n<!-- [routerLink]=\"['/study-group', studyGroup.id]\" -->\r\n", "import { catchError, map, Observable, of, tap } from 'rxjs';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { StudyGroup } from './study-group';\r\nimport { environment } from 'src/environments/environment';\r\n\r\nconst AUTH_API = '';\r\n\r\nconst httpOptions = {\r\n  headers: new HttpHeaders({ 'Content-Type': 'application/json' }),\r\n};\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class StudyGroupService {\r\n  private studyGroupParam: any;\r\n  studyGroups: StudyGroup[] = [];\r\n  myStudyGroups: StudyGroup[] = [];\r\n  subjects: any[] = [];\r\n\r\n  private readonly http = inject(HttpClient);\r\n  constructor() {}\r\n\r\n  getStudyGroups(): Observable<any[]> {\r\n    return this.http\r\n      .post<any[]>(`${AUTH_API}/study-groups/filter`, {}, httpOptions)\r\n      .pipe(\r\n        map((studyGroups) => studyGroups.map(this.mappingStudyGroup)),\r\n        catchError((error) => {\r\n          console.error('Erro na chamada de API', error);\r\n          return of([]);\r\n        })\r\n      );\r\n  }\r\n\r\n  getStudyGroupsFind(studentId: number): Observable<any[]> {\r\n    const requestBody = { studentId };\r\n\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/filter`, requestBody, httpOptions)\r\n    .pipe(\r\n      map((studyGroups) => studyGroups.map(this.mappingStudyGroup)),\r\n      catchError((error) => {\r\n        console.error('Erro na chamada de API', error);\r\n        return of([]);\r\n      })\r\n    );;\r\n  }\r\n\r\n  public mappingStudyGroup(item: any): any {\r\n    const {\r\n      id,\r\n      description,\r\n      tutor,\r\n      subject,\r\n      students,\r\n      maxStudents,\r\n      meetingTime,\r\n      modality,\r\n      weekdays,\r\n      ownerId,\r\n      discordInviteUrl\r\n    } = item;\r\n\r\n    // const shortDescription =\r\n    //   description.length > 50 ? description.substr(0, 100) + '...' : description;\r\n\r\n    let countTutor = 0;\r\n    if (tutor) {\r\n      countTutor = 1;\r\n    }\r\n    const monitorText = `${countTutor}/${1}`;\r\n\r\n    const participantsCount = students.length;\r\n    const participantsText = `${participantsCount}/${maxStudents}`;\r\n    const mappedHour = meetingTime.substr(0, 5);\r\n\r\n    let mappedModality = '';\r\n    switch (modality) {\r\n      case 'REMOTE':\r\n        mappedModality = 'remoto';\r\n        break;\r\n      case 'PRESENCIAL':\r\n        mappedModality = 'presencial';\r\n        break;\r\n      default:\r\n        mappedModality = 'híbrido';\r\n    }\r\n\r\n    return {\r\n      id: id,\r\n      title: subject.name,\r\n      code: subject.code,\r\n      ownerId: ownerId,\r\n      shortDescription: description,\r\n      modality: mappedModality,\r\n      hour: mappedHour,\r\n      monitor: monitorText,\r\n      participants: participantsText,\r\n      students: students,\r\n      daysOfWeek: weekdays.map((day: { name: string }) =>\r\n        day.name.toLowerCase().substring(0, 3)\r\n      ),\r\n      discordInviteUrl: discordInviteUrl\r\n    };\r\n  }\r\n\r\n  joinGroupService(groupId: number,id: number){\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/${groupId}/students/${id}/join`, {}, httpOptions)\r\n  }\r\n\r\n  leaveGroupService(groupId: number,id: number){\r\n    return this.http.post<any[]>(`${AUTH_API}/study-groups/${groupId}/students/${id}/leave`, {}, httpOptions)\r\n  }\r\n\r\n  getSubjects(): Observable<any[]> {\r\n    return this.http.get<any[]>(`${AUTH_API}/subjects`);\r\n  }\r\n\r\n  getStudyGroupId(studyGroupId: number): Observable<any[]> {\r\n    return this.http.get<any[]>(`${AUTH_API}/study-groups/${studyGroupId}`);\r\n  }\r\n\r\n  createStudyGroup(studyGroupData: any): Observable<any> {\r\n    return this.http.post<any>(`${AUTH_API}/study-groups`, studyGroupData);\r\n  }\r\n\r\n  editStudyGroup(studyGroupData: any, groupId: number): Observable<any> {\r\n    return this.http.put<any>(`${AUTH_API}/study-groups/${groupId}`, studyGroupData);\r\n  }\r\n\r\n  setStudyGroup(data: any) {\r\n    this.studyGroupParam = data;\r\n  }\r\n\r\n  getStudyGroup() {\r\n    return this.studyGroupParam;\r\n  }\r\n}\r\n", "import { CommonModule, NgFor } from '@angular/common';\r\nimport { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { ReactiveFormsModule, FormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatIconButton, MatButton } from '@angular/material/button';\r\nimport { MatChipListbox, MatChipOption } from '@angular/material/chips';\r\nimport { MatOption } from '@angular/material/core';\r\nimport { MatFormField, MatLabel } from '@angular/material/form-field';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatInput } from '@angular/material/input';\r\nimport { MatSelect } from '@angular/material/select';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { StudyGroupService } from '../study-group.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { MessageService } from 'primeng/api';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\n\r\n@Component({\r\n  selector: 'app-study-update-group',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    ReactiveFormsModule,\r\n    MatToolbar,\r\n    MatIconButton,\r\n    MatIcon,\r\n    MatFormField,\r\n    MatLabel,\r\n    MatSelect,\r\n    FormsModule,\r\n    NgFor,\r\n    MatOption,\r\n    MatChipListbox,\r\n    MatChipOption,\r\n    MatInput,\r\n    MatButton,\r\n    MatAutocompleteModule,\r\n    ToastModule,\r\n    ProgressSpinnerModule\r\n  ],\r\n  templateUrl: './study-update-group.component.html',\r\n  styleUrl: './study-update-group.component.scss',\r\n  providers: [MessageService]\r\n})\r\nexport class StudyUpdateGroupComponent implements OnInit {\r\n  formulario!: UntypedFormGroup;\r\n  options: any[] = [];\r\n  filteredOptions!: any[];\r\n  loading: boolean = false;\r\n  @ViewChild('input') input!: ElementRef<HTMLInputElement>;\r\n\r\n  protected daysOfWeek: { id: number; name: string }[] = [\r\n    { id: 1, name: 'Dom' },\r\n    { id: 2, name: 'Seg' },\r\n    { id: 3, name: 'Ter' },\r\n    { id: 4, name: 'Qua' },\r\n    { id: 5, name: 'Qui' },\r\n    { id: 6, name: 'Sex' },\r\n    { id: 7, name: 'Sáb' },\r\n  ];\r\n\r\n  protected modalities: { value: string; viewValue: string }[] = [\r\n    { value: 'presencial', viewValue: 'Presencial' },\r\n    { value: 'online', viewValue: 'Online' },\r\n    { value: 'hibrido', viewValue: 'Híbrido' },\r\n  ];\r\n\r\n  protected selectedModality: String | null = null;\r\n\r\n  constructor(\r\n    private snackBar: MatSnackBar,\r\n    private messageService: MessageService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    public service: StudyGroupService,\r\n    private builder: UntypedFormBuilder) {\r\n    this.formulario = this.builder.group({\r\n      id: [''],\r\n      title: [''],\r\n      description: [''],\r\n      campoOculto: [''],\r\n      subject: [''],\r\n      maxStudents: [''],\r\n      meetingTime: [''],\r\n      modality: [\"REMOTE\"],\r\n      weekdays: [[]]\r\n    })\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.service.getSubjects().subscribe((dados) => {\r\n      this.service.subjects = dados;\r\n      this.options = dados;\r\n      this.filteredOptions = this.options.slice();\r\n    })\r\n\r\n    this.route.queryParams.subscribe(params => {\r\n      const groupId = params['id'];\r\n\r\n      this.service.getStudyGroupId(groupId).subscribe((dados: any) => {\r\n        this.formulario.patchValue(dados)\r\n        this.formulario.get('campoOculto')?.patchValue(dados.subject.code + ' - ' + dados.subject.name);\r\n\r\n        const selectedDays = dados.weekdays.map((day: { id: number; name: string }) => {\r\n          return this.daysOfWeek.find(d => d.id === day.id);\r\n        });\r\n        this.formulario.get('weekdays')?.patchValue(selectedDays);\r\n      })\r\n    });\r\n  }\r\n\r\n  filter(): void {\r\n    const filterValue = this.input.nativeElement.value.toLowerCase();\r\n    this.filteredOptions = this.service.subjects.filter(option =>\r\n      option.id.toString().includes(filterValue) || option.name.toLowerCase().includes(filterValue) || option.code.toLowerCase().includes(filterValue)\r\n    );\r\n  }\r\n\r\n  onOptionSelected(option: any): void {\r\n    this.formulario.get('subject')?.patchValue(option);\r\n    this.formulario.get('campoOculto')?.patchValue(option.code + ' - ' + option.name);\r\n  }\r\n\r\n  editGroups(){\r\n    const idUsuario = localStorage.getItem('idUsuario');\r\n    const id = Number(idUsuario);\r\n    const title = this.formulario?.value.subject.code + \" \" + this.formulario?.value.subject.name;\r\n\r\n    const groupId = this.formulario?.value.id;\r\n\r\n    const studyGroupData = {\r\n      id: this.formulario?.value.id,\r\n      title: title,\r\n      description: this.formulario?.value.description,\r\n      userId: id,\r\n      ownerId: id,\r\n      subject: this.formulario?.value.subject,\r\n      weekdays: this.formulario?.value.weekdays,\r\n      meetingTime: this.formulario?.value.meetingTime,\r\n      maxStudents: this.formulario?.value.maxStudents,\r\n      modality: this.formulario?.value.modality\r\n    };\r\n\r\n    this.loading = true;\r\n\r\n    this.service.editStudyGroup(studyGroupData, groupId).subscribe(\r\n      response => {\r\n        console.log('Grupo de estudo editado com sucesso:', response);\r\n\r\n        this.snackBar.open(\r\n          'Grupo de estudo editado com sucesso!',\r\n          'X',\r\n          { duration: 2500 }\r\n        );\r\n        this.service.setStudyGroup(response);\r\n        this.router.navigate([`/detail/${response.id}`]);\r\n      },\r\n      error => {\r\n        this.loading = false;\r\n        console.error('Erro ao criar grupo de estudo:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n}\r\n", "<div class=\"filter-container\">\r\n  <form [formGroup]=\"formulario\" >\r\n    <div class=\"row\">\r\n      <mat-form-field appearance=\"outline\" class=\"search_row_search_bar\">\r\n        <mat-label>Disciplina</mat-label>\r\n        <input #input matInput formControlName=\"campoOculto\" class=\"search_row_search_bar\" [matAutocomplete]=\"auto\" (input)=\"filter()\" (focus)=\"filter()\" />\r\n        <mat-autocomplete requireSelection #auto=\"matAutocomplete\" (optionSelected)=\"onOptionSelected($event.option.value)\">\r\n          <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option\">\r\n          <!-- <mat-option *ngFor=\"let option of filteredOptions\" [value]=\"option.code + ' - ' + option.name\"> -->\r\n            ({{ option.code }}) {{ option.name }}\r\n          </mat-option>\r\n        </mat-autocomplete>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <mat-form-field class=\"input100\">\r\n      <mat-label>Descrição</mat-label>\r\n      <textarea matInput formControlName=\"description\"></textarea>\r\n    </mat-form-field>\r\n\r\n    <div class=\"row\">\r\n      <mat-form-field>\r\n        <mat-label>Número de Alunos</mat-label>\r\n        <mat-select formControlName=\"maxStudents\">\r\n          <mat-option [value]=\"2\">2</mat-option>\r\n          <mat-option [value]=\"3\">3</mat-option>\r\n          <mat-option [value]=\"4\">4</mat-option>\r\n          <mat-option [value]=\"5\">5</mat-option>\r\n          <mat-option [value]=\"6\">6</mat-option>\r\n        </mat-select>\r\n      </mat-form-field>\r\n\r\n      <mat-form-field class=\"margin input30\">\r\n        <mat-label>Hora de Início</mat-label>\r\n        <input matInput type=\"time\" formControlName=\"meetingTime\"/>\r\n      </mat-form-field>\r\n    </div>\r\n\r\n    <h2>Dias da Semana</h2>\r\n    <mat-chip-listbox formControlName=\"weekdays\" multiple>\r\n      <mat-chip-option *ngFor=\"let day of daysOfWeek\" [value]=\"day\">\r\n        {{ day.name }}\r\n      </mat-chip-option>\r\n    </mat-chip-listbox>\r\n\r\n    <div class=\"allCenter\">\r\n      <p-toast />\r\n\r\n      @if(loading === true){\r\n        <p-progressSpinner ariaLabel=\"loading\" />\r\n      } @else {\r\n        <button mat-raised-button color=\"primary\" class=\"input100\" (click)=\"editGroups()\">Atualizar Grupo</button>\r\n      }\r\n    </div>\r\n  </form>\r\n</div>\r\n", "export const environment = {\r\n    development: true,\r\n    encodedApiUrl: 'http%3A%2F%2Flocalhost%3A8080',\r\n    // authApi: '/api'\r\n    authApi: ''\r\n};", "import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\r\n\r\n\r\nimport { importProvidersFrom } from '@angular/core';\r\nimport { AppComponent } from './app/app.component';\r\nimport { SharedModule } from './app/shared/shared.module';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { provideNoopAnimations } from '@angular/platform-browser/animations';\r\nimport { withInterceptorsFromDi, provideHttpClient } from '@angular/common/http';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { BrowserModule, bootstrapApplication } from '@angular/platform-browser';\r\nimport { AngularMaterialModule } from './app/angular-material.module';\r\nimport { AppRoutingModule } from './app/app-routing.module';\r\nimport { httpInterceptorProviders } from './app/core/helpers/http.interceptor';\r\n\r\n\r\nbootstrapApplication(AppComponent, {\r\n    providers: [\r\n        importProvidersFrom(AppRoutingModule, AngularMaterialModule, BrowserModule, FormsModule, ReactiveFormsModule, MatCardModule, SharedModule),\r\n        httpInterceptorProviders,\r\n        provideHttpClient(withInterceptorsFromDi()),\r\n        provideNoopAnimations(),\r\n    ]\r\n})\r\n  .catch(err => console.error(err));\r\n"], "names": ["CommonModule", "OverlayModule", "CdkTreeModule", "PortalModule", "MatAutocompleteModule", "MatButtonModule", "MatButtonToggleModule", "MatCardModule", "MatCheckboxModule", "MatChipsModule", "MatRippleModule", "MatDividerModule", "MatExpansionModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "MatListModule", "MatMenuModule", "MatPaginatorModule", "MatProgressSpinnerModule", "MatSelectModule", "MatSidenavModule", "MatSnackBarModule", "MatSortModule", "MatTableModule", "MatTabsModule", "MatToolbarModule", "MatTreeModule", "MatBadgeModule", "MatGridListModule", "MatRadioModule", "MatDatepickerModule", "MatTooltipModule", "materialModules", "AngularMaterialModule", "_", "_2", "_3", "imports", "exports", "RouterModule", "RegisterComponent", "LoginComponent", "EmailConfirmComponent", "StudyGroupSearchBarComponent", "PasswordRecoveryComponent", "<PERSON>th<PERSON><PERSON>", "loggedInGuard", "StudyGroupDetailComponent", "StudyCreateGroupComponent", "MyStudyGroupComponent", "StudyUpdateGroupComponent", "StudyGroupAssociateComponent", "routes", "path", "component", "canActivate", "children", "AppRoutingModule", "forRoot", "i1", "ChangeDetectorRef", "inject", "AuthService", "Router", "RouterLink", "RouterOutlet", "MatNavList", "MatListItem", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatIconButton", "MatToolbar", "MatMenu", "MatMenuItem", "MatMenuTrigger", "MatSnackBar", "NavigationServiceService", "i0", "ɵɵelementStart", "ɵɵlistener", "AppComponent_Conditional_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "navigateToSearch", "ɵɵtext", "ɵɵelementEnd", "AppComponent_Conditional_8_Template_button_click_6_listener", "_r3", "logout", "AppComponent_Conditional_8_Template_button_click_11_listener", "snav_r4", "ɵɵreference", "toggle", "ɵɵadvance", "ɵɵproperty", "userMenu_r5", "ɵɵtextInterpolate", "user", "name", "AppComponent_Conditional_12_Conditional_1_Template_a_click_0_listener", "_r7", "close", "AppComponent_Conditional_12_Conditional_1_Template_a_click_2_listener", "AppComponent_Conditional_12_Conditional_1_Template_a_click_4_listener", "ɵɵtemplate", "AppComponent_Conditional_12_Conditional_1_Template", "AppComponent_Conditional_12_Template_a_click_2_listener", "_r6", "ɵɵconditional", "router", "url", "AppComponent", "constructor", "location", "appName", "title", "isLoggedIn", "undefined", "showBackIcon", "snackBar", "authService", "cdr", "navigationService", "ngOnInit", "subscribe", "getUser", "detectChanges", "events", "startsWith", "next", "data", "console", "log", "navigateByUrl", "open", "duration", "error", "navigate", "sidenav", "ɵɵdirectiveInject", "Location", "selectors", "viewQuery", "AppComponent_Query", "rf", "ctx", "AppComponent_Conditional_3_Template", "ɵɵelement", "AppComponent_Conditional_8_Template", "AppComponent_Conditional_12_Template", "AppComponent_Conditional_13_Template", "styles", "route", "message", "queryParams", "params", "token", "confirmEmail", "err", "ActivatedRoute", "i2", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "EmailConfirmComponent_Template", "FormBuilder", "Validators", "FormsModule", "ReactiveFormsModule", "MatButton", "<PERSON><PERSON><PERSON><PERSON>", "MatInput", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatCard", "MatCardTitle", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "fb", "errorMessage", "loginForm", "group", "email", "required", "password", "onSubmit", "valid", "body", "controls", "value", "login", "idUsuario", "id", "toString", "localStorage", "setItem", "isDiscordAssociate", "consts", "LoginComponent_Template", "LoginComponent_Template_form_ngSubmit_5_listener", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "PasswordRecoveryComponent_Template", "isLogged", "signupForm", "register", "onSuccess", "onError", "reset", "reloadPage", "window", "reload", "RegisterComponent_Template", "RegisterComponent_Template_form_ngSubmit_5_listener", "HTTP_INTERCEPTORS", "HttpRequestInterceptor", "intercept", "req", "clone", "withCredentials", "handle", "factory", "ɵfac", "httpInterceptorProviders", "provide", "useClass", "multi", "HttpClient", "HttpHeaders", "catchError", "tap", "StorageService", "AUTH_API", "httpOptions", "headers", "http", "storageService", "post", "concat", "pipe", "saveUser", "clear", "get", "providedIn", "BehaviorSubject", "of", "SIGNED_USER", "<PERSON><PERSON><PERSON>", "hasSignedUser", "restoreLocalStorage", "removeItem", "JSON", "stringify", "signedUserString", "getItem", "Error", "parse", "asObservable", "signedUserStr", "signedUser<PERSON>bj", "_route", "_state", "b", "discordAssociateGuard", "signed", "nav_r3", "HeaderComponent", "fillerNav", "Array", "from", "length", "i", "HeaderComponent_Template", "HeaderComponent_Template_button_click_2_listener", "snav_r2", "ɵɵrepeaterCreate", "HeaderComponent_For_13_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "SharedModule", "<PERSON><PERSON><PERSON>", "MatCheckbox", "StudyGroupSearchItemComponent", "option_r2", "code", "ɵɵtextInterpolate2", "groups_r3", "service", "options", "selectedDays", "Set", "selected<PERSON>our", "Number", "getStudyGroupsFind", "dados", "myStudyGroups", "filteredOptions", "slice", "filter", "filterValue", "input", "nativeElement", "toLowerCase", "option", "includes", "applyFilters", "codeFilter", "titleFilter", "split", "map", "part", "trim", "filterByDayOfWeek", "filterByHour", "clearFilters", "time", "checkboxes", "for<PERSON>ach", "checkbox", "checked", "daysOfWeek", "size", "some", "day", "has", "hour", "days", "delete", "add", "onHourChange", "event", "target", "StudyGroupService", "MyStudyGroupComponent_Query", "MyStudyGroupComponent_Template_input_input_5_listener", "MyStudyGroupComponent_Template_input_focus_5_listener", "MyStudyGroupComponent_mat_option_9_Template", "MyStudyGroupComponent_Template_button_click_10_listener", "MyStudyGroupComponent_Template_button_click_13_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_22_listener", "$event", "stopPropagation", "MyStudyGroupComponent_Template_mat_checkbox_click_24_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_26_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_28_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_30_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_32_listener", "MyStudyGroupComponent_Template_mat_checkbox_click_34_listener", "MyStudyGroupComponent_Template_mat_form_field_click_41_listener", "MyStudyGroupComponent_Template_input_change_44_listener", "MyStudyGroupComponent_div_47_Template", "auto_r4", "menu_r5", "menuHora_r6", "i3", "i4", "i5", "MatAutocomplete", "i6", "MatOption", "MatAutocompleteTrigger", "NavigationStart", "previousUrls", "currentUrl", "navigationStartEvent", "push", "getPreviousUrl", "navigateToPrevious", "previousUrl", "catch", "pop", "ɵɵinject", "MatChipListbox", "MatChipOption", "MatSelect", "ToastModule", "MessageService", "ProgressSpinnerModule", "day_r3", "ɵɵtextInterpolate1", "messageService", "builder", "modalities", "viewValue", "selectedModality", "subscription", "formulario", "description", "campoOculto", "subject", "maxStudents", "meetingTime", "modality", "weekdays", "getSubjects", "subjects", "onOptionSelected", "patchValue", "createGroups", "studyGroupData", "ownerId", "createStudyGroup", "response", "mappedStudyGroup", "mappingStudyGroup", "setStudyGroup", "ngOnDestroy", "unsubscribe", "UntypedFormBuilder", "StudyCreateGroupComponent_Query", "StudyCreateGroupComponent_Template", "StudyCreateGroupComponent_Template_input_input_6_listener", "StudyCreateGroupComponent_Template_input_focus_6_listener", "StudyCreateGroupComponent_Template_mat_autocomplete_optionSelected_8_listener", "StudyCreateGroupComponent_mat_option_10_Template", "StudyCreateGroupComponent_mat_chip_option_37_Template", "StudyCreateGroupComponent_Template_button_click_40_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i7", "i8", "Toast", "environment", "encodedApiUrl", "associate", "https", "StudyGroupAssociateComponent_Template", "StudyGroupAssociateComponent_Template_button_click_8_listener", "MatChipSet", "MatChip", "MatCardSubtitle", "ɵɵpureFunction1", "_c0", "studyGroup", "day_r1", "_c1", "ɵɵpipeBind1", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Conditional_3_Template_button_click_0_listener", "_r4", "editGroup", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Template_button_click_0_listener", "openDiscord", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Conditional_3_Template", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Template_button_click_4_listener", "leaveGroup", "discordInviteUrl", "isOwnerId", "StudyGroupDetailComponent_Conditional_21_Conditional_1_Template_button_click_0_listener", "_r5", "joinGroup", "StudyGroupDetailComponent_Conditional_21_Conditional_0_Template", "StudyGroupDetailComponent_Conditional_21_Conditional_1_Template", "userInGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "getStudyGroup", "callGroup", "panelClass", "joinGroupService", "leaveGroupService", "resposta", "idParam", "snapshot", "paramMap", "idDetail", "getStudyGroupId", "isArray", "students", "isStudentInGroup", "student", "isOwner", "ɵɵProvidersFeature", "StudyGroupDetailComponent_Template", "StudyGroupDetailComponent_mat_chip_19_Template", "StudyGroupDetailComponent_Conditional_20_Template", "StudyGroupDetailComponent_Conditional_21_Template", "shortDescription", "participants", "Ng<PERSON><PERSON>", "TitleCasePipe", "ProgressSpinner", "getStudyGroups", "studyGroups", "navigateCreate", "StudyGroupSearchBarComponent_Query", "StudyGroupSearchBarComponent_Template", "StudyGroupSearchBarComponent_Template_input_input_5_listener", "StudyGroupSearchBarComponent_Template_input_focus_5_listener", "StudyGroupSearchBarComponent_mat_option_9_Template", "StudyGroupSearchBarComponent_Template_button_click_10_listener", "StudyGroupSearchBarComponent_Template_button_click_13_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_23_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_25_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_27_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_29_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_31_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_33_listener", "StudyGroupSearchBarComponent_Template_mat_checkbox_click_35_listener", "StudyGroupSearchBarComponent_Template_mat_form_field_click_42_listener", "StudyGroupSearchBarComponent_Template_input_change_45_listener", "StudyGroupSearchBarComponent_Template_button_click_48_listener", "StudyGroupSearchBarComponent_div_52_Template", "MatCardHeader", "MatCardActions", "openDetalheDialog", "inputs", "StudyGroupSearchItemComponent_Template", "StudyGroupSearchItemComponent_mat_chip_26_Template", "StudyGroupSearchItemComponent_Template_button_click_28_listener", "monitor", "studentId", "requestBody", "item", "tutor", "countTutor", "monitorText", "participantsCount", "participantsText", "mappedHour", "substr", "mappedModality", "substring", "groupId", "studyGroupId", "editStudyGroup", "put", "studyGroupParam", "StudyUpdateGroupComponent_Conditional_41_Template_button_click_0_listener", "ctx_r4", "editGroups", "find", "d", "userId", "StudyUpdateGroupComponent_Query", "StudyUpdateGroupComponent_Template", "StudyUpdateGroupComponent_Template_input_input_6_listener", "StudyUpdateGroupComponent_Template_input_focus_6_listener", "StudyUpdateGroupComponent_Template_mat_autocomplete_optionSelected_8_listener", "StudyUpdateGroupComponent_mat_option_10_Template", "StudyUpdateGroupComponent_mat_chip_option_37_Template", "StudyUpdateGroupComponent_Conditional_40_Template", "StudyUpdateGroupComponent_Conditional_41_Template", "auto_r6", "i9", "development", "authApi", "importProvidersFrom", "provideNoopAnimations", "withInterceptorsFromDi", "provideHttpClient", "BrowserModule", "bootstrapApplication", "providers"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}