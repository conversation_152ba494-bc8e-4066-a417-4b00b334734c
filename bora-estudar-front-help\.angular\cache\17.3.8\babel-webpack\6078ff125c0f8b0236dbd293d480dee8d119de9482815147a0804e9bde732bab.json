{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ChangeDetectorRef, Component, ViewChild, inject } from '@angular/core';\nimport { AuthService } from './core/security/auth/auth.service';\nimport { Router, RouterLink, RouterOutlet } from '@angular/router';\nimport { Mat<PERSON>avList, MatListItem } from '@angular/material/list';\nimport { MatSidenavContainer, MatSidenav, MatSidenavContent } from '@angular/material/sidenav';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatIconButton } from '@angular/material/button';\nimport { MatToolbar } from '@angular/material/toolbar';\nimport { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { NavigationServiceService } from './study-group/navigation-service.service';\nlet AppComponent = class AppComponent {\n  constructor(location) {\n    this.location = location;\n    this.appName = 'Bora E<PERSON>udar UFF';\n    this.title = 'bora-estudar-front';\n    this.isLoggedIn = false;\n    this.user = undefined;\n    this.showBackIcon = false;\n    this.snackBar = inject(MatSnackBar);\n    this.authService = inject(AuthService);\n    this.router = inject(Router);\n    this.cdr = inject(ChangeDetectorRef);\n    this.navigationService = inject(NavigationServiceService);\n  }\n  ngOnInit() {\n    this.authService.isLoggedIn().subscribe(isLoggedIn => {\n      this.isLoggedIn = isLoggedIn;\n      if (isLoggedIn) {\n        this.getUser();\n      }\n      this.cdr.detectChanges();\n    });\n    this.router.events.subscribe(() => {\n      this.showBackIcon = this.router.url !== '/search';\n      if (this.router.url === '/create') {\n        this.appName = 'Criar Grupo';\n      } else if (this.router.url === '/my-study-group') {\n        this.appName = 'Meus Grupos';\n      } else if (this.router.url.startsWith('/edit')) {\n        this.appName = 'Editar';\n      } else if (this.router.url.startsWith('/detail')) {\n        this.appName = 'Detalhes';\n      } else {\n        this.appName = 'Bora Estudar UFF';\n      }\n    });\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: data => {\n        console.log(data);\n        this.router.navigateByUrl('/login');\n        this.close();\n        this.snackBar.open('Desconectado com sucesso!', 'X', {\n          duration: 2500\n        });\n      },\n      error: error => {\n        console.log(error);\n      }\n    });\n  }\n  getUser() {\n    this.authService.getUser().subscribe({\n      next: data => {\n        console.log(data);\n        this.user = data;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.log(error);\n      }\n    });\n  }\n  navigateToSearch() {\n    this.router.navigate(['/search']);\n  }\n  // navigateToSearch(): void {\n  //   const previousUrl = this.navigationService.getPreviousUrl();\n  //   if (previousUrl) {\n  //       this.router.navigate([previousUrl]);\n  //   } else {\n  //       this.router.navigate(['/home']);\n  //   }\n  // }\n  close() {\n    if (this.sidenav) {\n      this.sidenav.close();\n    }\n  }\n};\n__decorate([ViewChild('snav')], AppComponent.prototype, \"sidenav\", void 0);\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss'],\n  standalone: true,\n  imports: [MatToolbar, MatIconButton, MatIcon, MatSidenavContainer, MatSidenav, MatNavList, MatListItem, RouterLink, MatSidenavContent, RouterOutlet, MatMenu, MatMenuItem, MatMenuTrigger]\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "names": ["ChangeDetectorRef", "Component", "ViewChild", "inject", "AuthService", "Router", "RouterLink", "RouterOutlet", "MatNavList", "MatListItem", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatIconButton", "MatToolbar", "MatMenu", "MatMenuItem", "MatMenuTrigger", "MatSnackBar", "NavigationServiceService", "AppComponent", "constructor", "location", "appName", "title", "isLoggedIn", "user", "undefined", "showBackIcon", "snackBar", "authService", "router", "cdr", "navigationService", "ngOnInit", "subscribe", "getUser", "detectChanges", "events", "url", "startsWith", "logout", "next", "data", "console", "log", "navigateByUrl", "close", "open", "duration", "error", "navigateToSearch", "navigate", "sidenav", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\app.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild, inject } from '@angular/core';\r\nimport { AuthService } from './core/security/auth/auth.service';\r\nimport { Router, RouterLink, RouterOutlet } from '@angular/router';\r\nimport { UserResponseBasicDto } from './shared/models/user/user-response-basic-dto';\r\nimport { MatNavList, MatListItem } from '@angular/material/list';\r\nimport { MatSidenavContainer, MatSidenav, MatSidenavContent } from '@angular/material/sidenav';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatIconButton } from '@angular/material/button';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Location } from '@angular/common'\r\nimport { NavigationServiceService } from './study-group/navigation-service.service';\r\n\r\n@Component({\r\n    selector: 'app-root',\r\n    templateUrl: './app.component.html',\r\n    styleUrls: ['./app.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        MatToolbar,\r\n        MatIconButton,\r\n        MatIcon,\r\n        MatSidenavContainer,\r\n        MatSidenav,\r\n        MatNavList,\r\n        MatListItem,\r\n        RouterLink,\r\n        MatSidenavContent,\r\n        RouterOutlet,\r\n        MatMenu,\r\n        MatMenuItem,\r\n        MatMenuTrigger,\r\n    ],\r\n})\r\nexport class AppComponent implements OnInit {\r\n  appName: string = 'Bora Estudar UFF';\r\n  title = 'bora-estudar-front';\r\n  isLoggedIn = false;\r\n  user: UserResponseBasicDto | undefined = undefined;\r\n  showBackIcon = false;\r\n  @ViewChild('snav') sidenav!: MatSidenav;\r\n\r\n  private snackBar = inject(MatSnackBar);\r\n  private authService = inject(AuthService);\r\n  public router = inject(Router);\r\n  private cdr = inject(ChangeDetectorRef);\r\n  private navigationService = inject(NavigationServiceService);\r\n\r\n  constructor(\r\n    private location: Location\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.authService.isLoggedIn().subscribe((isLoggedIn) => {\r\n      this.isLoggedIn = isLoggedIn;\r\n      if (isLoggedIn) {\r\n        this.getUser();\r\n      }\r\n      this.cdr.detectChanges();\r\n    });\r\n\r\n    this.router.events.subscribe(() => {\r\n      this.showBackIcon = this.router.url !== '/search';\r\n\r\n      if(this.router.url === '/create'){\r\n        this.appName = 'Criar Grupo';\r\n      } else if(this.router.url === '/my-study-group'){\r\n        this.appName = 'Meus Grupos';\r\n      } else if(this.router.url.startsWith('/edit')){\r\n        this.appName = 'Editar';\r\n      } else if(this.router.url.startsWith('/detail')){\r\n        this.appName = 'Detalhes';\r\n      } else {\r\n        this.appName = 'Bora Estudar UFF';\r\n      }\r\n    });\r\n  }\r\n\r\n  public logout() {\r\n    this.authService.logout().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.router.navigateByUrl('/login');\r\n        this.close();\r\n\r\n        this.snackBar.open(\r\n          'Desconectado com sucesso!',\r\n          'X',\r\n          { duration: 2500 }\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public getUser() {\r\n    this.authService.getUser().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.user = data;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  navigateToSearch(): void {\r\n    this.router.navigate(['/search']);\r\n  }\r\n\r\n  // navigateToSearch(): void {\r\n  //   const previousUrl = this.navigationService.getPreviousUrl();\r\n\r\n  //   if (previousUrl) {\r\n  //       this.router.navigate([previousUrl]);\r\n  //   } else {\r\n  //       this.router.navigate(['/home']);\r\n  //   }\r\n  // }\r\n\r\n  close(){\r\n    if (this.sidenav) {\r\n      this.sidenav.close();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,iBAAiB,EAAEC,SAAS,EAAUC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACvF,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,MAAM,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AAElE,SAASC,UAAU,EAAEC,WAAW,QAAQ,wBAAwB;AAChE,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,iBAAiB,QAAQ,2BAA2B;AAC9F,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,OAAO,EAAEC,WAAW,EAAEC,cAAc,QAAQ,wBAAwB;AAC7E,SAASC,WAAW,QAAQ,6BAA6B;AAEzD,SAASC,wBAAwB,QAAQ,0CAA0C;AAuB5E,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAcvBC,YACUC,QAAkB;IAAlB,KAAAA,QAAQ,GAARA,QAAQ;IAdlB,KAAAC,OAAO,GAAW,kBAAkB;IACpC,KAAAC,KAAK,GAAG,oBAAoB;IAC5B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,IAAI,GAAqCC,SAAS;IAClD,KAAAC,YAAY,GAAG,KAAK;IAGZ,KAAAC,QAAQ,GAAG3B,MAAM,CAACgB,WAAW,CAAC;IAC9B,KAAAY,WAAW,GAAG5B,MAAM,CAACC,WAAW,CAAC;IAClC,KAAA4B,MAAM,GAAG7B,MAAM,CAACE,MAAM,CAAC;IACtB,KAAA4B,GAAG,GAAG9B,MAAM,CAACH,iBAAiB,CAAC;IAC/B,KAAAkC,iBAAiB,GAAG/B,MAAM,CAACiB,wBAAwB,CAAC;EAIzD;EAEHe,QAAQA,CAAA;IACN,IAAI,CAACJ,WAAW,CAACL,UAAU,EAAE,CAACU,SAAS,CAAEV,UAAU,IAAI;MACrD,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAIA,UAAU,EAAE;QACd,IAAI,CAACW,OAAO,EAAE;MAChB;MACA,IAAI,CAACJ,GAAG,CAACK,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACN,MAAM,CAACO,MAAM,CAACH,SAAS,CAAC,MAAK;MAChC,IAAI,CAACP,YAAY,GAAG,IAAI,CAACG,MAAM,CAACQ,GAAG,KAAK,SAAS;MAEjD,IAAG,IAAI,CAACR,MAAM,CAACQ,GAAG,KAAK,SAAS,EAAC;QAC/B,IAAI,CAAChB,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACQ,MAAM,CAACQ,GAAG,KAAK,iBAAiB,EAAC;QAC9C,IAAI,CAAChB,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACQ,MAAM,CAACQ,GAAG,CAACC,UAAU,CAAC,OAAO,CAAC,EAAC;QAC5C,IAAI,CAACjB,OAAO,GAAG,QAAQ;MACzB,CAAC,MAAM,IAAG,IAAI,CAACQ,MAAM,CAACQ,GAAG,CAACC,UAAU,CAAC,SAAS,CAAC,EAAC;QAC9C,IAAI,CAACjB,OAAO,GAAG,UAAU;MAC3B,CAAC,MAAM;QACL,IAAI,CAACA,OAAO,GAAG,kBAAkB;MACnC;IACF,CAAC,CAAC;EACJ;EAEOkB,MAAMA,CAAA;IACX,IAAI,CAACX,WAAW,CAACW,MAAM,EAAE,CAACN,SAAS,CAAC;MAClCO,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACZ,MAAM,CAACe,aAAa,CAAC,QAAQ,CAAC;QACnC,IAAI,CAACC,KAAK,EAAE;QAEZ,IAAI,CAAClB,QAAQ,CAACmB,IAAI,CAChB,2BAA2B,EAC3B,GAAG,EACH;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfN,OAAO,CAACC,GAAG,CAACK,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEOd,OAAOA,CAAA;IACZ,IAAI,CAACN,WAAW,CAACM,OAAO,EAAE,CAACD,SAAS,CAAC;MACnCO,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACjB,IAAI,GAAGiB,IAAI;QAChB,IAAI,CAACX,GAAG,CAACK,aAAa,EAAE;MAC1B,CAAC;MACDa,KAAK,EAAGA,KAAK,IAAI;QACfN,OAAO,CAACC,GAAG,CAACK,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACpB,MAAM,CAACqB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEAL,KAAKA,CAAA;IACH,IAAI,IAAI,CAACM,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACN,KAAK,EAAE;IACtB;EACF;CACD;AAzFoBO,UAAA,EAAlBrD,SAAS,CAAC,MAAM,CAAC,C,4CAAsB;AAN7BmB,YAAY,GAAAkC,UAAA,EArBxBtD,SAAS,CAAC;EACPuD,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,sBAAsB;EACnCC,SAAS,EAAE,CAAC,sBAAsB,CAAC;EACnCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACL7C,UAAU,EACVD,aAAa,EACbD,OAAO,EACPH,mBAAmB,EACnBC,UAAU,EACVH,UAAU,EACVC,WAAW,EACXH,UAAU,EACVM,iBAAiB,EACjBL,YAAY,EACZS,OAAO,EACPC,WAAW,EACXC,cAAc;CAErB,CAAC,C,EACWG,YAAY,CA+FxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}