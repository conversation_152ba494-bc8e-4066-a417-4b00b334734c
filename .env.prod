# apontar arquivo .env no docker-compose:   docker-compose --env-file C:\caminho\para\o\arquivo\.env.dev up
#PROD
spring_profiles_active="prod"
DB_HOST="dpg-d1h1tnjipnbc73ba6gc0-a.virginia-postgres.render.com"
DB_PORT="5432"
DB_NAME="boraestudar_rbu7"
DB_PASSWORD="16cpYGEaO87JAvn4eHIOCweT9dXgm2oy"
DB_USERNAME="postgresql"
EMAILSERVICE_USERNAME="<EMAIL>"
EMAILSERVICE_PASSWORD="fulh rhcm dlqv tzny"
DISCORD_TOKEN="MTIzNzYzMjk1NTE0NTI1NzAyMQ.G1F77C.1sr96uENcZgnptTyYbPPxJWUX5kVZNRcWh7np0"
DISCORD_GUILD_ID="1237585539314487476"
DISCORD_CLIENT_ID="1237632955145257021"
DISCORD_CLIENT_SECRET="iV3vyZyUdz-7g-syVbFFqRCz5o3m3-O_"
DISCORD_REDIRECT_URI="http://localhost:8080/discord/users"
DISCORD_AUTHORIZATION_URI="useless"
DISCORD_TOKEN_URI="https://discordapp.com/api/oauth2/token"
DISCORD_USER_INFO_URI="https://discord.com/api/users/@me"
JWT_SECRET="684ccb2aba022524803f2883bc92e0db71bbdddd91e7b70df34ba8af54fdf32bfc3c065ca3eef2813e4293aae8bedcce0a8de0244e765890140588df1436ff0f13ac23327dbeebf51dc3e6bce62df21e726b36e6af97c36ad44a8901ae7d5b4bc34f39d7cecc2ab6da61bce2fa3990734446526eb721bed819ff3ad80119c943156b66f0fb4789d010da41c02b9c51413fb0464e1205b99d1950c933c1279c24432305aafbeb497da4e53cebd0e1509a50aa69216fa976ab0dcb101c6038e1095cf7ba35871058b9383bde43a4be3bfa39a8f63eb5beefef6fe2b9a74d8fa18ce439236261e9293df6e1de683ca7439be25c2b50adb21d6d9dfd4ed9eb8eb77e"
SPRING_DATASOURCE_HIKARI_CONNECTION_TIMEOUT="30000"
EXTERNAL_HOST="https://boraestudar.onrender.com"
LOGGING_LEVEL_ROOT=""
LOGGING_LEVEL_ORG_SPRINGFRAMEWORK=""