services:
  app:
    container_name: bora-estudar-app
    image: uff.boraestudar:latest
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env.prod
    ports:
      - "8080:8080"
    # volumes:
    #   - ./backend:/app/backend
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_NAME=${DB_NAME}
      - DB_URL=jdbc:postgresql://db:5432/${DB_NAME}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
