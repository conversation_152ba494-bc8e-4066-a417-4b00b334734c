spring:
  security:
    oauth2:
      client:
        registration:
          discord:
            client-id: 1237632955145257021
            client-secret: iV3vyZyUdz-7g-syVbFFqRCz5o3m3-O_
            clientAuthenticationMethod: post
            authorizationGrantType: authorization_code
            scope:
              - identify
            redirect-uri: "http://localhost:8080/discord/users"
            clientName: FourScouts client
        provider:
          discord:
            authorizationUri: https://discord.com/oauth2/authorize?client_id=1237632955145257021&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fdiscord%2Fusers&scope=identify&state=${idUsuario}
            tokenUri: https://discordapp.com/api/oauth2/token
            userInfoUri: https://discord.com/api/users/@me
            user-info-uri: username
  datasource:
    username: sa
    password: ''
    url: jdbc:h2:mem:testdb
    driverClassName: org.h2.Driver
  h2:
    console:
      enabled: true
      path: '/h2'
      port: 8080
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: 'gzwq oiue ykyf xemm'
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    properties:
      hibernate:
        format_sql: true
    defer-datasource-initialization: true
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  sql:
    init:
      data-locations: classpath:/data.sql
security:
  jwt:
    secret: '======================Tcc=BoraEstudar==========================='
    expiration-time: 1440
    jwtCookieName: 'access_token'
  email:
    token:
      expiration-time-minutes: 60
discord:
  token: MTIzNzYzMjk1NTE0NTI1NzAyMQ.G1F77C.1sr96uENcZgnptTyYbPPxJWUX5kVZNRcWh7np0
  guildId: 1237585539314487476
external:
  host: ${http://localhost:8080}