.container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

mat-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 350px;
  min-width: 300px;
  padding: 20px 10px;
}


mat-chip {
  margin: 5px;
}

.titulo{
  font-size: 20px;
  font-weight: bold;
}

.subTitulo{
  color: rgba(0, 0, 0, 0.54);
}

.full_width {
  width: 100%;
}

.selected {
  background-color: #3f51b5 !important;
}

.selectedText{
  color: white;
}

.mtop{
  margin-top: 50px;
}

.spinerCenter{
  text-align: center;
  margin-top: 60px;
}
