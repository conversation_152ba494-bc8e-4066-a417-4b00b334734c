{"ast": null, "code": "import { <PERSON><PERSON><PERSON>on } from '@angular/material/button';\nimport { CommonModule } from '@angular/common';\nimport { MatChipSet, MatChip } from '@angular/material/chips';\nimport { MatCardTitle, MatCardSubtitle } from '@angular/material/card';\nimport { MatIcon } from '@angular/material/icon';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { ToastModule } from 'primeng/toast';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/snack-bar\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../study-group.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/toast\";\nimport * as i7 from \"primeng/progressspinner\";\nconst _c0 = a0 => ({\n  \"selected\": a0\n});\nconst _c1 = a0 => ({\n  \"selectedText\": a0\n});\nfunction StudyGroupDetailComponent_mat_chip_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 6)(1, \"p\", 7);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"titlecase\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const day_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r1.studyGroup == null ? null : ctx_r1.studyGroup.daysOfWeek.includes(day_r1)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx_r1.studyGroup == null ? null : ctx_r1.studyGroup.daysOfWeek.includes(day_r1)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 3, day_r1));\n  }\n}\nfunction StudyGroupDetailComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-progressSpinner\", 5);\n  }\n}\nfunction StudyGroupDetailComponent_Conditional_21_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function StudyGroupDetailComponent_Conditional_21_Conditional_0_Conditional_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.editGroup());\n    });\n    i0.ɵɵtext(1, \"Editar\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StudyGroupDetailComponent_Conditional_21_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function StudyGroupDetailComponent_Conditional_21_Conditional_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openDiscord());\n    });\n    i0.ɵɵelement(1, \"mat-icon\", 9);\n    i0.ɵɵtext(2, \" Entrar no Discord \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, StudyGroupDetailComponent_Conditional_21_Conditional_0_Conditional_3_Template, 2, 0, \"button\", 10);\n    i0.ɵɵelementStart(4, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function StudyGroupDetailComponent_Conditional_21_Conditional_0_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.leaveGroup());\n    });\n    i0.ɵɵtext(5, \"Sair\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.discordInviteUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(3, ctx_r1.isOwnerId === true ? 3 : -1);\n  }\n}\nfunction StudyGroupDetailComponent_Conditional_21_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function StudyGroupDetailComponent_Conditional_21_Conditional_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.joinGroup());\n    });\n    i0.ɵɵtext(1, \"Entrar\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StudyGroupDetailComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, StudyGroupDetailComponent_Conditional_21_Conditional_0_Template, 6, 2)(1, StudyGroupDetailComponent_Conditional_21_Conditional_1_Template, 2, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r1.userInGroup === true ? 0 : 1);\n  }\n}\nexport let StudyGroupDetailComponent = /*#__PURE__*/(() => {\n  class StudyGroupDetailComponent {\n    constructor(snackBar, router, service, route, messageService) {\n      this.snackBar = snackBar;\n      this.router = router;\n      this.service = service;\n      this.route = route;\n      this.messageService = messageService;\n      this.diasSemana = ['dom', 'seg', 'ter', 'qua', 'qui', 'sex', 'sáb'];\n      this.loading = false;\n      this.discordInviteUrl = null;\n    }\n    ngOnInit() {\n      this.studyGroup = this.service.getStudyGroup();\n      this.callGroup();\n    }\n    openDiscord() {\n      if (this.discordInviteUrl) {\n        window.open(this.discordInviteUrl, '_blank', 'noopener,noreferrer');\n      } else {\n        this.snackBar.open('Link do Discord não disponível', 'Fechar', {\n          duration: 3000,\n          panelClass: ['error-snackbar']\n        });\n      }\n    }\n    joinGroup() {\n      const idUsuario = localStorage.getItem('idUsuario');\n      if (idUsuario !== null) {\n        const id = Number(idUsuario);\n        this.service.joinGroupService(this.studyGroup.id, id).subscribe({\n          next: response => {\n            this.snackBar.open('Entrou no grupo com sucesso!', 'X', {\n              duration: 2500\n            });\n            this.loading = false;\n            this.callGroup();\n          },\n          error: error => {\n            console.error('Erro ao entrar no grupo:', error);\n          }\n        });\n      } else {\n        console.error('ID do usuário não encontrado no localStorage');\n      }\n    }\n    leaveGroup() {\n      const idUsuario = localStorage.getItem('idUsuario');\n      this.loading = true;\n      if (idUsuario !== null) {\n        const id = Number(idUsuario);\n        this.service.leaveGroupService(this.studyGroup.id, id).subscribe({\n          next: resposta => {\n            this.snackBar.open('Saiu do grupo com sucesso!', 'X', {\n              duration: 5000\n            });\n            this.loading = false;\n            this.callGroup();\n          },\n          error: error => {\n            console.error('Erro ao sair do grupo:', error);\n            this.loading = false;\n            this.snackBar.open('Erro ao sair do grupo!', '', {\n              duration: 5000\n            });\n          }\n        });\n      } else {\n        console.error('ID do usuário não encontrado no localStorage');\n      }\n    }\n    editGroup() {\n      if (this.studyGroup) {\n        this.router.navigate(['/edit'], {\n          queryParams: {\n            id: this.studyGroup.id\n          }\n        });\n      }\n    }\n    callGroup() {\n      const idParam = this.route.snapshot.paramMap.get('groupId');\n      const idDetail = idParam ? Number(idParam) : null;\n      if (idDetail !== null) {\n        this.service.getStudyGroupId(idDetail).subscribe({\n          next: response => {\n            console.error('grupo de estudo - response:', response);\n            const mappedStudyGroup = this.service.mappingStudyGroup(response);\n            this.studyGroup = mappedStudyGroup;\n            this.discordInviteUrl = this.studyGroup.discordInviteUrl; // Assumindo que o invite está nesta propriedade\n            console.error('grupo de estudo - detalhe:', mappedStudyGroup);\n            const idUsuario = localStorage.getItem('idUsuario');\n            const id = Number(idUsuario);\n            if (this.studyGroup && Array.isArray(this.studyGroup.students)) {\n              const isStudentInGroup = this.studyGroup.students.some(student => student.id === id);\n              this.userInGroup = isStudentInGroup;\n              const isOwner = this.studyGroup.ownerId === id;\n              this.isOwnerId = isOwner;\n            }\n          },\n          error: error => {\n            this.router.navigate([`/search`]);\n          }\n        });\n      }\n    }\n    static #_ = this.ɵfac = function StudyGroupDetailComponent_Factory(t) {\n      return new (t || StudyGroupDetailComponent)(i0.ɵɵdirectiveInject(i1.MatSnackBar), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.StudyGroupService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MessageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudyGroupDetailComponent,\n      selectors: [[\"app-study-group-detail\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MessageService]), i0.ɵɵStandaloneFeature],\n      decls: 23,\n      vars: 6,\n      consts: [[1, \"allGrid\", \"m-top\"], [1, \"titulo\"], [1, \"subTitulo\"], [1, \"allBetween\", \"m-top\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"ariaLabel\", \"loading\", 1, \"spinerCenter\"], [3, \"ngClass\"], [2, \"margin-top\", \"12px\", 3, \"ngClass\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full_width\", \"mtop\", 3, \"click\", \"disabled\"], [\"fontIcon\", \"discord\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full_width\", \"mtop\"], [\"mat-raised-button\", \"\", 1, \"full_width\", \"mtop\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full_width\", \"mtop\", 3, \"click\"]],\n      template: function StudyGroupDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card-title\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"mat-card-subtitle\", 2);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"p\")(7, \"strong\");\n          i0.ɵɵtext(8, \"Alunos:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\")(11, \"strong\");\n          i0.ɵɵtext(12, \"Hora de In\\u00EDcio:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\")(15, \"p\")(16, \"strong\");\n          i0.ɵɵtext(17, \"Dias da Semana\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"mat-chip-set\");\n          i0.ɵɵtemplate(19, StudyGroupDetailComponent_mat_chip_19_Template, 4, 9, \"mat-chip\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(20, StudyGroupDetailComponent_Conditional_20_Template, 1, 0, \"p-progressSpinner\", 5)(21, StudyGroupDetailComponent_Conditional_21_Template, 2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"p-toast\");\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.studyGroup == null ? null : ctx.studyGroup.code) + \" - \" + (ctx.studyGroup == null ? null : ctx.studyGroup.title));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.studyGroup == null ? null : ctx.studyGroup.shortDescription);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.studyGroup == null ? null : ctx.studyGroup.participants, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.studyGroup == null ? null : ctx.studyGroup.hour, \"\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.diasSemana);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(20, ctx.loading === true ? 20 : 21);\n        }\n      },\n      dependencies: [CommonModule, i5.NgClass, i5.NgForOf, i5.TitleCasePipe, MatCardTitle, MatCardSubtitle, MatChipSet, MatChip, MatButton, MatIcon, ToastModule, i6.Toast, ProgressSpinnerModule, i7.ProgressSpinner],\n      styles: [\".container[_ngcontent-%COMP%]{width:100%;display:flex;flex-wrap:wrap;justify-content:center}mat-card[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;max-width:350px;min-width:300px;padding:20px 10px}mat-chip[_ngcontent-%COMP%]{margin:5px}.titulo[_ngcontent-%COMP%]{font-size:20px;font-weight:700}.subTitulo[_ngcontent-%COMP%]{color:#0000008a}.full_width[_ngcontent-%COMP%]{width:100%}.selected[_ngcontent-%COMP%]{background-color:#3f51b5!important}.selectedText[_ngcontent-%COMP%]{color:#fff}.mtop[_ngcontent-%COMP%]{margin-top:50px}.spinerCenter[_ngcontent-%COMP%]{text-align:center;margin-top:60px}\"]\n    });\n  }\n  return StudyGroupDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}