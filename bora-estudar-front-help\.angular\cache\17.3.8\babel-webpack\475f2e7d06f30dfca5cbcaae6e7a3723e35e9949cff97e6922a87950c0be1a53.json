{"ast": null, "code": "export const environment = {\n  development: true,\n  encodedApiUrl: 'http%3A%2F%2Flocalhost%3A8080',\n  authApi: '/api'\n};", "map": {"version": 3, "names": ["environment", "development", "encodedApiUrl", "authApi"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\environments\\environment.development.ts"], "sourcesContent": ["export const environment = {\r\n    development: true,\r\n    encodedApiUrl: 'http%3A%2F%2Flocalhost%3A8080'\r\n    authApi: '/api'\r\n};"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACvBC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,+BAA+B;EAC9CC,OAAO,EAAE;CACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}