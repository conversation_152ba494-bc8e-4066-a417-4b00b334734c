{"ast": null, "code": "import { MatButton } from '@angular/material/button';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../study-group.service\";\nexport let StudyGroupAssociateComponent = /*#__PURE__*/(() => {\n  class StudyGroupAssociateComponent {\n    constructor(service) {\n      this.service = service;\n      this.encodedApiUrl = environment.encodedApiUrl;\n    }\n    ngOnInit() {}\n    associate() {\n      const idUsuario = localStorage.getItem('idUsuario');\n      // const url = `https://discord.com/oauth2/authorize?client_id=1237632955145257021&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A8080%2Fdiscord%2Fusers&scope=identify&state=${idUsuario}`;\n      const url = `https://discord.com/oauth2/authorize?client_id=1237632955145257021&response_type=code&redirect_uri=${this.encodedApiUrl}%2Fdiscord%2Fusers&scope=identify&state=${idUsuario}`;\n      https: window.open(url, '_blank');\n    }\n    static #_ = this.ɵfac = function StudyGroupAssociateComponent_Factory(t) {\n      return new (t || StudyGroupAssociateComponent)(i0.ɵɵdirectiveInject(i1.StudyGroupService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StudyGroupAssociateComponent,\n      selectors: [[\"app-study-group-associate\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 0,\n      consts: [[1, \"m-padding\"], [1, \"allCenter\"], [1, \"titulo\"], [1, \"justify\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full_width\", \"m-top\", 3, \"click\"]],\n      template: function StudyGroupAssociateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"p\", 2)(3, \"b\");\n          i0.ɵɵtext(4, \"Associe sua conta no Discord para utilizar o Bora Estudar - UFF!\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(5, \"div\", 3)(6, \"p\");\n          i0.ɵɵtext(7, \"Para utilizar todas as fun\\u00E7\\u00F5es do Bora Estudar - UFF, \\u00E9 necess\\u00E1rio que voc\\u00EA associe uma conta no Discrod. Clique no bot\\u00E3o abaixo para fazer a associa\\u00E7\\u00E3o e comece a utilizar.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function StudyGroupAssociateComponent_Template_button_click_8_listener() {\n            return ctx.associate();\n          });\n          i0.ɵɵtext(9, \"Associe sua Conta!\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [MatButton],\n      styles: [\".allCenter[_ngcontent-%COMP%]{display:flex;justify-content:center}.titulo[_ngcontent-%COMP%]{font-size:1.3rem;text-align:center;margin:20px 0;line-height:1.5}.justify[_ngcontent-%COMP%]{display:flex;text-align:justify}.full_width[_ngcontent-%COMP%]{width:100%}.m-padding[_ngcontent-%COMP%]{padding:0 20px}.m-top[_ngcontent-%COMP%]{margin-top:30px}\"]\n    });\n  }\n  return StudyGroupAssociateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}