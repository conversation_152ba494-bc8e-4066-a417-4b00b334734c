{"ast": null, "code": "import { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nexport function tap(observerOrNext, error, complete) {\n  const tapObserver = isFunction(observerOrNext) || error || complete ? {\n    next: observerOrNext,\n    error,\n    complete\n  } : observerOrNext;\n  return tapObserver ? operate((source, subscriber) => {\n    var _a;\n    (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n    let isUnsub = true;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      var _a;\n      (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n      subscriber.next(value);\n    }, () => {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      subscriber.complete();\n    }, err => {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n      subscriber.error(err);\n    }, () => {\n      var _a, _b;\n      if (isUnsub) {\n        (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      }\n      (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n    }));\n  }) : identity;\n}\n//# sourceMappingURL=tap.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}