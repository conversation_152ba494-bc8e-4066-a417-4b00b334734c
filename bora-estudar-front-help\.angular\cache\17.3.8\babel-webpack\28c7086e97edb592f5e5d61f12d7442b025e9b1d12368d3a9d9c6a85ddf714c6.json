{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HeaderComponent } from './components/header/header.component';\nimport { UserProfileComponent } from './components/user-profile/user-profile.component';\nimport { AngularMaterialModule } from '../angular-material.module';\nimport * as i0 from \"@angular/core\";\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {\n    static #_ = this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, AngularMaterialModule, HeaderComponent, UserProfileComponent]\n    });\n  }\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}