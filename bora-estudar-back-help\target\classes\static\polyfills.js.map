{"version": 3, "file": "polyfills.js", "mappings": ";;;;;;;;;AAAa;;AACb;AACA;AACA;AACA;AACA;AACA,MAAMA,MAAM,GAAGC,UAAU;AACzB;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACtB,MAAMC,YAAY,GAAGJ,MAAM,CAAC,sBAAsB,CAAC,IAAI,iBAAiB;EACxE,OAAOI,YAAY,GAAGD,IAAI;AAC9B;AACA,SAASE,QAAQA,CAAA,EAAG;EAChB,MAAMC,WAAW,GAAGN,MAAM,CAAC,aAAa,CAAC;EACzC,SAASO,IAAIA,CAACJ,IAAI,EAAE;IAChBG,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACH,IAAI,CAAC;EACnE;EACA,SAASK,kBAAkBA,CAACL,IAAI,EAAEM,KAAK,EAAE;IACrCH,WAAW,IAAIA,WAAW,CAAC,SAAS,CAAC,IAAIA,WAAW,CAAC,SAAS,CAAC,CAACH,IAAI,EAAEM,KAAK,CAAC;EAChF;EACAF,IAAI,CAAC,MAAM,CAAC;EACZ,MAAMG,QAAQ,CAAC;IACX;IAAA,QAAAC,CAAA,GACS,IAAI,CAACT,UAAU,GAAGA,UAAU;IACrC,OAAOU,iBAAiBA,CAAA,EAAG;MACvB,IAAIZ,MAAM,CAAC,SAAS,CAAC,KAAKa,OAAO,CAAC,kBAAkB,CAAC,EAAE;QACnD,MAAM,IAAIC,KAAK,CAAC,uEAAuE,GACnF,yBAAyB,GACzB,+DAA+D,GAC/D,kFAAkF,GAClF,sDAAsD,CAAC;MAC/D;IACJ;IACA,WAAWC,IAAIA,CAAA,EAAG;MACd,IAAIC,IAAI,GAAGN,QAAQ,CAACO,OAAO;MAC3B,OAAOD,IAAI,CAACE,MAAM,EAAE;QAChBF,IAAI,GAAGA,IAAI,CAACE,MAAM;MACtB;MACA,OAAOF,IAAI;IACf;IACA,WAAWC,OAAOA,CAAA,EAAG;MACjB,OAAOE,iBAAiB,CAACH,IAAI;IACjC;IACA,WAAWI,WAAWA,CAAA,EAAG;MACrB,OAAOC,YAAY;IACvB;IACA;IACA,OAAOC,YAAYA,CAACnB,IAAI,EAAEoB,EAAE,EAAEC,eAAe,GAAG,KAAK,EAAE;MACnD,IAAIX,OAAO,CAACY,cAAc,CAACtB,IAAI,CAAC,EAAE;QAC9B;QACA;QACA;QACA,MAAMuB,cAAc,GAAG1B,MAAM,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;QAC7E,IAAI,CAACsB,eAAe,IAAIE,cAAc,EAAE;UACpC,MAAMZ,KAAK,CAAC,wBAAwB,GAAGX,IAAI,CAAC;QAChD;MACJ,CAAC,MACI,IAAI,CAACH,MAAM,CAAC,iBAAiB,GAAGG,IAAI,CAAC,EAAE;QACxC,MAAMwB,QAAQ,GAAG,OAAO,GAAGxB,IAAI;QAC/BI,IAAI,CAACoB,QAAQ,CAAC;QACdd,OAAO,CAACV,IAAI,CAAC,GAAGoB,EAAE,CAACvB,MAAM,EAAEU,QAAQ,EAAEkB,IAAI,CAAC;QAC1CpB,kBAAkB,CAACmB,QAAQ,EAAEA,QAAQ,CAAC;MAC1C;IACJ;IACA,IAAIT,MAAMA,CAAA,EAAG;MACT,OAAO,IAAI,CAACW,OAAO;IACvB;IACA,IAAI1B,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAAC2B,KAAK;IACrB;IACAC,WAAWA,CAACb,MAAM,EAAEc,QAAQ,EAAE;MAC1B,IAAI,CAACH,OAAO,GAAGX,MAAM;MACrB,IAAI,CAACY,KAAK,GAAGE,QAAQ,GAAGA,QAAQ,CAAC7B,IAAI,IAAI,SAAS,GAAG,QAAQ;MAC7D,IAAI,CAAC8B,WAAW,GAAID,QAAQ,IAAIA,QAAQ,CAACE,UAAU,IAAK,CAAC,CAAC;MAC1D,IAAI,CAACC,aAAa,GAAG,IAAIC,aAAa,CAAC,IAAI,EAAE,IAAI,CAACP,OAAO,IAAI,IAAI,CAACA,OAAO,CAACM,aAAa,EAAEH,QAAQ,CAAC;IACtG;IACAK,GAAGA,CAACC,GAAG,EAAE;MACL,MAAMtB,IAAI,GAAG,IAAI,CAACuB,WAAW,CAACD,GAAG,CAAC;MAClC,IAAItB,IAAI,EACJ,OAAOA,IAAI,CAACiB,WAAW,CAACK,GAAG,CAAC;IACpC;IACAC,WAAWA,CAACD,GAAG,EAAE;MACb,IAAIrB,OAAO,GAAG,IAAI;MAClB,OAAOA,OAAO,EAAE;QACZ,IAAIA,OAAO,CAACgB,WAAW,CAACR,cAAc,CAACa,GAAG,CAAC,EAAE;UACzC,OAAOrB,OAAO;QAClB;QACAA,OAAO,GAAGA,OAAO,CAACY,OAAO;MAC7B;MACA,OAAO,IAAI;IACf;IACAW,IAAIA,CAACR,QAAQ,EAAE;MACX,IAAI,CAACA,QAAQ,EACT,MAAM,IAAIlB,KAAK,CAAC,oBAAoB,CAAC;MACzC,OAAO,IAAI,CAACqB,aAAa,CAACK,IAAI,CAAC,IAAI,EAAER,QAAQ,CAAC;IAClD;IACAS,IAAIA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MACnB,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;QAChC,MAAM,IAAI5B,KAAK,CAAC,0BAA0B,GAAG4B,QAAQ,CAAC;MAC1D;MACA,MAAME,SAAS,GAAG,IAAI,CAACT,aAAa,CAACU,SAAS,CAAC,IAAI,EAAEH,QAAQ,EAAEC,MAAM,CAAC;MACtE,MAAM3B,IAAI,GAAG,IAAI;MACjB,OAAO,YAAY;QACf,OAAOA,IAAI,CAAC8B,UAAU,CAACF,SAAS,EAAE,IAAI,EAAEG,SAAS,EAAEJ,MAAM,CAAC;MAC9D,CAAC;IACL;IACAK,GAAGA,CAACN,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACxCxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,OAAO,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;MAClF,CAAC,SACO;QACJxB,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACA4B,UAAUA,CAACJ,QAAQ,EAAEO,SAAS,GAAG,IAAI,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACtDxB,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAI;UACA,OAAO,IAAI,CAACmB,aAAa,CAACgB,MAAM,CAAC,IAAI,EAAET,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC;QAClF,CAAC,CACD,OAAOS,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJjC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;MAChD;IACJ;IACAoC,OAAOA,CAACC,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAChC,IAAIK,IAAI,CAACvC,IAAI,IAAI,IAAI,EAAE;QACnB,MAAM,IAAIF,KAAK,CAAC,6DAA6D,GACzE,CAACyC,IAAI,CAACvC,IAAI,IAAIwC,OAAO,EAAErD,IAAI,GAC3B,eAAe,GACf,IAAI,CAACA,IAAI,GACT,GAAG,CAAC;MACZ;MACA;MACA;MACA;MACA,IAAIoD,IAAI,CAACE,KAAK,KAAKC,YAAY,KAAKH,IAAI,CAACI,IAAI,KAAKC,SAAS,IAAIL,IAAI,CAACI,IAAI,KAAKE,SAAS,CAAC,EAAE;QACrF;MACJ;MACA,MAAMC,YAAY,GAAGP,IAAI,CAACE,KAAK,IAAIM,OAAO;MAC1CD,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACD,OAAO,EAAEE,SAAS,CAAC;MACtDV,IAAI,CAACW,QAAQ,EAAE;MACf,MAAMC,YAAY,GAAG9C,YAAY;MACjCA,YAAY,GAAGkC,IAAI;MACnBpC,iBAAiB,GAAG;QAAED,MAAM,EAAEC,iBAAiB;QAAEH,IAAI,EAAE;MAAK,CAAC;MAC7D,IAAI;QACA,IAAIuC,IAAI,CAACI,IAAI,IAAIE,SAAS,IAAIN,IAAI,CAACa,IAAI,IAAI,CAACb,IAAI,CAACa,IAAI,CAACC,UAAU,EAAE;UAC9Dd,IAAI,CAACe,QAAQ,GAAGC,SAAS;QAC7B;QACA,IAAI;UACA,OAAO,IAAI,CAACpC,aAAa,CAACqC,UAAU,CAAC,IAAI,EAAEjB,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;QAC1E,CAAC,CACD,OAAOE,KAAK,EAAE;UACV,IAAI,IAAI,CAACjB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAED,KAAK,CAAC,EAAE;YAC7C,MAAMA,KAAK;UACf;QACJ;MACJ,CAAC,SACO;QACJ;QACA;QACA,IAAIG,IAAI,CAACE,KAAK,KAAKC,YAAY,IAAIH,IAAI,CAACE,KAAK,KAAKgB,OAAO,EAAE;UACvD,IAAIlB,IAAI,CAACI,IAAI,IAAIC,SAAS,IAAKL,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAACC,UAAW,EAAE;YAC/DP,YAAY,IAAIP,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEF,OAAO,CAAC;UAC1D,CAAC,MACI;YACDR,IAAI,CAACW,QAAQ,GAAG,CAAC;YACjB,IAAI,CAACQ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;YAC/BO,YAAY,IACRP,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEK,OAAO,EAAEL,YAAY,CAAC;UAC/D;QACJ;QACAvC,iBAAiB,GAAGA,iBAAiB,CAACD,MAAM;QAC5CG,YAAY,GAAG8C,YAAY;MAC/B;IACJ;IACAQ,YAAYA,CAACpB,IAAI,EAAE;MACf,IAAIA,IAAI,CAACvC,IAAI,IAAIuC,IAAI,CAACvC,IAAI,KAAK,IAAI,EAAE;QACjC;QACA;QACA,IAAI4D,OAAO,GAAG,IAAI;QAClB,OAAOA,OAAO,EAAE;UACZ,IAAIA,OAAO,KAAKrB,IAAI,CAACvC,IAAI,EAAE;YACvB,MAAMF,KAAK,CAAE,8BAA6B,IAAI,CAACX,IAAK,8CAA6CoD,IAAI,CAACvC,IAAI,CAACb,IAAK,EAAC,CAAC;UACtH;UACAyE,OAAO,GAAGA,OAAO,CAAC1D,MAAM;QAC5B;MACJ;MACAqC,IAAI,CAACS,aAAa,CAACa,UAAU,EAAEnB,YAAY,CAAC;MAC5C,MAAMoB,aAAa,GAAG,EAAE;MACxBvB,IAAI,CAACwB,cAAc,GAAGD,aAAa;MACnCvB,IAAI,CAACyB,KAAK,GAAG,IAAI;MACjB,IAAI;QACAzB,IAAI,GAAG,IAAI,CAACpB,aAAa,CAACwC,YAAY,CAAC,IAAI,EAAEpB,IAAI,CAAC;MACtD,CAAC,CACD,OAAO0B,GAAG,EAAE;QACR;QACA;QACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEI,UAAU,EAAEnB,YAAY,CAAC;QACrD;QACA,IAAI,CAACvB,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI1B,IAAI,CAACwB,cAAc,KAAKD,aAAa,EAAE;QACvC;QACA,IAAI,CAACJ,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC;MAClC;MACA,IAAIA,IAAI,CAACE,KAAK,IAAIoB,UAAU,EAAE;QAC1BtB,IAAI,CAACS,aAAa,CAACC,SAAS,EAAEY,UAAU,CAAC;MAC7C;MACA,OAAOtB,IAAI;IACf;IACA2B,iBAAiBA,CAACvC,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAE;MACtD,OAAO,IAAI,CAACR,YAAY,CAAC,IAAIS,QAAQ,CAACC,SAAS,EAAE1C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEZ,SAAS,CAAC,CAAC;IACxG;IACAe,iBAAiBA,CAAC3C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACvB,SAAS,EAAElB,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAC,iBAAiBA,CAAC7C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;MACpE,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAIS,QAAQ,CAACxB,SAAS,EAAEjB,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC,CAAC;IAC3G;IACAE,UAAUA,CAAClC,IAAI,EAAE;MACb,IAAIA,IAAI,CAACvC,IAAI,IAAI,IAAI,EACjB,MAAM,IAAIF,KAAK,CAAC,mEAAmE,GAC/E,CAACyC,IAAI,CAACvC,IAAI,IAAIwC,OAAO,EAAErD,IAAI,GAC3B,eAAe,GACf,IAAI,CAACA,IAAI,GACT,GAAG,CAAC;MACZ,IAAIoD,IAAI,CAACE,KAAK,KAAKQ,SAAS,IAAIV,IAAI,CAACE,KAAK,KAAKM,OAAO,EAAE;QACpD;MACJ;MACAR,IAAI,CAACS,aAAa,CAAC0B,SAAS,EAAEzB,SAAS,EAAEF,OAAO,CAAC;MACjD,IAAI;QACA,IAAI,CAAC5B,aAAa,CAACsD,UAAU,CAAC,IAAI,EAAElC,IAAI,CAAC;MAC7C,CAAC,CACD,OAAO0B,GAAG,EAAE;QACR;QACA1B,IAAI,CAACS,aAAa,CAACS,OAAO,EAAEiB,SAAS,CAAC;QACtC,IAAI,CAACvD,aAAa,CAACkB,WAAW,CAAC,IAAI,EAAE4B,GAAG,CAAC;QACzC,MAAMA,GAAG;MACb;MACA,IAAI,CAACP,gBAAgB,CAACnB,IAAI,EAAE,CAAC,CAAC,CAAC;MAC/BA,IAAI,CAACS,aAAa,CAACN,YAAY,EAAEgC,SAAS,CAAC;MAC3CnC,IAAI,CAACW,QAAQ,GAAG,CAAC;MACjB,OAAOX,IAAI;IACf;IACAmB,gBAAgBA,CAACnB,IAAI,EAAEoC,KAAK,EAAE;MAC1B,MAAMb,aAAa,GAAGvB,IAAI,CAACwB,cAAc;MACzC,IAAIY,KAAK,IAAI,CAAC,CAAC,EAAE;QACbpC,IAAI,CAACwB,cAAc,GAAG,IAAI;MAC9B;MACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,aAAa,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3Cd,aAAa,CAACc,CAAC,CAAC,CAAClB,gBAAgB,CAACnB,IAAI,CAACI,IAAI,EAAEgC,KAAK,CAAC;MACvD;IACJ;EACJ;EACA,MAAMG,WAAW,GAAG;IAChB3F,IAAI,EAAE,EAAE;IACR4F,SAAS,EAAEA,CAACC,QAAQ,EAAErF,CAAC,EAAEsF,MAAM,EAAEC,YAAY,KAAKF,QAAQ,CAACG,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;IACxFE,cAAc,EAAEA,CAACJ,QAAQ,EAAErF,CAAC,EAAEsF,MAAM,EAAE1C,IAAI,KAAKyC,QAAQ,CAACrB,YAAY,CAACsB,MAAM,EAAE1C,IAAI,CAAC;IAClF8C,YAAY,EAAEA,CAACL,QAAQ,EAAErF,CAAC,EAAEsF,MAAM,EAAE1C,IAAI,EAAEN,SAAS,EAAEC,SAAS,KAAK8C,QAAQ,CAACxB,UAAU,CAACyB,MAAM,EAAE1C,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC;IAC1HoD,YAAY,EAAEA,CAACN,QAAQ,EAAErF,CAAC,EAAEsF,MAAM,EAAE1C,IAAI,KAAKyC,QAAQ,CAACP,UAAU,CAACQ,MAAM,EAAE1C,IAAI;EACjF,CAAC;EACD,MAAMnB,aAAa,CAAC;IAChB,IAAIpB,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACgE,KAAK;IACrB;IACAjD,WAAWA,CAACf,IAAI,EAAEuF,cAAc,EAAEvE,QAAQ,EAAE;MACxC,IAAI,CAACwE,WAAW,GAAG;QACf,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,CAAC;QACd,WAAW,EAAE;MACjB,CAAC;MACD,IAAI,CAACxB,KAAK,GAAGhE,IAAI;MACjB,IAAI,CAACyF,eAAe,GAAGF,cAAc;MACrC,IAAI,CAACG,OAAO,GAAG1E,QAAQ,KAAKA,QAAQ,IAAIA,QAAQ,CAAC2E,MAAM,GAAG3E,QAAQ,GAAGuE,cAAc,CAACG,OAAO,CAAC;MAC5F,IAAI,CAACE,SAAS,GAAG5E,QAAQ,KAAKA,QAAQ,CAAC2E,MAAM,GAAGJ,cAAc,GAAGA,cAAc,CAACK,SAAS,CAAC;MAC1F,IAAI,CAACC,aAAa,GACd7E,QAAQ,KAAKA,QAAQ,CAAC2E,MAAM,GAAG,IAAI,CAAC3B,KAAK,GAAGuB,cAAc,CAACM,aAAa,CAAC;MAC7E,IAAI,CAACC,YAAY,GACb9E,QAAQ,KAAKA,QAAQ,CAAC+E,WAAW,GAAG/E,QAAQ,GAAGuE,cAAc,CAACO,YAAY,CAAC;MAC/E,IAAI,CAACE,cAAc,GACfhF,QAAQ,KAAKA,QAAQ,CAAC+E,WAAW,GAAGR,cAAc,GAAGA,cAAc,CAACS,cAAc,CAAC;MACvF,IAAI,CAACC,kBAAkB,GACnBjF,QAAQ,KAAKA,QAAQ,CAAC+E,WAAW,GAAG,IAAI,CAAC/B,KAAK,GAAGuB,cAAc,CAACU,kBAAkB,CAAC;MACvF,IAAI,CAACC,SAAS,GAAGlF,QAAQ,KAAKA,QAAQ,CAACmF,QAAQ,GAAGnF,QAAQ,GAAGuE,cAAc,CAACW,SAAS,CAAC;MACtF,IAAI,CAACE,WAAW,GACZpF,QAAQ,KAAKA,QAAQ,CAACmF,QAAQ,GAAGZ,cAAc,GAAGA,cAAc,CAACa,WAAW,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBrF,QAAQ,KAAKA,QAAQ,CAACmF,QAAQ,GAAG,IAAI,CAACnC,KAAK,GAAGuB,cAAc,CAACc,eAAe,CAAC;MACjF,IAAI,CAACC,cAAc,GACftF,QAAQ,KAAKA,QAAQ,CAACuF,aAAa,GAAGvF,QAAQ,GAAGuE,cAAc,CAACe,cAAc,CAAC;MACnF,IAAI,CAACE,gBAAgB,GACjBxF,QAAQ,KAAKA,QAAQ,CAACuF,aAAa,GAAGhB,cAAc,GAAGA,cAAc,CAACiB,gBAAgB,CAAC;MAC3F,IAAI,CAACC,oBAAoB,GACrBzF,QAAQ,KAAKA,QAAQ,CAACuF,aAAa,GAAG,IAAI,CAACvC,KAAK,GAAGuB,cAAc,CAACkB,oBAAoB,CAAC;MAC3F,IAAI,CAACC,eAAe,GAChB1F,QAAQ,KAAKA,QAAQ,CAACoE,cAAc,GAAGpE,QAAQ,GAAGuE,cAAc,CAACmB,eAAe,CAAC;MACrF,IAAI,CAACC,iBAAiB,GAClB3F,QAAQ,KAAKA,QAAQ,CAACoE,cAAc,GAAGG,cAAc,GAAGA,cAAc,CAACoB,iBAAiB,CAAC;MAC7F,IAAI,CAACC,qBAAqB,GACtB5F,QAAQ,KAAKA,QAAQ,CAACoE,cAAc,GAAG,IAAI,CAACpB,KAAK,GAAGuB,cAAc,CAACqB,qBAAqB,CAAC;MAC7F,IAAI,CAACC,aAAa,GACd7F,QAAQ,KAAKA,QAAQ,CAACqE,YAAY,GAAGrE,QAAQ,GAAGuE,cAAc,CAACsB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChB9F,QAAQ,KAAKA,QAAQ,CAACqE,YAAY,GAAGE,cAAc,GAAGA,cAAc,CAACuB,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpB/F,QAAQ,KAAKA,QAAQ,CAACqE,YAAY,GAAG,IAAI,CAACrB,KAAK,GAAGuB,cAAc,CAACwB,mBAAmB,CAAC;MACzF,IAAI,CAACC,aAAa,GACdhG,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAGtE,QAAQ,GAAGuE,cAAc,CAACyB,aAAa,CAAC;MACjF,IAAI,CAACC,eAAe,GAChBjG,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAGC,cAAc,GAAGA,cAAc,CAAC0B,eAAe,CAAC;MACzF,IAAI,CAACC,mBAAmB,GACpBlG,QAAQ,KAAKA,QAAQ,CAACsE,YAAY,GAAG,IAAI,CAACtB,KAAK,GAAGuB,cAAc,CAAC2B,mBAAmB,CAAC;MACzF,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,MAAMC,eAAe,GAAGvG,QAAQ,IAAIA,QAAQ,CAAC+D,SAAS;MACtD,MAAMyC,aAAa,GAAGjC,cAAc,IAAIA,cAAc,CAAC4B,UAAU;MACjE,IAAII,eAAe,IAAIC,aAAa,EAAE;QAClC;QACA;QACA,IAAI,CAACL,UAAU,GAAGI,eAAe,GAAGvG,QAAQ,GAAG8D,WAAW;QAC1D,IAAI,CAACsC,YAAY,GAAG7B,cAAc;QAClC,IAAI,CAAC8B,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACtD,KAAK;QAClC,IAAI,CAAChD,QAAQ,CAACoE,cAAc,EAAE;UAC1B,IAAI,CAACsB,eAAe,GAAG5B,WAAW;UAClC,IAAI,CAAC6B,iBAAiB,GAAGpB,cAAc;UACvC,IAAI,CAACqB,qBAAqB,GAAG,IAAI,CAAC5C,KAAK;QAC3C;QACA,IAAI,CAAChD,QAAQ,CAACqE,YAAY,EAAE;UACxB,IAAI,CAACwB,aAAa,GAAG/B,WAAW;UAChC,IAAI,CAACgC,eAAe,GAAGvB,cAAc;UACrC,IAAI,CAACwB,mBAAmB,GAAG,IAAI,CAAC/C,KAAK;QACzC;QACA,IAAI,CAAChD,QAAQ,CAACsE,YAAY,EAAE;UACxB,IAAI,CAAC0B,aAAa,GAAGlC,WAAW;UAChC,IAAI,CAACmC,eAAe,GAAG1B,cAAc;UACrC,IAAI,CAAC2B,mBAAmB,GAAG,IAAI,CAAClD,KAAK;QACzC;MACJ;IACJ;IACAxC,IAAIA,CAACiG,UAAU,EAAEzG,QAAQ,EAAE;MACvB,OAAO,IAAI,CAAC0E,OAAO,GACb,IAAI,CAACA,OAAO,CAACC,MAAM,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC5F,IAAI,EAAEyH,UAAU,EAAEzG,QAAQ,CAAC,GACpE,IAAItB,QAAQ,CAAC+H,UAAU,EAAEzG,QAAQ,CAAC;IAC5C;IACAa,SAASA,CAAC4F,UAAU,EAAE/F,QAAQ,EAAEC,MAAM,EAAE;MACpC,OAAO,IAAI,CAACmE,YAAY,GAClB,IAAI,CAACA,YAAY,CAACC,WAAW,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,kBAAkB,EAAEwB,UAAU,EAAE/F,QAAQ,EAAEC,MAAM,CAAC,GACzGD,QAAQ;IAClB;IACAS,MAAMA,CAACsF,UAAU,EAAE/F,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,EAAE;MACvD,OAAO,IAAI,CAACuE,SAAS,GACf,IAAI,CAACA,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,eAAe,EAAEoB,UAAU,EAAE/F,QAAQ,EAAEO,SAAS,EAAEC,SAAS,EAAEP,MAAM,CAAC,GACnHD,QAAQ,CAACgG,KAAK,CAACzF,SAAS,EAAEC,SAAS,CAAC;IAC9C;IACAG,WAAWA,CAACoF,UAAU,EAAErF,KAAK,EAAE;MAC3B,OAAO,IAAI,CAACkE,cAAc,GACpB,IAAI,CAACA,cAAc,CAACC,aAAa,CAAC,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACC,oBAAoB,EAAEgB,UAAU,EAAErF,KAAK,CAAC,GACtG,IAAI;IACd;IACAuB,YAAYA,CAAC8D,UAAU,EAAElF,IAAI,EAAE;MAC3B,IAAIoF,UAAU,GAAGpF,IAAI;MACrB,IAAI,IAAI,CAACmE,eAAe,EAAE;QACtB,IAAI,IAAI,CAACS,UAAU,EAAE;UACjBQ,UAAU,CAAC5D,cAAc,CAAC6D,IAAI,CAAC,IAAI,CAACP,iBAAiB,CAAC;QAC1D;QACAM,UAAU,GAAG,IAAI,CAACjB,eAAe,CAACtB,cAAc,CAAC,IAAI,CAACuB,iBAAiB,EAAE,IAAI,CAACC,qBAAqB,EAAEa,UAAU,EAAElF,IAAI,CAAC;QACtH,IAAI,CAACoF,UAAU,EACXA,UAAU,GAAGpF,IAAI;MACzB,CAAC,MACI;QACD,IAAIA,IAAI,CAACsF,UAAU,EAAE;UACjBtF,IAAI,CAACsF,UAAU,CAACtF,IAAI,CAAC;QACzB,CAAC,MACI,IAAIA,IAAI,CAACI,IAAI,IAAI0B,SAAS,EAAE;UAC7BH,iBAAiB,CAAC3B,IAAI,CAAC;QAC3B,CAAC,MACI;UACD,MAAM,IAAIzC,KAAK,CAAC,6BAA6B,CAAC;QAClD;MACJ;MACA,OAAO6H,UAAU;IACrB;IACAnE,UAAUA,CAACiE,UAAU,EAAElF,IAAI,EAAEN,SAAS,EAAEC,SAAS,EAAE;MAC/C,OAAO,IAAI,CAAC2E,aAAa,GACnB,IAAI,CAACA,aAAa,CAACxB,YAAY,CAAC,IAAI,CAACyB,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEU,UAAU,EAAElF,IAAI,EAAEN,SAAS,EAAEC,SAAS,CAAC,GACvHK,IAAI,CAACb,QAAQ,CAACgG,KAAK,CAACzF,SAAS,EAAEC,SAAS,CAAC;IACnD;IACAuC,UAAUA,CAACgD,UAAU,EAAElF,IAAI,EAAE;MACzB,IAAIuF,KAAK;MACT,IAAI,IAAI,CAACd,aAAa,EAAE;QACpBc,KAAK,GAAG,IAAI,CAACd,aAAa,CAAC1B,YAAY,CAAC,IAAI,CAAC2B,eAAe,EAAE,IAAI,CAACC,mBAAmB,EAAEO,UAAU,EAAElF,IAAI,CAAC;MAC7G,CAAC,MACI;QACD,IAAI,CAACA,IAAI,CAACe,QAAQ,EAAE;UAChB,MAAMxD,KAAK,CAAC,wBAAwB,CAAC;QACzC;QACAgI,KAAK,GAAGvF,IAAI,CAACe,QAAQ,CAACf,IAAI,CAAC;MAC/B;MACA,OAAOuF,KAAK;IAChB;IACA3C,OAAOA,CAACsC,UAAU,EAAEM,OAAO,EAAE;MACzB;MACA;MACA,IAAI;QACA,IAAI,CAACZ,UAAU,IACX,IAAI,CAACA,UAAU,CAACpC,SAAS,CAAC,IAAI,CAACqC,YAAY,EAAE,IAAI,CAACE,gBAAgB,EAAEG,UAAU,EAAEM,OAAO,CAAC;MAChG,CAAC,CACD,OAAO9D,GAAG,EAAE;QACR,IAAI,CAAC5B,WAAW,CAACoF,UAAU,EAAExD,GAAG,CAAC;MACrC;IACJ;IACA;IACAP,gBAAgBA,CAACf,IAAI,EAAEgC,KAAK,EAAE;MAC1B,MAAMqD,MAAM,GAAG,IAAI,CAACxC,WAAW;MAC/B,MAAMyC,IAAI,GAAGD,MAAM,CAACrF,IAAI,CAAC;MACzB,MAAMuF,IAAI,GAAIF,MAAM,CAACrF,IAAI,CAAC,GAAGsF,IAAI,GAAGtD,KAAM;MAC1C,IAAIuD,IAAI,GAAG,CAAC,EAAE;QACV,MAAM,IAAIpI,KAAK,CAAC,0CAA0C,CAAC;MAC/D;MACA,IAAImI,IAAI,IAAI,CAAC,IAAIC,IAAI,IAAI,CAAC,EAAE;QACxB,MAAMH,OAAO,GAAG;UACZ1D,SAAS,EAAE2D,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCnF,SAAS,EAAEmF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCpF,SAAS,EAAEoF,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC;UAClCG,MAAM,EAAExF;QACZ,CAAC;QACD,IAAI,CAACwC,OAAO,CAAC,IAAI,CAACnB,KAAK,EAAE+D,OAAO,CAAC;MACrC;IACJ;EACJ;EACA,MAAM3D,QAAQ,CAAC;IACXrD,WAAWA,CAAC4B,IAAI,EAAEhB,MAAM,EAAED,QAAQ,EAAE0G,OAAO,EAAEP,UAAU,EAAEvE,QAAQ,EAAE;MAC/D;MACA,IAAI,CAACU,KAAK,GAAG,IAAI;MACjB,IAAI,CAACd,QAAQ,GAAG,CAAC;MACjB;MACA,IAAI,CAACa,cAAc,GAAG,IAAI;MAC1B;MACA,IAAI,CAACsE,MAAM,GAAG,cAAc;MAC5B,IAAI,CAAC1F,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAChB,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACyB,IAAI,GAAGgF,OAAO;MACnB,IAAI,CAACP,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACvE,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAAC5B,QAAQ,EAAE;QACX,MAAM,IAAI5B,KAAK,CAAC,yBAAyB,CAAC;MAC9C;MACA,IAAI,CAAC4B,QAAQ,GAAGA,QAAQ;MACxB,MAAM4G,IAAI,GAAG,IAAI;MACjB;MACA,IAAI3F,IAAI,KAAKC,SAAS,IAAIwF,OAAO,IAAIA,OAAO,CAACG,IAAI,EAAE;QAC/C,IAAI,CAACpG,MAAM,GAAGiC,QAAQ,CAACZ,UAAU;MACrC,CAAC,MACI;QACD,IAAI,CAACrB,MAAM,GAAG,YAAY;UACtB,OAAOiC,QAAQ,CAACZ,UAAU,CAACgF,IAAI,CAACxJ,MAAM,EAAEsJ,IAAI,EAAE,IAAI,EAAEvG,SAAS,CAAC;QAClE,CAAC;MACL;IACJ;IACA,OAAOyB,UAAUA,CAACjB,IAAI,EAAE0C,MAAM,EAAEwD,IAAI,EAAE;MAClC,IAAI,CAAClG,IAAI,EAAE;QACPA,IAAI,GAAG,IAAI;MACf;MACAmG,yBAAyB,EAAE;MAC3B,IAAI;QACAnG,IAAI,CAACW,QAAQ,EAAE;QACf,OAAOX,IAAI,CAACvC,IAAI,CAACsC,OAAO,CAACC,IAAI,EAAE0C,MAAM,EAAEwD,IAAI,CAAC;MAChD,CAAC,SACO;QACJ,IAAIC,yBAAyB,IAAI,CAAC,EAAE;UAChCC,mBAAmB,CAAC,CAAC;QACzB;QACAD,yBAAyB,EAAE;MAC/B;IACJ;IACA,IAAI1I,IAAIA,CAAA,EAAG;MACP,OAAO,IAAI,CAACgE,KAAK;IACrB;IACA,IAAIvB,KAAKA,CAAA,EAAG;MACR,OAAO,IAAI,CAAC4F,MAAM;IACtB;IACAO,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC5F,aAAa,CAACN,YAAY,EAAEmB,UAAU,CAAC;IAChD;IACA;IACAb,aAAaA,CAAC6F,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;MAC3C,IAAI,IAAI,CAACV,MAAM,KAAKS,UAAU,IAAI,IAAI,CAACT,MAAM,KAAKU,UAAU,EAAE;QAC1D,IAAI,CAACV,MAAM,GAAGQ,OAAO;QACrB,IAAIA,OAAO,IAAInG,YAAY,EAAE;UACzB,IAAI,CAACqB,cAAc,GAAG,IAAI;QAC9B;MACJ,CAAC,MACI;QACD,MAAM,IAAIjE,KAAK,CAAE,GAAE,IAAI,CAAC6C,IAAK,KAAI,IAAI,CAAChB,MAAO,6BAA4BkH,OAAQ,uBAAsBC,UAAW,IAAGC,UAAU,GAAG,OAAO,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAG,UAAS,IAAI,CAACV,MAAO,IAAG,CAAC;MAC/L;IACJ;IACAW,QAAQA,CAAA,EAAG;MACP,IAAI,IAAI,CAAC5F,IAAI,IAAI,OAAO,IAAI,CAACA,IAAI,CAAC6F,QAAQ,KAAK,WAAW,EAAE;QACxD,OAAO,IAAI,CAAC7F,IAAI,CAAC6F,QAAQ,CAACD,QAAQ,CAAC,CAAC;MACxC,CAAC,MACI;QACD,OAAOE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;MAC/C;IACJ;IACA;IACA;IACAY,MAAMA,CAAA,EAAG;MACL,OAAO;QACHzG,IAAI,EAAE,IAAI,CAACA,IAAI;QACfF,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBd,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB3B,IAAI,EAAE,IAAI,CAACA,IAAI,CAACb,IAAI;QACpB+D,QAAQ,EAAE,IAAI,CAACA;MACnB,CAAC;IACL;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMmG,gBAAgB,GAAGnK,UAAU,CAAC,YAAY,CAAC;EACjD,MAAMoK,aAAa,GAAGpK,UAAU,CAAC,SAAS,CAAC;EAC3C,MAAMqK,UAAU,GAAGrK,UAAU,CAAC,MAAM,CAAC;EACrC,IAAIsK,eAAe,GAAG,EAAE;EACxB,IAAIC,yBAAyB,GAAG,KAAK;EACrC,IAAIC,2BAA2B;EAC/B,SAASC,uBAAuBA,CAACC,IAAI,EAAE;IACnC,IAAI,CAACF,2BAA2B,EAAE;MAC9B,IAAI1K,MAAM,CAACsK,aAAa,CAAC,EAAE;QACvBI,2BAA2B,GAAG1K,MAAM,CAACsK,aAAa,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC;MAClE;IACJ;IACA,IAAIH,2BAA2B,EAAE;MAC7B,IAAII,UAAU,GAAGJ,2BAA2B,CAACH,UAAU,CAAC;MACxD,IAAI,CAACO,UAAU,EAAE;QACb;QACA;QACAA,UAAU,GAAGJ,2BAA2B,CAAC,MAAM,CAAC;MACpD;MACAI,UAAU,CAACtB,IAAI,CAACkB,2BAA2B,EAAEE,IAAI,CAAC;IACtD,CAAC,MACI;MACD5K,MAAM,CAACqK,gBAAgB,CAAC,CAACO,IAAI,EAAE,CAAC,CAAC;IACrC;EACJ;EACA,SAAS1F,iBAAiBA,CAAC3B,IAAI,EAAE;IAC7B;IACA;IACA,IAAImG,yBAAyB,KAAK,CAAC,IAAIc,eAAe,CAAC3E,MAAM,KAAK,CAAC,EAAE;MACjE;MACA8E,uBAAuB,CAAChB,mBAAmB,CAAC;IAChD;IACApG,IAAI,IAAIiH,eAAe,CAAC5B,IAAI,CAACrF,IAAI,CAAC;EACtC;EACA,SAASoG,mBAAmBA,CAAA,EAAG;IAC3B,IAAI,CAACc,yBAAyB,EAAE;MAC5BA,yBAAyB,GAAG,IAAI;MAChC,OAAOD,eAAe,CAAC3E,MAAM,EAAE;QAC3B,MAAMkF,KAAK,GAAGP,eAAe;QAC7BA,eAAe,GAAG,EAAE;QACpB,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,KAAK,CAAClF,MAAM,EAAED,CAAC,EAAE,EAAE;UACnC,MAAMrC,IAAI,GAAGwH,KAAK,CAACnF,CAAC,CAAC;UACrB,IAAI;YACArC,IAAI,CAACvC,IAAI,CAACsC,OAAO,CAACC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACvC,CAAC,CACD,OAAOH,KAAK,EAAE;YACVxB,IAAI,CAACoJ,gBAAgB,CAAC5H,KAAK,CAAC;UAChC;QACJ;MACJ;MACAxB,IAAI,CAACqJ,kBAAkB,CAAC,CAAC;MACzBR,yBAAyB,GAAG,KAAK;IACrC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,MAAMjH,OAAO,GAAG;IAAErD,IAAI,EAAE;EAAU,CAAC;EACnC,MAAMuD,YAAY,GAAG,cAAc;IAAEmB,UAAU,GAAG,YAAY;IAAEZ,SAAS,GAAG,WAAW;IAAEF,OAAO,GAAG,SAAS;IAAE2B,SAAS,GAAG,WAAW;IAAEjB,OAAO,GAAG,SAAS;EAC1J,MAAMY,SAAS,GAAG,WAAW;IAAExB,SAAS,GAAG,WAAW;IAAED,SAAS,GAAG,WAAW;EAC/E,MAAM/C,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMe,IAAI,GAAG;IACTsJ,MAAM,EAAEhL,UAAU;IAClBiL,gBAAgB,EAAEA,CAAA,KAAMhK,iBAAiB;IACzC6J,gBAAgB,EAAEI,IAAI;IACtBH,kBAAkB,EAAEG,IAAI;IACxBlG,iBAAiB,EAAEA,iBAAiB;IACpCmG,iBAAiB,EAAEA,CAAA,KAAM,CAAC3K,QAAQ,CAACR,UAAU,CAAC,iCAAiC,CAAC,CAAC;IACjFoL,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,iBAAiB,EAAEH,IAAI;IACvBI,WAAW,EAAEA,CAAA,KAAMJ,IAAI;IACvBK,aAAa,EAAEA,CAAA,KAAM,EAAE;IACvBC,SAAS,EAAEA,CAAA,KAAMN,IAAI;IACrBO,cAAc,EAAEA,CAAA,KAAMP,IAAI;IAC1BQ,mBAAmB,EAAEA,CAAA,KAAMR,IAAI;IAC/BS,UAAU,EAAEA,CAAA,KAAM,KAAK;IACvBC,gBAAgB,EAAEA,CAAA,KAAMvH,SAAS;IACjCwH,oBAAoB,EAAEA,CAAA,KAAMX,IAAI;IAChCY,8BAA8B,EAAEA,CAAA,KAAMzH,SAAS;IAC/C0H,YAAY,EAAEA,CAAA,KAAM1H,SAAS;IAC7B2H,UAAU,EAAEA,CAAA,KAAM,EAAE;IACpBC,UAAU,EAAEA,CAAA,KAAMf,IAAI;IACtBgB,mBAAmB,EAAEA,CAAA,KAAMhB,IAAI;IAC/BiB,gBAAgB,EAAEA,CAAA,KAAM,EAAE;IAC1BC,qBAAqB,EAAEA,CAAA,KAAMlB,IAAI;IACjCmB,iBAAiB,EAAEA,CAAA,KAAMnB,IAAI;IAC7BoB,cAAc,EAAEA,CAAA,KAAMpB,IAAI;IAC1BT,uBAAuB,EAAEA;EAC7B,CAAC;EACD,IAAIxJ,iBAAiB,GAAG;IAAED,MAAM,EAAE,IAAI;IAAEF,IAAI,EAAE,IAAIN,QAAQ,CAAC,IAAI,EAAE,IAAI;EAAE,CAAC;EACxE,IAAIW,YAAY,GAAG,IAAI;EACvB,IAAIqI,yBAAyB,GAAG,CAAC;EACjC,SAAS0B,IAAIA,CAAA,EAAG,CAAE;EAClB5K,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC;EAClC,OAAOE,QAAQ;AACnB;AAEA,SAAS+L,QAAQA,CAAA,EAAG;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMzM,MAAM,GAAGC,UAAU;EACzB,MAAMyB,cAAc,GAAG1B,MAAM,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,KAAK,IAAI;EAC7E,IAAIF,MAAM,CAAC,MAAM,CAAC,KAAK0B,cAAc,IAAI,OAAO1B,MAAM,CAAC,MAAM,CAAC,CAACE,UAAU,KAAK,UAAU,CAAC,EAAE;IACvF,MAAM,IAAIY,KAAK,CAAC,sBAAsB,CAAC;EAC3C;EACA;EACAd,MAAM,CAAC,MAAM,CAAC,KAAKK,QAAQ,CAAC,CAAC;EAC7B,OAAOL,MAAM,CAAC,MAAM,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgM,8BAA8B,GAAG9B,MAAM,CAACwC,wBAAwB;AACtE;AACA,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAAc;AAClD;AACA,MAAMC,oBAAoB,GAAG1C,MAAM,CAAC2C,cAAc;AAClD;AACA,MAAMZ,YAAY,GAAG/B,MAAM,CAAC4C,MAAM;AAClC;AACA,MAAMZ,UAAU,GAAGa,KAAK,CAAC5C,SAAS,CAAC6C,KAAK;AACxC;AACA,MAAMC,sBAAsB,GAAG,kBAAkB;AACjD;AACA,MAAMC,yBAAyB,GAAG,qBAAqB;AACvD;AACA,MAAMC,8BAA8B,GAAGjN,UAAU,CAAC+M,sBAAsB,CAAC;AACzE;AACA,MAAMG,iCAAiC,GAAGlN,UAAU,CAACgN,yBAAyB,CAAC;AAC/E;AACA,MAAMG,QAAQ,GAAG,MAAM;AACvB;AACA,MAAMC,SAAS,GAAG,OAAO;AACzB;AACA,MAAMC,kBAAkB,GAAGrN,UAAU,CAAC,EAAE,CAAC;AACzC,SAASkM,mBAAmBA,CAAC1J,QAAQ,EAAEC,MAAM,EAAE;EAC3C,OAAO6K,IAAI,CAACvM,OAAO,CAACwB,IAAI,CAACC,QAAQ,EAAEC,MAAM,CAAC;AAC9C;AACA,SAAS8K,gCAAgCA,CAAC9K,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,EAAE;EAC5F,OAAOiI,IAAI,CAACvM,OAAO,CAACqE,iBAAiB,CAAC3C,MAAM,EAAED,QAAQ,EAAE0B,IAAI,EAAEe,cAAc,EAAEI,YAAY,CAAC;AAC/F;AACA,MAAMmI,UAAU,GAAGxN,UAAU;AAC7B,MAAMyN,cAAc,GAAG,OAAOC,MAAM,KAAK,WAAW;AACpD,MAAMC,cAAc,GAAGF,cAAc,GAAGC,MAAM,GAAGrJ,SAAS;AAC1D,MAAMuJ,OAAO,GAAIH,cAAc,IAAIE,cAAc,IAAK5N,UAAU;AAChE,MAAM8N,gBAAgB,GAAG,iBAAiB;AAC1C,SAAStC,aAAaA,CAAChC,IAAI,EAAE9G,MAAM,EAAE;EACjC,KAAK,IAAIiD,CAAC,GAAG6D,IAAI,CAAC5D,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAI,OAAO6D,IAAI,CAAC7D,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/B6D,IAAI,CAAC7D,CAAC,CAAC,GAAGwG,mBAAmB,CAAC3C,IAAI,CAAC7D,CAAC,CAAC,EAAEjD,MAAM,GAAG,GAAG,GAAGiD,CAAC,CAAC;IAC5D;EACJ;EACA,OAAO6D,IAAI;AACf;AACA,SAASuE,cAAcA,CAAC7D,SAAS,EAAE8D,OAAO,EAAE;EACxC,MAAMtL,MAAM,GAAGwH,SAAS,CAACpI,WAAW,CAAC,MAAM,CAAC;EAC5C,KAAK,IAAI6D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqI,OAAO,CAACpI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMzF,IAAI,GAAG8N,OAAO,CAACrI,CAAC,CAAC;IACvB,MAAMI,QAAQ,GAAGmE,SAAS,CAAChK,IAAI,CAAC;IAChC,IAAI6F,QAAQ,EAAE;MACV,MAAMkI,aAAa,GAAGlC,8BAA8B,CAAC7B,SAAS,EAAEhK,IAAI,CAAC;MACrE,IAAI,CAACgO,kBAAkB,CAACD,aAAa,CAAC,EAAE;QACpC;MACJ;MACA/D,SAAS,CAAChK,IAAI,CAAC,GAAG,CAAE6F,QAAQ,IAAK;QAC7B,MAAMoI,OAAO,GAAG,SAAAA,CAAA,EAAY;UACxB,OAAOpI,QAAQ,CAAC0C,KAAK,CAAC,IAAI,EAAE+C,aAAa,CAAC1I,SAAS,EAAEJ,MAAM,GAAG,GAAG,GAAGxC,IAAI,CAAC,CAAC;QAC9E,CAAC;QACDmM,qBAAqB,CAAC8B,OAAO,EAAEpI,QAAQ,CAAC;QACxC,OAAOoI,OAAO;MAClB,CAAC,EAAEpI,QAAQ,CAAC;IAChB;EACJ;AACJ;AACA,SAASmI,kBAAkBA,CAACE,YAAY,EAAE;EACtC,IAAI,CAACA,YAAY,EAAE;IACf,OAAO,IAAI;EACf;EACA,IAAIA,YAAY,CAACC,QAAQ,KAAK,KAAK,EAAE;IACjC,OAAO,KAAK;EAChB;EACA,OAAO,EAAE,OAAOD,YAAY,CAAChM,GAAG,KAAK,UAAU,IAAI,OAAOgM,YAAY,CAACE,GAAG,KAAK,WAAW,CAAC;AAC/F;AACA,MAAMC,WAAW,GAAG,OAAOC,iBAAiB,KAAK,WAAW,IAAInF,IAAI,YAAYmF,iBAAiB;AACjG;AACA;AACA,MAAMC,MAAM,GAAG,EAAE,IAAI,IAAIZ,OAAO,CAAC,IAC7B,OAAOA,OAAO,CAACa,OAAO,KAAK,WAAW,IACtCb,OAAO,CAACa,OAAO,CAAC3E,QAAQ,CAAC,CAAC,KAAK,kBAAkB;AACrD,MAAM4E,SAAS,GAAG,CAACF,MAAM,IAAI,CAACF,WAAW,IAAI,CAAC,EAAEb,cAAc,IAAIE,cAAc,CAAC,aAAa,CAAC,CAAC;AAChG;AACA;AACA;AACA,MAAMgB,KAAK,GAAG,OAAOf,OAAO,CAACa,OAAO,KAAK,WAAW,IAChDb,OAAO,CAACa,OAAO,CAAC3E,QAAQ,CAAC,CAAC,KAAK,kBAAkB,IACjD,CAACwE,WAAW,IACZ,CAAC,EAAEb,cAAc,IAAIE,cAAc,CAAC,aAAa,CAAC,CAAC;AACvD,MAAMiB,sBAAsB,GAAG,CAAC,CAAC;AACjC,MAAMC,MAAM,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAC5B;EACA;EACAA,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAK;EAC9B,IAAI,CAACA,KAAK,EAAE;IACR;EACJ;EACA,IAAIC,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACrL,IAAI,CAAC;EACxD,IAAI,CAACsL,eAAe,EAAE;IAClBA,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACrL,IAAI,CAAC,GAAG+J,UAAU,CAAC,aAAa,GAAGsB,KAAK,CAACrL,IAAI,CAAC;EACjG;EACA,MAAMsC,MAAM,GAAG,IAAI,IAAI+I,KAAK,CAAC/I,MAAM,IAAI6H,OAAO;EAC9C,MAAMoB,QAAQ,GAAGjJ,MAAM,CAACgJ,eAAe,CAAC;EACxC,IAAIE,MAAM;EACV,IAAIP,SAAS,IAAI3I,MAAM,KAAK4H,cAAc,IAAImB,KAAK,CAACrL,IAAI,KAAK,OAAO,EAAE;IAClE;IACA;IACA;IACA,MAAMyL,UAAU,GAAGJ,KAAK;IACxBG,MAAM,GACFD,QAAQ,IACJA,QAAQ,CAAC1F,IAAI,CAAC,IAAI,EAAE4F,UAAU,CAACC,OAAO,EAAED,UAAU,CAACE,QAAQ,EAAEF,UAAU,CAACG,MAAM,EAAEH,UAAU,CAACI,KAAK,EAAEJ,UAAU,CAAChM,KAAK,CAAC;IAC3H,IAAI+L,MAAM,KAAK,IAAI,EAAE;MACjBH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ,CAAC,MACI;IACDN,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACxG,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IACpD,IAAIoM,MAAM,IAAI5K,SAAS,IAAI,CAAC4K,MAAM,EAAE;MAChCH,KAAK,CAACS,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA,OAAON,MAAM;AACjB,CAAC;AACD,SAASO,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAEzF,SAAS,EAAE;EACzC,IAAI0F,IAAI,GAAG7D,8BAA8B,CAAC2D,GAAG,EAAEC,IAAI,CAAC;EACpD,IAAI,CAACC,IAAI,IAAI1F,SAAS,EAAE;IACpB;IACA,MAAM+D,aAAa,GAAGlC,8BAA8B,CAAC7B,SAAS,EAAEyF,IAAI,CAAC;IACrE,IAAI1B,aAAa,EAAE;MACf2B,IAAI,GAAG;QAAEC,UAAU,EAAE,IAAI;QAAEC,YAAY,EAAE;MAAK,CAAC;IACnD;EACJ;EACA;EACA;EACA,IAAI,CAACF,IAAI,IAAI,CAACA,IAAI,CAACE,YAAY,EAAE;IAC7B;EACJ;EACA,MAAMC,mBAAmB,GAAGtC,UAAU,CAAC,IAAI,GAAGkC,IAAI,GAAG,SAAS,CAAC;EAC/D,IAAID,GAAG,CAAClO,cAAc,CAACuO,mBAAmB,CAAC,IAAIL,GAAG,CAACK,mBAAmB,CAAC,EAAE;IACrE;EACJ;EACA;EACA;EACA;EACA;EACA;EACA,OAAOH,IAAI,CAACvB,QAAQ;EACpB,OAAOuB,IAAI,CAAC/G,KAAK;EACjB,MAAMmH,eAAe,GAAGJ,IAAI,CAACxN,GAAG;EAChC,MAAM6N,eAAe,GAAGL,IAAI,CAACtB,GAAG;EAChC;EACA,MAAM4B,SAAS,GAAGP,IAAI,CAAC5C,KAAK,CAAC,CAAC,CAAC;EAC/B,IAAIiC,eAAe,GAAGH,sBAAsB,CAACqB,SAAS,CAAC;EACvD,IAAI,CAAClB,eAAe,EAAE;IAClBA,eAAe,GAAGH,sBAAsB,CAACqB,SAAS,CAAC,GAAGzC,UAAU,CAAC,aAAa,GAAGyC,SAAS,CAAC;EAC/F;EACAN,IAAI,CAACtB,GAAG,GAAG,UAAU6B,QAAQ,EAAE;IAC3B;IACA;IACA,IAAInK,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAI0J,GAAG,KAAK7B,OAAO,EAAE;MAC5B7H,MAAM,GAAG6H,OAAO;IACpB;IACA,IAAI,CAAC7H,MAAM,EAAE;MACT;IACJ;IACA,MAAMoK,aAAa,GAAGpK,MAAM,CAACgJ,eAAe,CAAC;IAC7C,IAAI,OAAOoB,aAAa,KAAK,UAAU,EAAE;MACrCpK,MAAM,CAACqK,mBAAmB,CAACH,SAAS,EAAEpB,MAAM,CAAC;IACjD;IACA;IACA;IACAmB,eAAe,IAAIA,eAAe,CAAC1G,IAAI,CAACvD,MAAM,EAAE,IAAI,CAAC;IACrDA,MAAM,CAACgJ,eAAe,CAAC,GAAGmB,QAAQ;IAClC,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAChCnK,MAAM,CAACsK,gBAAgB,CAACJ,SAAS,EAAEpB,MAAM,EAAE,KAAK,CAAC;IACrD;EACJ,CAAC;EACD;EACA;EACAc,IAAI,CAACxN,GAAG,GAAG,YAAY;IACnB;IACA;IACA,IAAI4D,MAAM,GAAG,IAAI;IACjB,IAAI,CAACA,MAAM,IAAI0J,GAAG,KAAK7B,OAAO,EAAE;MAC5B7H,MAAM,GAAG6H,OAAO;IACpB;IACA,IAAI,CAAC7H,MAAM,EAAE;MACT,OAAO,IAAI;IACf;IACA,MAAMiJ,QAAQ,GAAGjJ,MAAM,CAACgJ,eAAe,CAAC;IACxC,IAAIC,QAAQ,EAAE;MACV,OAAOA,QAAQ;IACnB,CAAC,MACI,IAAIe,eAAe,EAAE;MACtB;MACA;MACA;MACA;MACA;MACA;MACA,IAAInH,KAAK,GAAGmH,eAAe,CAACzG,IAAI,CAAC,IAAI,CAAC;MACtC,IAAIV,KAAK,EAAE;QACP+G,IAAI,CAACtB,GAAG,CAAC/E,IAAI,CAAC,IAAI,EAAEV,KAAK,CAAC;QAC1B,IAAI,OAAO7C,MAAM,CAAC8H,gBAAgB,CAAC,KAAK,UAAU,EAAE;UAChD9H,MAAM,CAACuK,eAAe,CAACZ,IAAI,CAAC;QAChC;QACA,OAAO9G,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDiD,oBAAoB,CAAC4D,GAAG,EAAEC,IAAI,EAAEC,IAAI,CAAC;EACrCF,GAAG,CAACK,mBAAmB,CAAC,GAAG,IAAI;AACnC;AACA,SAASzE,iBAAiBA,CAACoE,GAAG,EAAEzN,UAAU,EAAEiI,SAAS,EAAE;EACnD,IAAIjI,UAAU,EAAE;IACZ,KAAK,IAAI0D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1D,UAAU,CAAC2D,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC8J,aAAa,CAACC,GAAG,EAAE,IAAI,GAAGzN,UAAU,CAAC0D,CAAC,CAAC,EAAEuE,SAAS,CAAC;IACvD;EACJ,CAAC,MACI;IACD,MAAMsG,YAAY,GAAG,EAAE;IACvB,KAAK,MAAMb,IAAI,IAAID,GAAG,EAAE;MACpB,IAAIC,IAAI,CAAC5C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,EAAE;QAC1ByD,YAAY,CAAC7H,IAAI,CAACgH,IAAI,CAAC;MAC3B;IACJ;IACA,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAAC5K,MAAM,EAAE6K,CAAC,EAAE,EAAE;MAC1ChB,aAAa,CAACC,GAAG,EAAEc,YAAY,CAACC,CAAC,CAAC,EAAEvG,SAAS,CAAC;IAClD;EACJ;AACJ;AACA,MAAMwG,mBAAmB,GAAGjD,UAAU,CAAC,kBAAkB,CAAC;AAC1D;AACA,SAASvB,UAAUA,CAACyE,SAAS,EAAE;EAC3B,MAAMC,aAAa,GAAG/C,OAAO,CAAC8C,SAAS,CAAC;EACxC,IAAI,CAACC,aAAa,EACd;EACJ;EACA/C,OAAO,CAACJ,UAAU,CAACkD,SAAS,CAAC,CAAC,GAAGC,aAAa;EAC9C/C,OAAO,CAAC8C,SAAS,CAAC,GAAG,YAAY;IAC7B,MAAME,CAAC,GAAGrF,aAAa,CAAC1I,SAAS,EAAE6N,SAAS,CAAC;IAC7C,QAAQE,CAAC,CAACjL,MAAM;MACZ,KAAK,CAAC;QACF,IAAI,CAAC8K,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAAC,CAAC;QAC/C;MACJ,KAAK,CAAC;QACF,IAAI,CAACF,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D;MACJ,KAAK,CAAC;QACF,IAAI,CAACH,mBAAmB,CAAC,GAAG,IAAIE,aAAa,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE;MACJ;QACI,MAAM,IAAIhQ,KAAK,CAAC,oBAAoB,CAAC;IAC7C;EACJ,CAAC;EACD;EACAwL,qBAAqB,CAACwB,OAAO,CAAC8C,SAAS,CAAC,EAAEC,aAAa,CAAC;EACxD,MAAME,QAAQ,GAAG,IAAIF,aAAa,CAAC,YAAY,CAAE,CAAC,CAAC;EACnD,IAAIjB,IAAI;EACR,KAAKA,IAAI,IAAImB,QAAQ,EAAE;IACnB;IACA,IAAIH,SAAS,KAAK,gBAAgB,IAAIhB,IAAI,KAAK,cAAc,EACzD;IACJ,CAAC,UAAUA,IAAI,EAAE;MACb,IAAI,OAAOmB,QAAQ,CAACnB,IAAI,CAAC,KAAK,UAAU,EAAE;QACtC9B,OAAO,CAAC8C,SAAS,CAAC,CAACzG,SAAS,CAACyF,IAAI,CAAC,GAAG,YAAY;UAC7C,OAAO,IAAI,CAACe,mBAAmB,CAAC,CAACf,IAAI,CAAC,CAAClH,KAAK,CAAC,IAAI,CAACiI,mBAAmB,CAAC,EAAE5N,SAAS,CAAC;QACtF,CAAC;MACL,CAAC,MACI;QACDgJ,oBAAoB,CAAC+B,OAAO,CAAC8C,SAAS,CAAC,CAACzG,SAAS,EAAEyF,IAAI,EAAE;UACrDrB,GAAG,EAAE,SAAAA,CAAUhN,EAAE,EAAE;YACf,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;cAC1B,IAAI,CAACoP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAGxD,mBAAmB,CAAC7K,EAAE,EAAEqP,SAAS,GAAG,GAAG,GAAGhB,IAAI,CAAC;cACjF;cACA;cACA;cACAtD,qBAAqB,CAAC,IAAI,CAACqE,mBAAmB,CAAC,CAACf,IAAI,CAAC,EAAErO,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,IAAI,CAACoP,mBAAmB,CAAC,CAACf,IAAI,CAAC,GAAGrO,EAAE;YACxC;UACJ,CAAC;UACDc,GAAG,EAAE,SAAAA,CAAA,EAAY;YACb,OAAO,IAAI,CAACsO,mBAAmB,CAAC,CAACf,IAAI,CAAC;UAC1C;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,EAAEA,IAAI,CAAC;EACZ;EACA,KAAKA,IAAI,IAAIiB,aAAa,EAAE;IACxB,IAAIjB,IAAI,KAAK,WAAW,IAAIiB,aAAa,CAACpP,cAAc,CAACmO,IAAI,CAAC,EAAE;MAC5D9B,OAAO,CAAC8C,SAAS,CAAC,CAAChB,IAAI,CAAC,GAAGiB,aAAa,CAACjB,IAAI,CAAC;IAClD;EACJ;AACJ;AACA,SAASpE,WAAWA,CAACvF,MAAM,EAAE9F,IAAI,EAAE6Q,OAAO,EAAE;EACxC,IAAIC,KAAK,GAAGhL,MAAM;EAClB,OAAOgL,KAAK,IAAI,CAACA,KAAK,CAACxP,cAAc,CAACtB,IAAI,CAAC,EAAE;IACzC8Q,KAAK,GAAGrE,oBAAoB,CAACqE,KAAK,CAAC;EACvC;EACA,IAAI,CAACA,KAAK,IAAIhL,MAAM,CAAC9F,IAAI,CAAC,EAAE;IACxB;IACA8Q,KAAK,GAAGhL,MAAM;EAClB;EACA,MAAMiL,YAAY,GAAGxD,UAAU,CAACvN,IAAI,CAAC;EACrC,IAAI6F,QAAQ,GAAG,IAAI;EACnB,IAAIiL,KAAK,KAAK,EAAEjL,QAAQ,GAAGiL,KAAK,CAACC,YAAY,CAAC,CAAC,IAAI,CAACD,KAAK,CAACxP,cAAc,CAACyP,YAAY,CAAC,CAAC,EAAE;IACrFlL,QAAQ,GAAGiL,KAAK,CAACC,YAAY,CAAC,GAAGD,KAAK,CAAC9Q,IAAI,CAAC;IAC5C;IACA;IACA,MAAM0P,IAAI,GAAGoB,KAAK,IAAIjF,8BAA8B,CAACiF,KAAK,EAAE9Q,IAAI,CAAC;IACjE,IAAIgO,kBAAkB,CAAC0B,IAAI,CAAC,EAAE;MAC1B,MAAMsB,aAAa,GAAGH,OAAO,CAAChL,QAAQ,EAAEkL,YAAY,EAAE/Q,IAAI,CAAC;MAC3D8Q,KAAK,CAAC9Q,IAAI,CAAC,GAAG,YAAY;QACtB,OAAOgR,aAAa,CAAC,IAAI,EAAEpO,SAAS,CAAC;MACzC,CAAC;MACDuJ,qBAAqB,CAAC2E,KAAK,CAAC9Q,IAAI,CAAC,EAAE6F,QAAQ,CAAC;IAChD;EACJ;EACA,OAAOA,QAAQ;AACnB;AACA;AACA,SAAS2F,cAAcA,CAACgE,GAAG,EAAEyB,QAAQ,EAAEC,WAAW,EAAE;EAChD,IAAIC,SAAS,GAAG,IAAI;EACpB,SAAS3M,YAAYA,CAACpB,IAAI,EAAE;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;IACtBA,IAAI,CAACqF,IAAI,CAACrF,IAAI,CAACmN,KAAK,CAAC,GAAG,YAAY;MAChChO,IAAI,CAACJ,MAAM,CAACuF,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IACtC,CAAC;IACDuO,SAAS,CAAC5I,KAAK,CAACtE,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAACqF,IAAI,CAAC;IACvC,OAAOlG,IAAI;EACf;EACA+N,SAAS,GAAG9F,WAAW,CAACmE,GAAG,EAAEyB,QAAQ,EAAGpL,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;IACvE,MAAM+H,IAAI,GAAGH,WAAW,CAAC/H,IAAI,EAAEG,IAAI,CAAC;IACpC,IAAI+H,IAAI,CAACD,KAAK,IAAI,CAAC,IAAI,OAAO9H,IAAI,CAAC+H,IAAI,CAACD,KAAK,CAAC,KAAK,UAAU,EAAE;MAC3D,OAAO9D,gCAAgC,CAAC+D,IAAI,CAACrR,IAAI,EAAEsJ,IAAI,CAAC+H,IAAI,CAACD,KAAK,CAAC,EAAEC,IAAI,EAAE7M,YAAY,CAAC;IAC5F,CAAC,MACI;MACD;MACA,OAAOqB,QAAQ,CAAC0C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IACrC;EACJ,CAAC,CAAC;AACN;AACA,SAAS6C,qBAAqBA,CAAC8B,OAAO,EAAEqD,QAAQ,EAAE;EAC9CrD,OAAO,CAACV,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG+D,QAAQ;AACtD;AACA,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,QAAQ,GAAG,KAAK;AACpB,SAASC,IAAIA,CAAA,EAAG;EACZ,IAAI;IACA,MAAMC,EAAE,GAAGhE,cAAc,CAACiE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7D,OAAO,IAAI;IACf;EACJ,CAAC,CACD,OAAO5O,KAAK,EAAE,CAAE;EAChB,OAAO,KAAK;AAChB;AACA,SAASyI,UAAUA,CAAA,EAAG;EAClB,IAAI6F,kBAAkB,EAAE;IACpB,OAAOC,QAAQ;EACnB;EACAD,kBAAkB,GAAG,IAAI;EACzB,IAAI;IACA,MAAMG,EAAE,GAAGhE,cAAc,CAACiE,SAAS,CAACC,SAAS;IAC7C,IAAIF,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAIH,EAAE,CAACG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3FL,QAAQ,GAAG,IAAI;IACnB;EACJ,CAAC,CACD,OAAOvO,KAAK,EAAE,CAAE;EAChB,OAAOuO,QAAQ;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIM,gBAAgB,GAAG,KAAK;AAC5B,IAAI,OAAOrE,MAAM,KAAK,WAAW,EAAE;EAC/B,IAAI;IACA,MAAMxE,OAAO,GAAGc,MAAM,CAACyC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACjDtK,GAAG,EAAE,SAAAA,CAAA,EAAY;QACb4P,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACArE,MAAM,CAAC2C,gBAAgB,CAAC,MAAM,EAAEnH,OAAO,EAAEA,OAAO,CAAC;IACjDwE,MAAM,CAAC0C,mBAAmB,CAAC,MAAM,EAAElH,OAAO,EAAEA,OAAO,CAAC;EACxD,CAAC,CACD,OAAOnE,GAAG,EAAE;IACRgN,gBAAgB,GAAG,KAAK;EAC5B;AACJ;AACA;AACA,MAAMC,8BAA8B,GAAG;EACnC3I,IAAI,EAAE;AACV,CAAC;AACD,MAAM4I,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,sBAAsB,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAG/E,kBAAkB,GAAG,qBAAqB,CAAC;AAC3F,MAAMgF,4BAA4B,GAAG7E,UAAU,CAAC,oBAAoB,CAAC;AACrE,SAAS8E,iBAAiBA,CAACrC,SAAS,EAAEsC,iBAAiB,EAAE;EACrD,MAAMC,cAAc,GAAG,CAACD,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,IAAI7C,SAAS;EACjG,MAAMqF,aAAa,GAAG,CAACF,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,IAAI9C,QAAQ;EAC/F,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAGmF,cAAc;EAClD,MAAME,aAAa,GAAGrF,kBAAkB,GAAGoF,aAAa;EACxDR,oBAAoB,CAAChC,SAAS,CAAC,GAAG,CAAC,CAAC;EACpCgC,oBAAoB,CAAChC,SAAS,CAAC,CAAC7C,SAAS,CAAC,GAAGpC,MAAM;EACnDiH,oBAAoB,CAAChC,SAAS,CAAC,CAAC9C,QAAQ,CAAC,GAAGuF,aAAa;AAC7D;AACA,SAAStH,gBAAgBA,CAACwC,OAAO,EAAE+E,GAAG,EAAEC,IAAI,EAAEC,YAAY,EAAE;EACxD,MAAMC,kBAAkB,GAAID,YAAY,IAAIA,YAAY,CAACE,GAAG,IAAKhG,sBAAsB;EACvF,MAAMiG,qBAAqB,GAAIH,YAAY,IAAIA,YAAY,CAACI,EAAE,IAAKjG,yBAAyB;EAC5F,MAAMkG,wBAAwB,GAAIL,YAAY,IAAIA,YAAY,CAACM,SAAS,IAAK,gBAAgB;EAC7F,MAAMC,mCAAmC,GAAIP,YAAY,IAAIA,YAAY,CAACQ,KAAK,IAAK,oBAAoB;EACxG,MAAMC,0BAA0B,GAAG9F,UAAU,CAACsF,kBAAkB,CAAC;EACjE,MAAMS,yBAAyB,GAAG,GAAG,GAAGT,kBAAkB,GAAG,GAAG;EAChE,MAAMU,sBAAsB,GAAG,iBAAiB;EAChD,MAAMC,6BAA6B,GAAG,GAAG,GAAGD,sBAAsB,GAAG,GAAG;EACxE,MAAMlP,UAAU,GAAG,SAAAA,CAAUjB,IAAI,EAAE0C,MAAM,EAAE+I,KAAK,EAAE;IAC9C;IACA;IACA,IAAIzL,IAAI,CAACqQ,SAAS,EAAE;MAChB;IACJ;IACA,MAAM5N,QAAQ,GAAGzC,IAAI,CAACb,QAAQ;IAC9B,IAAI,OAAOsD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAAC6N,WAAW,EAAE;MACtD;MACAtQ,IAAI,CAACb,QAAQ,GAAIsM,KAAK,IAAKhJ,QAAQ,CAAC6N,WAAW,CAAC7E,KAAK,CAAC;MACtDzL,IAAI,CAACuQ,gBAAgB,GAAG9N,QAAQ;IACpC;IACA;IACA;IACA;IACA;IACA,IAAI5C,KAAK;IACT,IAAI;MACAG,IAAI,CAACJ,MAAM,CAACI,IAAI,EAAE0C,MAAM,EAAE,CAAC+I,KAAK,CAAC,CAAC;IACtC,CAAC,CACD,OAAO/J,GAAG,EAAE;MACR7B,KAAK,GAAG6B,GAAG;IACf;IACA,MAAMmE,OAAO,GAAG7F,IAAI,CAAC6F,OAAO;IAC5B,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC2K,IAAI,EAAE;MACxD;MACA;MACA;MACA,MAAM/N,QAAQ,GAAGzC,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACb,QAAQ;MAC9EuD,MAAM,CAACiN,qBAAqB,CAAC,CAAC1J,IAAI,CAACvD,MAAM,EAAE+I,KAAK,CAACrL,IAAI,EAAEqC,QAAQ,EAAEoD,OAAO,CAAC;IAC7E;IACA,OAAOhG,KAAK;EAChB,CAAC;EACD,SAAS4Q,cAAcA,CAACC,OAAO,EAAEjF,KAAK,EAAEkF,SAAS,EAAE;IAC/C;IACA;IACAlF,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAK;IAC9B,IAAI,CAACA,KAAK,EAAE;MACR;IACJ;IACA;IACA;IACA,MAAM/I,MAAM,GAAGgO,OAAO,IAAIjF,KAAK,CAAC/I,MAAM,IAAI6H,OAAO;IACjD,MAAMqG,KAAK,GAAGlO,MAAM,CAACkM,oBAAoB,CAACnD,KAAK,CAACrL,IAAI,CAAC,CAACuQ,SAAS,GAAG7G,QAAQ,GAAGC,SAAS,CAAC,CAAC;IACxF,IAAI6G,KAAK,EAAE;MACP,MAAMC,MAAM,GAAG,EAAE;MACjB;MACA;MACA,IAAID,KAAK,CAACtO,MAAM,KAAK,CAAC,EAAE;QACpB,MAAMZ,GAAG,GAAGT,UAAU,CAAC2P,KAAK,CAAC,CAAC,CAAC,EAAElO,MAAM,EAAE+I,KAAK,CAAC;QAC/C/J,GAAG,IAAImP,MAAM,CAACxL,IAAI,CAAC3D,GAAG,CAAC;MAC3B,CAAC,MACI;QACD;QACA;QACA;QACA,MAAMoP,SAAS,GAAGF,KAAK,CAACnH,KAAK,CAAC,CAAC;QAC/B,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyO,SAAS,CAACxO,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,IAAIoJ,KAAK,IAAIA,KAAK,CAACuD,4BAA4B,CAAC,KAAK,IAAI,EAAE;YACvD;UACJ;UACA,MAAMtN,GAAG,GAAGT,UAAU,CAAC6P,SAAS,CAACzO,CAAC,CAAC,EAAEK,MAAM,EAAE+I,KAAK,CAAC;UACnD/J,GAAG,IAAImP,MAAM,CAACxL,IAAI,CAAC3D,GAAG,CAAC;QAC3B;MACJ;MACA;MACA;MACA,IAAImP,MAAM,CAACvO,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMuO,MAAM,CAAC,CAAC,CAAC;MACnB,CAAC,MACI;QACD,KAAK,IAAIxO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwO,MAAM,CAACvO,MAAM,EAAED,CAAC,EAAE,EAAE;UACpC,MAAMX,GAAG,GAAGmP,MAAM,CAACxO,CAAC,CAAC;UACrBiN,GAAG,CAAClI,uBAAuB,CAAC,MAAM;YAC9B,MAAM1F,GAAG;UACb,CAAC,CAAC;QACN;MACJ;IACJ;EACJ;EACA;EACA,MAAMqP,uBAAuB,GAAG,SAAAA,CAAUtF,KAAK,EAAE;IAC7C,OAAOgF,cAAc,CAAC,IAAI,EAAEhF,KAAK,EAAE,KAAK,CAAC;EAC7C,CAAC;EACD;EACA,MAAMuF,8BAA8B,GAAG,SAAAA,CAAUvF,KAAK,EAAE;IACpD,OAAOgF,cAAc,CAAC,IAAI,EAAEhF,KAAK,EAAE,IAAI,CAAC;EAC5C,CAAC;EACD,SAASwF,uBAAuBA,CAAC7E,GAAG,EAAEoD,YAAY,EAAE;IAChD,IAAI,CAACpD,GAAG,EAAE;MACN,OAAO,KAAK;IAChB;IACA,IAAI8E,iBAAiB,GAAG,IAAI;IAC5B,IAAI1B,YAAY,IAAIA,YAAY,CAACxJ,IAAI,KAAKhF,SAAS,EAAE;MACjDkQ,iBAAiB,GAAG1B,YAAY,CAACxJ,IAAI;IACzC;IACA,MAAMmL,eAAe,GAAG3B,YAAY,IAAIA,YAAY,CAAC4B,EAAE;IACvD,IAAIjT,cAAc,GAAG,IAAI;IACzB,IAAIqR,YAAY,IAAIA,YAAY,CAAC6B,MAAM,KAAKrQ,SAAS,EAAE;MACnD7C,cAAc,GAAGqR,YAAY,CAAC6B,MAAM;IACxC;IACA,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAI9B,YAAY,IAAIA,YAAY,CAAC+B,EAAE,KAAKvQ,SAAS,EAAE;MAC/CsQ,YAAY,GAAG9B,YAAY,CAAC+B,EAAE;IAClC;IACA,IAAI7D,KAAK,GAAGtB,GAAG;IACf,OAAOsB,KAAK,IAAI,CAACA,KAAK,CAACxP,cAAc,CAACuR,kBAAkB,CAAC,EAAE;MACvD/B,KAAK,GAAGrE,oBAAoB,CAACqE,KAAK,CAAC;IACvC;IACA,IAAI,CAACA,KAAK,IAAItB,GAAG,CAACqD,kBAAkB,CAAC,EAAE;MACnC;MACA/B,KAAK,GAAGtB,GAAG;IACf;IACA,IAAI,CAACsB,KAAK,EAAE;MACR,OAAO,KAAK;IAChB;IACA,IAAIA,KAAK,CAACuC,0BAA0B,CAAC,EAAE;MACnC,OAAO,KAAK;IAChB;IACA,MAAMf,iBAAiB,GAAGM,YAAY,IAAIA,YAAY,CAACN,iBAAiB;IACxE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMsC,QAAQ,GAAG,CAAC,CAAC;IACnB,MAAMC,sBAAsB,GAAI/D,KAAK,CAACuC,0BAA0B,CAAC,GAAGvC,KAAK,CAAC+B,kBAAkB,CAAE;IAC9F,MAAMiC,yBAAyB,GAAIhE,KAAK,CAACvD,UAAU,CAACwF,qBAAqB,CAAC,CAAC,GACvEjC,KAAK,CAACiC,qBAAqB,CAAE;IACjC,MAAMgC,eAAe,GAAIjE,KAAK,CAACvD,UAAU,CAAC0F,wBAAwB,CAAC,CAAC,GAChEnC,KAAK,CAACmC,wBAAwB,CAAE;IACpC,MAAM+B,wBAAwB,GAAIlE,KAAK,CAACvD,UAAU,CAAC4F,mCAAmC,CAAC,CAAC,GACpFrC,KAAK,CAACqC,mCAAmC,CAAE;IAC/C,IAAI8B,0BAA0B;IAC9B,IAAIrC,YAAY,IAAIA,YAAY,CAACsC,OAAO,EAAE;MACtCD,0BAA0B,GAAGnE,KAAK,CAACvD,UAAU,CAACqF,YAAY,CAACsC,OAAO,CAAC,CAAC,GAChEpE,KAAK,CAAC8B,YAAY,CAACsC,OAAO,CAAC;IACnC;IACA;AACR;AACA;AACA;IACQ,SAASC,yBAAyBA,CAAClM,OAAO,EAAEmM,OAAO,EAAE;MACjD,IAAI,CAACtD,gBAAgB,IAAI,OAAO7I,OAAO,KAAK,QAAQ,IAAIA,OAAO,EAAE;QAC7D;QACA;QACA;QACA,OAAO,CAAC,CAACA,OAAO,CAACoM,OAAO;MAC5B;MACA,IAAI,CAACvD,gBAAgB,IAAI,CAACsD,OAAO,EAAE;QAC/B,OAAOnM,OAAO;MAClB;MACA,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAC9B,OAAO;UAAEoM,OAAO,EAAEpM,OAAO;UAAEmM,OAAO,EAAE;QAAK,CAAC;MAC9C;MACA,IAAI,CAACnM,OAAO,EAAE;QACV,OAAO;UAAEmM,OAAO,EAAE;QAAK,CAAC;MAC5B;MACA,IAAI,OAAOnM,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACmM,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO;UAAE,GAAGnM,OAAO;UAAEmM,OAAO,EAAE;QAAK,CAAC;MACxC;MACA,OAAOnM,OAAO;IAClB;IACA,MAAMqM,oBAAoB,GAAG,SAAAA,CAAUlS,IAAI,EAAE;MACzC;MACA;MACA,IAAIwR,QAAQ,CAACW,UAAU,EAAE;QACrB;MACJ;MACA,OAAOV,sBAAsB,CAACxL,IAAI,CAACuL,QAAQ,CAAC9O,MAAM,EAAE8O,QAAQ,CAAC5E,SAAS,EAAE4E,QAAQ,CAACS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAES,QAAQ,CAAC3L,OAAO,CAAC;IAC1K,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMuM,kBAAkB,GAAG,SAAAA,CAAUpS,IAAI,EAAE;MACvC;MACA;MACA;MACA,IAAI,CAACA,IAAI,CAACqQ,SAAS,EAAE;QACjB,MAAMgC,gBAAgB,GAAGzD,oBAAoB,CAAC5O,IAAI,CAAC4M,SAAS,CAAC;QAC7D,IAAI0F,eAAe;QACnB,IAAID,gBAAgB,EAAE;UAClBC,eAAe,GAAGD,gBAAgB,CAACrS,IAAI,CAACiS,OAAO,GAAGnI,QAAQ,GAAGC,SAAS,CAAC;QAC3E;QACA,MAAMwI,aAAa,GAAGD,eAAe,IAAItS,IAAI,CAAC0C,MAAM,CAAC4P,eAAe,CAAC;QACrE,IAAIC,aAAa,EAAE;UACf,KAAK,IAAIlQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkQ,aAAa,CAACjQ,MAAM,EAAED,CAAC,EAAE,EAAE;YAC3C,MAAMmQ,YAAY,GAAGD,aAAa,CAAClQ,CAAC,CAAC;YACrC,IAAImQ,YAAY,KAAKxS,IAAI,EAAE;cACvBuS,aAAa,CAACE,MAAM,CAACpQ,CAAC,EAAE,CAAC,CAAC;cAC1B;cACArC,IAAI,CAACqQ,SAAS,GAAG,IAAI;cACrB,IAAIrQ,IAAI,CAAC0S,mBAAmB,EAAE;gBAC1B1S,IAAI,CAAC0S,mBAAmB,CAAC,CAAC;gBAC1B1S,IAAI,CAAC0S,mBAAmB,GAAG,IAAI;cACnC;cACA,IAAIH,aAAa,CAACjQ,MAAM,KAAK,CAAC,EAAE;gBAC5B;gBACA;gBACAtC,IAAI,CAAC2S,UAAU,GAAG,IAAI;gBACtB3S,IAAI,CAAC0C,MAAM,CAAC4P,eAAe,CAAC,GAAG,IAAI;cACvC;cACA;YACJ;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACtS,IAAI,CAAC2S,UAAU,EAAE;QAClB;MACJ;MACA,OAAOjB,yBAAyB,CAACzL,IAAI,CAACjG,IAAI,CAAC0C,MAAM,EAAE1C,IAAI,CAAC4M,SAAS,EAAE5M,IAAI,CAACiS,OAAO,GAAGjB,8BAA8B,GAAGD,uBAAuB,EAAE/Q,IAAI,CAAC6F,OAAO,CAAC;IAC7J,CAAC;IACD,MAAM+M,uBAAuB,GAAG,SAAAA,CAAU5S,IAAI,EAAE;MAC5C,OAAOyR,sBAAsB,CAACxL,IAAI,CAACuL,QAAQ,CAAC9O,MAAM,EAAE8O,QAAQ,CAAC5E,SAAS,EAAE5M,IAAI,CAACJ,MAAM,EAAE4R,QAAQ,CAAC3L,OAAO,CAAC;IAC1G,CAAC;IACD,MAAMgN,qBAAqB,GAAG,SAAAA,CAAU7S,IAAI,EAAE;MAC1C,OAAO6R,0BAA0B,CAAC5L,IAAI,CAACuL,QAAQ,CAAC9O,MAAM,EAAE8O,QAAQ,CAAC5E,SAAS,EAAE5M,IAAI,CAACJ,MAAM,EAAE4R,QAAQ,CAAC3L,OAAO,CAAC;IAC9G,CAAC;IACD,MAAMiN,qBAAqB,GAAG,SAAAA,CAAU9S,IAAI,EAAE;MAC1C,OAAO0R,yBAAyB,CAACzL,IAAI,CAACjG,IAAI,CAAC0C,MAAM,EAAE1C,IAAI,CAAC4M,SAAS,EAAE5M,IAAI,CAACJ,MAAM,EAAEI,IAAI,CAAC6F,OAAO,CAAC;IACjG,CAAC;IACD,MAAMjE,cAAc,GAAGsP,iBAAiB,GAAGgB,oBAAoB,GAAGU,uBAAuB;IACzF,MAAM5Q,YAAY,GAAGkP,iBAAiB,GAAGkB,kBAAkB,GAAGU,qBAAqB;IACnF,MAAMC,6BAA6B,GAAG,SAAAA,CAAU/S,IAAI,EAAEyC,QAAQ,EAAE;MAC5D,MAAMuQ,cAAc,GAAG,OAAOvQ,QAAQ;MACtC,OAASuQ,cAAc,KAAK,UAAU,IAAIhT,IAAI,CAACb,QAAQ,KAAKsD,QAAQ,IAC/DuQ,cAAc,KAAK,QAAQ,IAAIhT,IAAI,CAACuQ,gBAAgB,KAAK9N,QAAS;IAC3E,CAAC;IACD,MAAMwQ,OAAO,GAAGzD,YAAY,IAAIA,YAAY,CAAC0D,IAAI,GAAG1D,YAAY,CAAC0D,IAAI,GAAGH,6BAA6B;IACrG,MAAMI,eAAe,GAAGlJ,IAAI,CAACE,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAC5D,MAAMiJ,aAAa,GAAG7I,OAAO,CAACJ,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC3D,SAASkJ,wBAAwBA,CAACxN,OAAO,EAAE;MACvC,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;QACjD;QACA;QACA;QACA,MAAMyN,UAAU,GAAG;UAAE,GAAGzN;QAAQ,CAAC;QACjC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIA,OAAO,CAAC0N,MAAM,EAAE;UAChBD,UAAU,CAACC,MAAM,GAAG1N,OAAO,CAAC0N,MAAM;QACtC;QACA,OAAOD,UAAU;MACrB;MACA,OAAOzN,OAAO;IAClB;IACA,MAAM2N,eAAe,GAAG,SAAAA,CAAUC,cAAc,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAEtC,YAAY,GAAG,KAAK,EAAEQ,OAAO,GAAG,KAAK,EAAE;MAClI,OAAO,YAAY;QACf,MAAMpP,MAAM,GAAG,IAAI,IAAI6H,OAAO;QAC9B,IAAIqC,SAAS,GAAGpN,SAAS,CAAC,CAAC,CAAC;QAC5B,IAAIgQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;UAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;QACzD;QACA,IAAInK,QAAQ,GAAGjD,SAAS,CAAC,CAAC,CAAC;QAC3B,IAAI,CAACiD,QAAQ,EAAE;UACX,OAAOgR,cAAc,CAACtO,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;QAChD;QACA,IAAI2L,MAAM,IAAIyB,SAAS,KAAK,mBAAmB,EAAE;UAC7C;UACA,OAAO6G,cAAc,CAACtO,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;QAChD;QACA;QACA;QACA;QACA,IAAIsU,aAAa,GAAG,KAAK;QACzB,IAAI,OAAOrR,QAAQ,KAAK,UAAU,EAAE;UAChC,IAAI,CAACA,QAAQ,CAAC6N,WAAW,EAAE;YACvB,OAAOmD,cAAc,CAACtO,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;UAChD;UACAsU,aAAa,GAAG,IAAI;QACxB;QACA,IAAI3C,eAAe,IAAI,CAACA,eAAe,CAACsC,cAAc,EAAEhR,QAAQ,EAAEC,MAAM,EAAElD,SAAS,CAAC,EAAE;UAClF;QACJ;QACA,MAAMwS,OAAO,GAAGtD,gBAAgB,IAAI,CAAC,CAAC0E,aAAa,IAAIA,aAAa,CAAC3E,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC;QAC9F,MAAM/G,OAAO,GAAGwN,wBAAwB,CAACtB,yBAAyB,CAACvS,SAAS,CAAC,CAAC,CAAC,EAAEwS,OAAO,CAAC,CAAC;QAC1F,MAAMuB,MAAM,GAAG1N,OAAO,EAAE0N,MAAM;QAC9B,IAAIA,MAAM,EAAEQ,OAAO,EAAE;UACjB;UACA;QACJ;QACA,IAAIZ,eAAe,EAAE;UACjB;UACA,KAAK,IAAI9Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Q,eAAe,CAAC7Q,MAAM,EAAED,CAAC,EAAE,EAAE;YAC7C,IAAIuK,SAAS,KAAKuG,eAAe,CAAC9Q,CAAC,CAAC,EAAE;cAClC,IAAI2P,OAAO,EAAE;gBACT,OAAOyB,cAAc,CAACxN,IAAI,CAACvD,MAAM,EAAEkK,SAAS,EAAEnK,QAAQ,EAAEoD,OAAO,CAAC;cACpE,CAAC,MACI;gBACD,OAAO4N,cAAc,CAACtO,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;cAChD;YACJ;UACJ;QACJ;QACA,MAAMyS,OAAO,GAAG,CAACpM,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACoM,OAAO;QACxF,MAAMzB,IAAI,GAAG3K,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAAC2K,IAAI,GAAG,KAAK;QAC1E,MAAM/S,IAAI,GAAGwM,IAAI,CAACvM,OAAO;QACzB,IAAI2U,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;QACtD,IAAI,CAACyF,gBAAgB,EAAE;UACnBpD,iBAAiB,CAACrC,SAAS,EAAEsC,iBAAiB,CAAC;UAC/CmD,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;QACtD;QACA,MAAM0F,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGnI,QAAQ,GAAGC,SAAS,CAAC;QACxE,IAAIwI,aAAa,GAAG7P,MAAM,CAAC4P,eAAe,CAAC;QAC3C,IAAIH,UAAU,GAAG,KAAK;QACtB,IAAII,aAAa,EAAE;UACf;UACAJ,UAAU,GAAG,IAAI;UACjB,IAAIhU,cAAc,EAAE;YAChB,KAAK,IAAIkE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkQ,aAAa,CAACjQ,MAAM,EAAED,CAAC,EAAE,EAAE;cAC3C,IAAI4Q,OAAO,CAACV,aAAa,CAAClQ,CAAC,CAAC,EAAEI,QAAQ,CAAC,EAAE;gBACrC;gBACA;cACJ;YACJ;UACJ;QACJ,CAAC,MACI;UACD8P,aAAa,GAAG7P,MAAM,CAAC4P,eAAe,CAAC,GAAG,EAAE;QAChD;QACA,IAAIlT,MAAM;QACV,MAAM4U,eAAe,GAAGtR,MAAM,CAAClE,WAAW,CAAC,MAAM,CAAC;QAClD,MAAMyV,YAAY,GAAGpF,aAAa,CAACmF,eAAe,CAAC;QACnD,IAAIC,YAAY,EAAE;UACd7U,MAAM,GAAG6U,YAAY,CAACrH,SAAS,CAAC;QACpC;QACA,IAAI,CAACxN,MAAM,EAAE;UACTA,MAAM,GACF4U,eAAe,GACXN,SAAS,IACRxE,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,CAAC;QAC1E;QACA;QACA;QACA;QACA;QACA;QACA4E,QAAQ,CAAC3L,OAAO,GAAGA,OAAO;QAC1B,IAAI2K,IAAI,EAAE;UACN;UACA;UACA;UACAgB,QAAQ,CAAC3L,OAAO,CAAC2K,IAAI,GAAG,KAAK;QACjC;QACAgB,QAAQ,CAAC9O,MAAM,GAAGA,MAAM;QACxB8O,QAAQ,CAACS,OAAO,GAAGA,OAAO;QAC1BT,QAAQ,CAAC5E,SAAS,GAAGA,SAAS;QAC9B4E,QAAQ,CAACW,UAAU,GAAGA,UAAU;QAChC,MAAMtR,IAAI,GAAGqQ,iBAAiB,GAAGvC,8BAA8B,GAAG3N,SAAS;QAC3E;QACA,IAAIH,IAAI,EAAE;UACNA,IAAI,CAAC2Q,QAAQ,GAAGA,QAAQ;QAC5B;QACA,IAAI+B,MAAM,EAAE;UACR;UACA;UACA;UACA/B,QAAQ,CAAC3L,OAAO,CAAC0N,MAAM,GAAGvS,SAAS;QACvC;QACA;QACA;QACA;QACA;QACA,MAAMhB,IAAI,GAAGvC,IAAI,CAACwE,iBAAiB,CAAC7C,MAAM,EAAEqD,QAAQ,EAAE5B,IAAI,EAAE8S,gBAAgB,EAAEC,cAAc,CAAC;QAC7F,IAAIL,MAAM,EAAE;UACR;UACA/B,QAAQ,CAAC3L,OAAO,CAAC0N,MAAM,GAAGA,MAAM;UAChC;UACA;UACA;UACA,MAAMW,OAAO,GAAGA,CAAA,KAAMlU,IAAI,CAACvC,IAAI,CAACyE,UAAU,CAAClC,IAAI,CAAC;UAChDyT,cAAc,CAACxN,IAAI,CAACsN,MAAM,EAAE,OAAO,EAAEW,OAAO,EAAE;YAAE1D,IAAI,EAAE;UAAK,CAAC,CAAC;UAC7D;UACA;UACA;UACA;UACAxQ,IAAI,CAAC0S,mBAAmB,GAAG,MAAMa,MAAM,CAACxG,mBAAmB,CAAC,OAAO,EAAEmH,OAAO,CAAC;QACjF;QACA;QACA;QACA1C,QAAQ,CAAC9O,MAAM,GAAG,IAAI;QACtB;QACA,IAAI7B,IAAI,EAAE;UACNA,IAAI,CAAC2Q,QAAQ,GAAG,IAAI;QACxB;QACA;QACA;QACA,IAAIhB,IAAI,EAAE;UACNgB,QAAQ,CAAC3L,OAAO,CAAC2K,IAAI,GAAG,IAAI;QAChC;QACA,IAAI,EAAE,CAAC9B,gBAAgB,IAAI,OAAO1O,IAAI,CAAC6F,OAAO,KAAK,SAAS,CAAC,EAAE;UAC3D;UACA;UACA7F,IAAI,CAAC6F,OAAO,GAAGA,OAAO;QAC1B;QACA7F,IAAI,CAAC0C,MAAM,GAAGA,MAAM;QACpB1C,IAAI,CAACiS,OAAO,GAAGA,OAAO;QACtBjS,IAAI,CAAC4M,SAAS,GAAGA,SAAS;QAC1B,IAAIkH,aAAa,EAAE;UACf;UACA9T,IAAI,CAACuQ,gBAAgB,GAAG9N,QAAQ;QACpC;QACA,IAAI,CAACqP,OAAO,EAAE;UACVS,aAAa,CAAClN,IAAI,CAACrF,IAAI,CAAC;QAC5B,CAAC,MACI;UACDuS,aAAa,CAAC4B,OAAO,CAACnU,IAAI,CAAC;QAC/B;QACA,IAAIsR,YAAY,EAAE;UACd,OAAO5O,MAAM;QACjB;MACJ,CAAC;IACL,CAAC;IACDgL,KAAK,CAAC+B,kBAAkB,CAAC,GAAG+D,eAAe,CAAC/B,sBAAsB,EAAEvB,yBAAyB,EAAEtO,cAAc,EAAEI,YAAY,EAAEsP,YAAY,CAAC;IAC1I,IAAIO,0BAA0B,EAAE;MAC5BnE,KAAK,CAACyC,sBAAsB,CAAC,GAAGqD,eAAe,CAAC3B,0BAA0B,EAAEzB,6BAA6B,EAAEyC,qBAAqB,EAAE7Q,YAAY,EAAEsP,YAAY,EAAE,IAAI,CAAC;IACvK;IACA5D,KAAK,CAACiC,qBAAqB,CAAC,GAAG,YAAY;MACvC,MAAMjN,MAAM,GAAG,IAAI,IAAI6H,OAAO;MAC9B,IAAIqC,SAAS,GAAGpN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAIgQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;QAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;MACzD;MACA,MAAM/G,OAAO,GAAGrG,SAAS,CAAC,CAAC,CAAC;MAC5B,MAAMyS,OAAO,GAAG,CAACpM,OAAO,GAAG,KAAK,GAAG,OAAOA,OAAO,KAAK,SAAS,GAAG,IAAI,GAAGA,OAAO,CAACoM,OAAO;MACxF,MAAMxP,QAAQ,GAAGjD,SAAS,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACiD,QAAQ,EAAE;QACX,OAAOiP,yBAAyB,CAACvM,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;MAC3D;MACA,IAAI2R,eAAe,IACf,CAACA,eAAe,CAACO,yBAAyB,EAAEjP,QAAQ,EAAEC,MAAM,EAAElD,SAAS,CAAC,EAAE;QAC1E;MACJ;MACA,MAAM6S,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;MACxD,IAAI0F,eAAe;MACnB,IAAID,gBAAgB,EAAE;QAClBC,eAAe,GAAGD,gBAAgB,CAACJ,OAAO,GAAGnI,QAAQ,GAAGC,SAAS,CAAC;MACtE;MACA,MAAMwI,aAAa,GAAGD,eAAe,IAAI5P,MAAM,CAAC4P,eAAe,CAAC;MAChE;MACA;MACA;MACA;MACA,IAAIC,aAAa,EAAE;QACf,KAAK,IAAIlQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkQ,aAAa,CAACjQ,MAAM,EAAED,CAAC,EAAE,EAAE;UAC3C,MAAMmQ,YAAY,GAAGD,aAAa,CAAClQ,CAAC,CAAC;UACrC,IAAI4Q,OAAO,CAACT,YAAY,EAAE/P,QAAQ,CAAC,EAAE;YACjC8P,aAAa,CAACE,MAAM,CAACpQ,CAAC,EAAE,CAAC,CAAC;YAC1B;YACAmQ,YAAY,CAACnC,SAAS,GAAG,IAAI;YAC7B,IAAIkC,aAAa,CAACjQ,MAAM,KAAK,CAAC,EAAE;cAC5B;cACA;cACAkQ,YAAY,CAACG,UAAU,GAAG,IAAI;cAC9BjQ,MAAM,CAAC4P,eAAe,CAAC,GAAG,IAAI;cAC9B;cACA;cACA;cACA;cACA;cACA,IAAI,CAACL,OAAO,IAAI,OAAOrF,SAAS,KAAK,QAAQ,EAAE;gBAC3C,MAAMwH,gBAAgB,GAAGpK,kBAAkB,GAAG,aAAa,GAAG4C,SAAS;gBACvElK,MAAM,CAAC0R,gBAAgB,CAAC,GAAG,IAAI;cACnC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA5B,YAAY,CAAC/U,IAAI,CAACyE,UAAU,CAACsQ,YAAY,CAAC;YAC1C,IAAIlB,YAAY,EAAE;cACd,OAAO5O,MAAM;YACjB;YACA;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAOgP,yBAAyB,CAACvM,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IAC3D,CAAC;IACDkO,KAAK,CAACmC,wBAAwB,CAAC,GAAG,YAAY;MAC1C,MAAMnN,MAAM,GAAG,IAAI,IAAI6H,OAAO;MAC9B,IAAIqC,SAAS,GAAGpN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAIgQ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;QAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;MACzD;MACA,MAAMkD,SAAS,GAAG,EAAE;MACpB,MAAMc,KAAK,GAAGyD,cAAc,CAAC3R,MAAM,EAAEwM,iBAAiB,GAAGA,iBAAiB,CAACtC,SAAS,CAAC,GAAGA,SAAS,CAAC;MAClG,KAAK,IAAIvK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuO,KAAK,CAACtO,MAAM,EAAED,CAAC,EAAE,EAAE;QACnC,MAAMrC,IAAI,GAAG4Q,KAAK,CAACvO,CAAC,CAAC;QACrB,IAAII,QAAQ,GAAGzC,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACb,QAAQ;QAC5E2Q,SAAS,CAACzK,IAAI,CAAC5C,QAAQ,CAAC;MAC5B;MACA,OAAOqN,SAAS;IACpB,CAAC;IACDpC,KAAK,CAACqC,mCAAmC,CAAC,GAAG,YAAY;MACrD,MAAMrN,MAAM,GAAG,IAAI,IAAI6H,OAAO;MAC9B,IAAIqC,SAAS,GAAGpN,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAI,CAACoN,SAAS,EAAE;QACZ,MAAM0H,IAAI,GAAG3N,MAAM,CAAC2N,IAAI,CAAC5R,MAAM,CAAC;QAChC,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiS,IAAI,CAAChS,MAAM,EAAED,CAAC,EAAE,EAAE;UAClC,MAAMgK,IAAI,GAAGiI,IAAI,CAACjS,CAAC,CAAC;UACpB,MAAMkS,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAI,CAACnI,IAAI,CAAC;UAC/C,IAAIoI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;UAC/B;UACA;UACA;UACA;UACA,IAAIE,OAAO,IAAIA,OAAO,KAAK,gBAAgB,EAAE;YACzC,IAAI,CAAC1E,mCAAmC,CAAC,CAAC9J,IAAI,CAAC,IAAI,EAAEwO,OAAO,CAAC;UACjE;QACJ;QACA;QACA,IAAI,CAAC1E,mCAAmC,CAAC,CAAC9J,IAAI,CAAC,IAAI,EAAE,gBAAgB,CAAC;MAC1E,CAAC,MACI;QACD,IAAIuJ,YAAY,IAAIA,YAAY,CAACqE,iBAAiB,EAAE;UAChDjH,SAAS,GAAG4C,YAAY,CAACqE,iBAAiB,CAACjH,SAAS,CAAC;QACzD;QACA,MAAMyF,gBAAgB,GAAGzD,oBAAoB,CAAChC,SAAS,CAAC;QACxD,IAAIyF,gBAAgB,EAAE;UAClB,MAAMC,eAAe,GAAGD,gBAAgB,CAACtI,SAAS,CAAC;UACnD,MAAM2K,sBAAsB,GAAGrC,gBAAgB,CAACvI,QAAQ,CAAC;UACzD,MAAM8G,KAAK,GAAGlO,MAAM,CAAC4P,eAAe,CAAC;UACrC,MAAMqC,YAAY,GAAGjS,MAAM,CAACgS,sBAAsB,CAAC;UACnD,IAAI9D,KAAK,EAAE;YACP,MAAMgE,WAAW,GAAGhE,KAAK,CAACnH,KAAK,CAAC,CAAC;YACjC,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuS,WAAW,CAACtS,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMrC,IAAI,GAAG4U,WAAW,CAACvS,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAGzC,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAACwQ,qBAAqB,CAAC,CAAC1J,IAAI,CAAC,IAAI,EAAE2G,SAAS,EAAEnK,QAAQ,EAAEzC,IAAI,CAAC6F,OAAO,CAAC;YAC7E;UACJ;UACA,IAAI8O,YAAY,EAAE;YACd,MAAMC,WAAW,GAAGD,YAAY,CAAClL,KAAK,CAAC,CAAC;YACxC,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuS,WAAW,CAACtS,MAAM,EAAED,CAAC,EAAE,EAAE;cACzC,MAAMrC,IAAI,GAAG4U,WAAW,CAACvS,CAAC,CAAC;cAC3B,IAAII,QAAQ,GAAGzC,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACuQ,gBAAgB,GAAGvQ,IAAI,CAACb,QAAQ;cAC5E,IAAI,CAACwQ,qBAAqB,CAAC,CAAC1J,IAAI,CAAC,IAAI,EAAE2G,SAAS,EAAEnK,QAAQ,EAAEzC,IAAI,CAAC6F,OAAO,CAAC;YAC7E;UACJ;QACJ;MACJ;MACA,IAAIyL,YAAY,EAAE;QACd,OAAO,IAAI;MACf;IACJ,CAAC;IACD;IACAvI,qBAAqB,CAAC2E,KAAK,CAAC+B,kBAAkB,CAAC,EAAEgC,sBAAsB,CAAC;IACxE1I,qBAAqB,CAAC2E,KAAK,CAACiC,qBAAqB,CAAC,EAAE+B,yBAAyB,CAAC;IAC9E,IAAIE,wBAAwB,EAAE;MAC1B7I,qBAAqB,CAAC2E,KAAK,CAACqC,mCAAmC,CAAC,EAAE6B,wBAAwB,CAAC;IAC/F;IACA,IAAID,eAAe,EAAE;MACjB5I,qBAAqB,CAAC2E,KAAK,CAACmC,wBAAwB,CAAC,EAAE8B,eAAe,CAAC;IAC3E;IACA,OAAO,IAAI;EACf;EACA,IAAIkD,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIxS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkN,IAAI,CAACjN,MAAM,EAAED,CAAC,EAAE,EAAE;IAClCwS,OAAO,CAACxS,CAAC,CAAC,GAAG4O,uBAAuB,CAAC1B,IAAI,CAAClN,CAAC,CAAC,EAAEmN,YAAY,CAAC;EAC/D;EACA,OAAOqF,OAAO;AAClB;AACA,SAASR,cAAcA,CAAC3R,MAAM,EAAEkK,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,EAAE;IACZ,MAAMkI,UAAU,GAAG,EAAE;IACrB,KAAK,IAAIzI,IAAI,IAAI3J,MAAM,EAAE;MACrB,MAAM6R,KAAK,GAAGzF,sBAAsB,CAAC0F,IAAI,CAACnI,IAAI,CAAC;MAC/C,IAAIoI,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAIE,OAAO,KAAK,CAAC7H,SAAS,IAAI6H,OAAO,KAAK7H,SAAS,CAAC,EAAE;QAClD,MAAMgE,KAAK,GAAGlO,MAAM,CAAC2J,IAAI,CAAC;QAC1B,IAAIuE,KAAK,EAAE;UACP,KAAK,IAAIvO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuO,KAAK,CAACtO,MAAM,EAAED,CAAC,EAAE,EAAE;YACnCyS,UAAU,CAACzP,IAAI,CAACuL,KAAK,CAACvO,CAAC,CAAC,CAAC;UAC7B;QACJ;MACJ;IACJ;IACA,OAAOyS,UAAU;EACrB;EACA,IAAIxC,eAAe,GAAG1D,oBAAoB,CAAChC,SAAS,CAAC;EACrD,IAAI,CAAC0F,eAAe,EAAE;IAClBrD,iBAAiB,CAACrC,SAAS,CAAC;IAC5B0F,eAAe,GAAG1D,oBAAoB,CAAChC,SAAS,CAAC;EACrD;EACA,MAAMmI,iBAAiB,GAAGrS,MAAM,CAAC4P,eAAe,CAACvI,SAAS,CAAC,CAAC;EAC5D,MAAMiL,gBAAgB,GAAGtS,MAAM,CAAC4P,eAAe,CAACxI,QAAQ,CAAC,CAAC;EAC1D,IAAI,CAACiL,iBAAiB,EAAE;IACpB,OAAOC,gBAAgB,GAAGA,gBAAgB,CAACvL,KAAK,CAAC,CAAC,GAAG,EAAE;EAC3D,CAAC,MACI;IACD,OAAOuL,gBAAgB,GACjBD,iBAAiB,CAACE,MAAM,CAACD,gBAAgB,CAAC,GAC1CD,iBAAiB,CAACtL,KAAK,CAAC,CAAC;EACnC;AACJ;AACA,SAASpB,mBAAmBA,CAAC5L,MAAM,EAAE6S,GAAG,EAAE;EACtC,MAAM4F,KAAK,GAAGzY,MAAM,CAAC,OAAO,CAAC;EAC7B,IAAIyY,KAAK,IAAIA,KAAK,CAACtO,SAAS,EAAE;IAC1B0I,GAAG,CAACrH,WAAW,CAACiN,KAAK,CAACtO,SAAS,EAAE,0BAA0B,EAAGnE,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;MAC7FH,IAAI,CAACiJ,4BAA4B,CAAC,GAAG,IAAI;MACzC;MACA;MACA;MACAvM,QAAQ,IAAIA,QAAQ,CAAC0C,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;IAC1C,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASiP,mBAAmBA,CAAC1Y,MAAM,EAAE6S,GAAG,EAAE;EACtCA,GAAG,CAACrH,WAAW,CAACxL,MAAM,EAAE,gBAAgB,EAAGgG,QAAQ,IAAK;IACpD,OAAO,UAAUsD,IAAI,EAAEG,IAAI,EAAE;MACzB+D,IAAI,CAACvM,OAAO,CAACiE,iBAAiB,CAAC,gBAAgB,EAAEuE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;EACL,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA,MAAMkP,UAAU,GAAGjL,UAAU,CAAC,UAAU,CAAC;AACzC,SAASkL,UAAUA,CAAChL,MAAM,EAAEiL,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACzD,IAAIzH,SAAS,GAAG,IAAI;EACpB,IAAI0H,WAAW,GAAG,IAAI;EACtBH,OAAO,IAAIE,UAAU;EACrBD,UAAU,IAAIC,UAAU;EACxB,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,SAAStU,YAAYA,CAACpB,IAAI,EAAE;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;IACtBA,IAAI,CAACqF,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY;MACvB,OAAOlG,IAAI,CAACJ,MAAM,CAACuF,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;IAC7C,CAAC;IACDqB,IAAI,CAAC6F,QAAQ,GAAGqH,SAAS,CAAC5I,KAAK,CAACkF,MAAM,EAAExJ,IAAI,CAACqF,IAAI,CAAC;IAClD,OAAOlG,IAAI;EACf;EACA,SAAS2V,SAASA,CAAC3V,IAAI,EAAE;IACrB,OAAOyV,WAAW,CAACxP,IAAI,CAACoE,MAAM,EAAErK,IAAI,CAACa,IAAI,CAAC6F,QAAQ,CAAC;EACvD;EACAqH,SAAS,GAAG9F,WAAW,CAACoC,MAAM,EAAEiL,OAAO,EAAG7S,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;IACzE,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/B,MAAML,OAAO,GAAG;QACZ/E,UAAU,EAAE0U,UAAU,KAAK,UAAU;QACrCI,KAAK,EAAEJ,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,UAAU,GAAGtP,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGlF,SAAS;QACvFkF,IAAI,EAAEA;MACV,CAAC;MACD,MAAM/G,QAAQ,GAAG+G,IAAI,CAAC,CAAC,CAAC;MACxBA,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS2P,KAAKA,CAAA,EAAG;QACvB,IAAI;UACA,OAAO1W,QAAQ,CAACgG,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;QAC1C,CAAC,SACO;UACJ;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACqG,OAAO,CAAC/E,UAAU,EAAE;YACrB,IAAI,OAAO+E,OAAO,CAACa,QAAQ,KAAK,QAAQ,EAAE;cACtC;cACA;cACA,OAAOgP,eAAe,CAAC7P,OAAO,CAACa,QAAQ,CAAC;YAC5C,CAAC,MACI,IAAIb,OAAO,CAACa,QAAQ,EAAE;cACvB;cACA;cACAb,OAAO,CAACa,QAAQ,CAAC0O,UAAU,CAAC,GAAG,IAAI;YACvC;UACJ;QACJ;MACJ,CAAC;MACD,MAAMpV,IAAI,GAAGkK,gCAAgC,CAACoL,OAAO,EAAEpP,IAAI,CAAC,CAAC,CAAC,EAAEL,OAAO,EAAEzE,YAAY,EAAEuU,SAAS,CAAC;MACjG,IAAI,CAAC3V,IAAI,EAAE;QACP,OAAOA,IAAI;MACf;MACA;MACA,MAAM8V,MAAM,GAAG9V,IAAI,CAACa,IAAI,CAAC6F,QAAQ;MACjC,IAAI,OAAOoP,MAAM,KAAK,QAAQ,EAAE;QAC5B;QACA;QACAJ,eAAe,CAACI,MAAM,CAAC,GAAG9V,IAAI;MAClC,CAAC,MACI,IAAI8V,MAAM,EAAE;QACb;QACA;QACAA,MAAM,CAACV,UAAU,CAAC,GAAGpV,IAAI;MAC7B;MACA;MACA;MACA,IAAI8V,MAAM,IACNA,MAAM,CAACC,GAAG,IACVD,MAAM,CAACE,KAAK,IACZ,OAAOF,MAAM,CAACC,GAAG,KAAK,UAAU,IAChC,OAAOD,MAAM,CAACE,KAAK,KAAK,UAAU,EAAE;QACpChW,IAAI,CAAC+V,GAAG,GAAGD,MAAM,CAACC,GAAG,CAACE,IAAI,CAACH,MAAM,CAAC;QAClC9V,IAAI,CAACgW,KAAK,GAAGF,MAAM,CAACE,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;MAC1C;MACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,EAAE;QACtC,OAAOA,MAAM;MACjB;MACA,OAAO9V,IAAI;IACf,CAAC,MACI;MACD;MACA,OAAOyC,QAAQ,CAAC0C,KAAK,CAACkF,MAAM,EAAEnE,IAAI,CAAC;IACvC;EACJ,CAAC,CAAC;EACFuP,WAAW,GAAGxN,WAAW,CAACoC,MAAM,EAAEkL,UAAU,EAAG9S,QAAQ,IAAK,UAAUsD,IAAI,EAAEG,IAAI,EAAE;IAC9E,MAAMgQ,EAAE,GAAGhQ,IAAI,CAAC,CAAC,CAAC;IAClB,IAAIlG,IAAI;IACR,IAAI,OAAOkW,EAAE,KAAK,QAAQ,EAAE;MACxB;MACAlW,IAAI,GAAG0V,eAAe,CAACQ,EAAE,CAAC;IAC9B,CAAC,MACI;MACD;MACAlW,IAAI,GAAGkW,EAAE,IAAIA,EAAE,CAACd,UAAU,CAAC;MAC3B;MACA,IAAI,CAACpV,IAAI,EAAE;QACPA,IAAI,GAAGkW,EAAE;MACb;IACJ;IACA,IAAIlW,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,KAAK,QAAQ,EAAE;MACvC,IAAIJ,IAAI,CAACE,KAAK,KAAK,cAAc,KAC3BF,IAAI,CAACe,QAAQ,IAAIf,IAAI,CAACa,IAAI,CAACC,UAAU,IAAKd,IAAI,CAACW,QAAQ,KAAK,CAAC,CAAC,EAAE;QAClE,IAAI,OAAOuV,EAAE,KAAK,QAAQ,EAAE;UACxB,OAAOR,eAAe,CAACQ,EAAE,CAAC;QAC9B,CAAC,MACI,IAAIA,EAAE,EAAE;UACTA,EAAE,CAACd,UAAU,CAAC,GAAG,IAAI;QACzB;QACA;QACApV,IAAI,CAACvC,IAAI,CAACyE,UAAU,CAAClC,IAAI,CAAC;MAC9B;IACJ,CAAC,MACI;MACD;MACAyC,QAAQ,CAAC0C,KAAK,CAACkF,MAAM,EAAEnE,IAAI,CAAC;IAChC;EACJ,CAAC,CAAC;AACN;AAEA,SAASiQ,mBAAmBA,CAAC5L,OAAO,EAAE+E,GAAG,EAAE;EACvC,MAAM;IAAEjE,SAAS;IAAEC;EAAM,CAAC,GAAGgE,GAAG,CAAC/G,gBAAgB,CAAC,CAAC;EACnD,IAAK,CAAC8C,SAAS,IAAI,CAACC,KAAK,IAAK,CAACf,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,IAAIA,OAAO,CAAC,EAAE;IACxF;EACJ;EACA;EACA,MAAM6L,SAAS,GAAG,CACd,mBAAmB,EACnB,sBAAsB,EACtB,iBAAiB,EACjB,0BAA0B,EAC1B,wBAAwB,EACxB,sBAAsB,EACtB,mBAAmB,EACnB,0BAA0B,CAC7B;EACD9G,GAAG,CAACrG,cAAc,CAACqG,GAAG,EAAE/E,OAAO,CAAC8L,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAED,SAAS,CAAC;AAC1F;AAEA,SAASE,gBAAgBA,CAAC/L,OAAO,EAAE+E,GAAG,EAAE;EACpC,IAAIrF,IAAI,CAACqF,GAAG,CAAC3H,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE;IACtC;IACA;EACJ;EACA,MAAM;IAAE4O,UAAU;IAAE3H,oBAAoB;IAAE9E,QAAQ;IAAEC,SAAS;IAAEC;EAAmB,CAAC,GAAGsF,GAAG,CAAC/G,gBAAgB,CAAC,CAAC;EAC5G;EACA,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkU,UAAU,CAACjU,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,MAAMuK,SAAS,GAAG2J,UAAU,CAAClU,CAAC,CAAC;IAC/B,MAAM8M,cAAc,GAAGvC,SAAS,GAAG7C,SAAS;IAC5C,MAAMqF,aAAa,GAAGxC,SAAS,GAAG9C,QAAQ;IAC1C,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAGmF,cAAc;IAClD,MAAME,aAAa,GAAGrF,kBAAkB,GAAGoF,aAAa;IACxDR,oBAAoB,CAAChC,SAAS,CAAC,GAAG,CAAC,CAAC;IACpCgC,oBAAoB,CAAChC,SAAS,CAAC,CAAC7C,SAAS,CAAC,GAAGpC,MAAM;IACnDiH,oBAAoB,CAAChC,SAAS,CAAC,CAAC9C,QAAQ,CAAC,GAAGuF,aAAa;EAC7D;EACA,MAAMmH,YAAY,GAAGjM,OAAO,CAAC,aAAa,CAAC;EAC3C,IAAI,CAACiM,YAAY,IAAI,CAACA,YAAY,CAAC5P,SAAS,EAAE;IAC1C;EACJ;EACA0I,GAAG,CAACvH,gBAAgB,CAACwC,OAAO,EAAE+E,GAAG,EAAE,CAACkH,YAAY,IAAIA,YAAY,CAAC5P,SAAS,CAAC,CAAC;EAC5E,OAAO,IAAI;AACf;AACA,SAAS6P,UAAUA,CAACha,MAAM,EAAE6S,GAAG,EAAE;EAC7BA,GAAG,CAACjH,mBAAmB,CAAC5L,MAAM,EAAE6S,GAAG,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA,SAASxG,gBAAgBA,CAACpG,MAAM,EAAEwK,YAAY,EAAEwJ,gBAAgB,EAAE;EAC9D,IAAI,CAACA,gBAAgB,IAAIA,gBAAgB,CAACpU,MAAM,KAAK,CAAC,EAAE;IACpD,OAAO4K,YAAY;EACvB;EACA,MAAMyJ,GAAG,GAAGD,gBAAgB,CAACE,MAAM,CAAEC,EAAE,IAAKA,EAAE,CAACnU,MAAM,KAAKA,MAAM,CAAC;EACjE,IAAI,CAACiU,GAAG,IAAIA,GAAG,CAACrU,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAO4K,YAAY;EACvB;EACA,MAAM4J,sBAAsB,GAAGH,GAAG,CAAC,CAAC,CAAC,CAACD,gBAAgB;EACtD,OAAOxJ,YAAY,CAAC0J,MAAM,CAAEG,EAAE,IAAKD,sBAAsB,CAACrI,OAAO,CAACsI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF;AACA,SAASC,uBAAuBA,CAACtU,MAAM,EAAEwK,YAAY,EAAEwJ,gBAAgB,EAAE9P,SAAS,EAAE;EAChF;EACA;EACA,IAAI,CAAClE,MAAM,EAAE;IACT;EACJ;EACA,MAAMuU,kBAAkB,GAAGnO,gBAAgB,CAACpG,MAAM,EAAEwK,YAAY,EAAEwJ,gBAAgB,CAAC;EACnF1O,iBAAiB,CAACtF,MAAM,EAAEuU,kBAAkB,EAAErQ,SAAS,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA,SAASsQ,eAAeA,CAACxU,MAAM,EAAE;EAC7B,OAAOiE,MAAM,CAACwQ,mBAAmB,CAACzU,MAAM,CAAC,CACpCkU,MAAM,CAAEha,IAAI,IAAKA,IAAI,CAACwa,UAAU,CAAC,IAAI,CAAC,IAAIxa,IAAI,CAAC0F,MAAM,GAAG,CAAC,CAAC,CAC1D+U,GAAG,CAAEza,IAAI,IAAKA,IAAI,CAAC0a,SAAS,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,SAASC,uBAAuBA,CAACjI,GAAG,EAAE/E,OAAO,EAAE;EAC3C,IAAIY,MAAM,IAAI,CAACG,KAAK,EAAE;IAClB;EACJ;EACA,IAAIrB,IAAI,CAACqF,GAAG,CAAC3H,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE;IACjC;IACA;EACJ;EACA,MAAM+O,gBAAgB,GAAGnM,OAAO,CAAC,6BAA6B,CAAC;EAC/D;EACA,IAAIiN,YAAY,GAAG,EAAE;EACrB,IAAInM,SAAS,EAAE;IACX,MAAMf,cAAc,GAAGD,MAAM;IAC7BmN,YAAY,GAAGA,YAAY,CAACvC,MAAM,CAAC,CAC/B,UAAU,EACV,YAAY,EACZ,SAAS,EACT,aAAa,EACb,iBAAiB,EACjB,kBAAkB,EAClB,qBAAqB,EACrB,kBAAkB,EAClB,mBAAmB,EACnB,oBAAoB,EACpB,QAAQ,CACX,CAAC;IACF,MAAMwC,qBAAqB,GAAGpJ,IAAI,CAAC,CAAC,GAC9B,CAAC;MAAE3L,MAAM,EAAE4H,cAAc;MAAEoM,gBAAgB,EAAE,CAAC,OAAO;IAAE,CAAC,CAAC,GACzD,EAAE;IACR;IACA;IACAM,uBAAuB,CAAC1M,cAAc,EAAE4M,eAAe,CAAC5M,cAAc,CAAC,EAAEoM,gBAAgB,GAAGA,gBAAgB,CAACzB,MAAM,CAACwC,qBAAqB,CAAC,GAAGf,gBAAgB,EAAErN,oBAAoB,CAACiB,cAAc,CAAC,CAAC;EACxM;EACAkN,YAAY,GAAGA,YAAY,CAACvC,MAAM,CAAC,CAC/B,gBAAgB,EAChB,2BAA2B,EAC3B,UAAU,EACV,YAAY,EACZ,kBAAkB,EAClB,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,WAAW,CACd,CAAC;EACF,KAAK,IAAI5S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmV,YAAY,CAAClV,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,MAAMK,MAAM,GAAG6H,OAAO,CAACiN,YAAY,CAACnV,CAAC,CAAC,CAAC;IACvCK,MAAM,IACFA,MAAM,CAACkE,SAAS,IAChBoQ,uBAAuB,CAACtU,MAAM,CAACkE,SAAS,EAAEsQ,eAAe,CAACxU,MAAM,CAACkE,SAAS,CAAC,EAAE8P,gBAAgB,CAAC;EACtG;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASgB,YAAYA,CAACzN,IAAI,EAAE;EACxBA,IAAI,CAAClM,YAAY,CAAC,QAAQ,EAAGtB,MAAM,IAAK;IACpC,MAAMkb,WAAW,GAAGlb,MAAM,CAACwN,IAAI,CAACtN,UAAU,CAAC,aAAa,CAAC,CAAC;IAC1D,IAAIgb,WAAW,EAAE;MACbA,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,CAAC;EACF1N,IAAI,CAAClM,YAAY,CAAC,QAAQ,EAAGtB,MAAM,IAAK;IACpC,MAAMuO,GAAG,GAAG,KAAK;IACjB,MAAM4M,KAAK,GAAG,OAAO;IACrBvC,UAAU,CAAC5Y,MAAM,EAAEuO,GAAG,EAAE4M,KAAK,EAAE,SAAS,CAAC;IACzCvC,UAAU,CAAC5Y,MAAM,EAAEuO,GAAG,EAAE4M,KAAK,EAAE,UAAU,CAAC;IAC1CvC,UAAU,CAAC5Y,MAAM,EAAEuO,GAAG,EAAE4M,KAAK,EAAE,WAAW,CAAC;EAC/C,CAAC,CAAC;EACF3N,IAAI,CAAClM,YAAY,CAAC,uBAAuB,EAAGtB,MAAM,IAAK;IACnD4Y,UAAU,CAAC5Y,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB,CAAC;IACzD4Y,UAAU,CAAC5Y,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,CAAC;IAC/D4Y,UAAU,CAAC5Y,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB,CAAC;EACzE,CAAC,CAAC;EACFwN,IAAI,CAAClM,YAAY,CAAC,UAAU,EAAE,CAACtB,MAAM,EAAEwN,IAAI,KAAK;IAC5C,MAAM4N,eAAe,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;IACtD,KAAK,IAAIxV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwV,eAAe,CAACvV,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMzF,IAAI,GAAGib,eAAe,CAACxV,CAAC,CAAC;MAC/B4F,WAAW,CAACxL,MAAM,EAAEG,IAAI,EAAE,CAAC6F,QAAQ,EAAEkF,MAAM,EAAE/K,IAAI,KAAK;QAClD,OAAO,UAAUkb,CAAC,EAAE5R,IAAI,EAAE;UACtB,OAAO+D,IAAI,CAACvM,OAAO,CAAC+B,GAAG,CAACgD,QAAQ,EAAEhG,MAAM,EAAEyJ,IAAI,EAAEtJ,IAAI,CAAC;QACzD,CAAC;MACL,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACFqN,IAAI,CAAClM,YAAY,CAAC,aAAa,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IACpDmH,UAAU,CAACha,MAAM,EAAE6S,GAAG,CAAC;IACvBgH,gBAAgB,CAAC7Z,MAAM,EAAE6S,GAAG,CAAC;IAC7B;IACA,MAAMyI,yBAAyB,GAAGtb,MAAM,CAAC,2BAA2B,CAAC;IACrE,IAAIsb,yBAAyB,IAAIA,yBAAyB,CAACnR,SAAS,EAAE;MAClE0I,GAAG,CAACvH,gBAAgB,CAACtL,MAAM,EAAE6S,GAAG,EAAE,CAACyI,yBAAyB,CAACnR,SAAS,CAAC,CAAC;IAC5E;EACJ,CAAC,CAAC;EACFqD,IAAI,CAAClM,YAAY,CAAC,kBAAkB,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IACzD1G,UAAU,CAAC,kBAAkB,CAAC;IAC9BA,UAAU,CAAC,wBAAwB,CAAC;EACxC,CAAC,CAAC;EACFqB,IAAI,CAAClM,YAAY,CAAC,sBAAsB,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IAC7D1G,UAAU,CAAC,sBAAsB,CAAC;EACtC,CAAC,CAAC;EACFqB,IAAI,CAAClM,YAAY,CAAC,YAAY,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IACnD1G,UAAU,CAAC,YAAY,CAAC;EAC5B,CAAC,CAAC;EACFqB,IAAI,CAAClM,YAAY,CAAC,aAAa,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IACpDiI,uBAAuB,CAACjI,GAAG,EAAE7S,MAAM,CAAC;EACxC,CAAC,CAAC;EACFwN,IAAI,CAAClM,YAAY,CAAC,gBAAgB,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IACvD6G,mBAAmB,CAAC1Z,MAAM,EAAE6S,GAAG,CAAC;EACpC,CAAC,CAAC;EACFrF,IAAI,CAAClM,YAAY,CAAC,KAAK,EAAE,CAACtB,MAAM,EAAEwN,IAAI,KAAK;IACvC;IACA+N,QAAQ,CAACvb,MAAM,CAAC;IAChB,MAAMwb,QAAQ,GAAG9N,UAAU,CAAC,SAAS,CAAC;IACtC,MAAM+N,QAAQ,GAAG/N,UAAU,CAAC,SAAS,CAAC;IACtC,MAAMgO,YAAY,GAAGhO,UAAU,CAAC,aAAa,CAAC;IAC9C,MAAMiO,aAAa,GAAGjO,UAAU,CAAC,cAAc,CAAC;IAChD,MAAMkO,OAAO,GAAGlO,UAAU,CAAC,QAAQ,CAAC;IACpC,MAAMmO,0BAA0B,GAAGnO,UAAU,CAAC,yBAAyB,CAAC;IACxE,SAAS6N,QAAQA,CAAC3N,MAAM,EAAE;MACtB,MAAMkO,cAAc,GAAGlO,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAI,CAACkO,cAAc,EAAE;QACjB;QACA;MACJ;MACA,MAAMC,uBAAuB,GAAGD,cAAc,CAAC3R,SAAS;MACxD,SAAS6R,eAAeA,CAAC/V,MAAM,EAAE;QAC7B,OAAOA,MAAM,CAACuV,QAAQ,CAAC;MAC3B;MACA,IAAIS,cAAc,GAAGF,uBAAuB,CAAC5O,8BAA8B,CAAC;MAC5E,IAAI+O,iBAAiB,GAAGH,uBAAuB,CAAC3O,iCAAiC,CAAC;MAClF,IAAI,CAAC6O,cAAc,EAAE;QACjB,MAAMX,yBAAyB,GAAG1N,MAAM,CAAC,2BAA2B,CAAC;QACrE,IAAI0N,yBAAyB,EAAE;UAC3B,MAAMa,kCAAkC,GAAGb,yBAAyB,CAACnR,SAAS;UAC9E8R,cAAc,GAAGE,kCAAkC,CAAChP,8BAA8B,CAAC;UACnF+O,iBAAiB,GAAGC,kCAAkC,CAAC/O,iCAAiC,CAAC;QAC7F;MACJ;MACA,MAAMgP,kBAAkB,GAAG,kBAAkB;MAC7C,MAAMC,SAAS,GAAG,WAAW;MAC7B,SAAS1X,YAAYA,CAACpB,IAAI,EAAE;QACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;QACtB,MAAM6B,MAAM,GAAG7B,IAAI,CAAC6B,MAAM;QAC1BA,MAAM,CAAC0V,aAAa,CAAC,GAAG,KAAK;QAC7B1V,MAAM,CAAC4V,0BAA0B,CAAC,GAAG,KAAK;QAC1C;QACA,MAAM3M,QAAQ,GAAGjJ,MAAM,CAACyV,YAAY,CAAC;QACrC,IAAI,CAACO,cAAc,EAAE;UACjBA,cAAc,GAAGhW,MAAM,CAACkH,8BAA8B,CAAC;UACvD+O,iBAAiB,GAAGjW,MAAM,CAACmH,iCAAiC,CAAC;QACjE;QACA,IAAI8B,QAAQ,EAAE;UACVgN,iBAAiB,CAAC1S,IAAI,CAACvD,MAAM,EAAEmW,kBAAkB,EAAElN,QAAQ,CAAC;QAChE;QACA,MAAMoN,WAAW,GAAIrW,MAAM,CAACyV,YAAY,CAAC,GAAG,MAAM;UAC9C,IAAIzV,MAAM,CAACsW,UAAU,KAAKtW,MAAM,CAACuW,IAAI,EAAE;YACnC;YACA;YACA,IAAI,CAACpY,IAAI,CAACkT,OAAO,IAAIrR,MAAM,CAAC0V,aAAa,CAAC,IAAIpY,IAAI,CAACE,KAAK,KAAK4Y,SAAS,EAAE;cACpE;cACA;cACA;cACA;cACA;cACA;cACA;cACA,MAAMI,SAAS,GAAGxW,MAAM,CAACuH,IAAI,CAACtN,UAAU,CAAC,WAAW,CAAC,CAAC;cACtD,IAAI+F,MAAM,CAACyW,MAAM,KAAK,CAAC,IAAID,SAAS,IAAIA,SAAS,CAAC5W,MAAM,GAAG,CAAC,EAAE;gBAC1D,MAAM8W,SAAS,GAAGpZ,IAAI,CAACJ,MAAM;gBAC7BI,IAAI,CAACJ,MAAM,GAAG,YAAY;kBACtB;kBACA;kBACA,MAAMsZ,SAAS,GAAGxW,MAAM,CAACuH,IAAI,CAACtN,UAAU,CAAC,WAAW,CAAC,CAAC;kBACtD,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6W,SAAS,CAAC5W,MAAM,EAAED,CAAC,EAAE,EAAE;oBACvC,IAAI6W,SAAS,CAAC7W,CAAC,CAAC,KAAKrC,IAAI,EAAE;sBACvBkZ,SAAS,CAACzG,MAAM,CAACpQ,CAAC,EAAE,CAAC,CAAC;oBAC1B;kBACJ;kBACA,IAAI,CAACxB,IAAI,CAACkT,OAAO,IAAI/T,IAAI,CAACE,KAAK,KAAK4Y,SAAS,EAAE;oBAC3CM,SAAS,CAACnT,IAAI,CAACjG,IAAI,CAAC;kBACxB;gBACJ,CAAC;gBACDkZ,SAAS,CAAC7T,IAAI,CAACrF,IAAI,CAAC;cACxB,CAAC,MACI;gBACDA,IAAI,CAACJ,MAAM,CAAC,CAAC;cACjB;YACJ,CAAC,MACI,IAAI,CAACiB,IAAI,CAACkT,OAAO,IAAIrR,MAAM,CAAC0V,aAAa,CAAC,KAAK,KAAK,EAAE;cACvD;cACA1V,MAAM,CAAC4V,0BAA0B,CAAC,GAAG,IAAI;YAC7C;UACJ;QACJ,CAAE;QACFI,cAAc,CAACzS,IAAI,CAACvD,MAAM,EAAEmW,kBAAkB,EAAEE,WAAW,CAAC;QAC5D,MAAMM,UAAU,GAAG3W,MAAM,CAACuV,QAAQ,CAAC;QACnC,IAAI,CAACoB,UAAU,EAAE;UACb3W,MAAM,CAACuV,QAAQ,CAAC,GAAGjY,IAAI;QAC3B;QACAsZ,UAAU,CAACnU,KAAK,CAACzC,MAAM,EAAE7B,IAAI,CAACqF,IAAI,CAAC;QACnCxD,MAAM,CAAC0V,aAAa,CAAC,GAAG,IAAI;QAC5B,OAAOpY,IAAI;MACf;MACA,SAASuZ,mBAAmBA,CAAA,EAAG,CAAE;MACjC,SAAS5D,SAASA,CAAC3V,IAAI,EAAE;QACrB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAI;QACtB;QACA;QACAA,IAAI,CAACkT,OAAO,GAAG,IAAI;QACnB,OAAOyF,WAAW,CAACrU,KAAK,CAACtE,IAAI,CAAC6B,MAAM,EAAE7B,IAAI,CAACqF,IAAI,CAAC;MACpD;MACA,MAAMuT,UAAU,GAAGxR,WAAW,CAACuQ,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAUzS,IAAI,EAAEG,IAAI,EAAE;QACxFH,IAAI,CAACmS,QAAQ,CAAC,GAAGhS,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK;QACjCH,IAAI,CAACsS,OAAO,CAAC,GAAGnS,IAAI,CAAC,CAAC,CAAC;QACvB,OAAOuT,UAAU,CAACtU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;MACvC,CAAC,CAAC;MACF,MAAMwT,qBAAqB,GAAG,qBAAqB;MACnD,MAAMC,iBAAiB,GAAGxP,UAAU,CAAC,mBAAmB,CAAC;MACzD,MAAMyP,mBAAmB,GAAGzP,UAAU,CAAC,qBAAqB,CAAC;MAC7D,MAAMmP,UAAU,GAAGrR,WAAW,CAACuQ,uBAAuB,EAAE,MAAM,EAAE,MAAM,UAAUzS,IAAI,EAAEG,IAAI,EAAE;QACxF,IAAI+D,IAAI,CAACvM,OAAO,CAACkc,mBAAmB,CAAC,KAAK,IAAI,EAAE;UAC5C;UACA;UACA;UACA,OAAON,UAAU,CAACnU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACvC;QACA,IAAIH,IAAI,CAACmS,QAAQ,CAAC,EAAE;UAChB;UACA,OAAOoB,UAAU,CAACnU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACvC,CAAC,MACI;UACD,MAAML,OAAO,GAAG;YACZnD,MAAM,EAAEqD,IAAI;YACZ8T,GAAG,EAAE9T,IAAI,CAACsS,OAAO,CAAC;YAClBvX,UAAU,EAAE,KAAK;YACjBoF,IAAI,EAAEA,IAAI;YACV6N,OAAO,EAAE;UACb,CAAC;UACD,MAAM/T,IAAI,GAAGkK,gCAAgC,CAACwP,qBAAqB,EAAEH,mBAAmB,EAAE1T,OAAO,EAAEzE,YAAY,EAAEuU,SAAS,CAAC;UAC3H,IAAI5P,IAAI,IACJA,IAAI,CAACuS,0BAA0B,CAAC,KAAK,IAAI,IACzC,CAACzS,OAAO,CAACkO,OAAO,IAChB/T,IAAI,CAACE,KAAK,KAAK4Y,SAAS,EAAE;YAC1B;YACA;YACA;YACA9Y,IAAI,CAACJ,MAAM,CAAC,CAAC;UACjB;QACJ;MACJ,CAAC,CAAC;MACF,MAAM4Z,WAAW,GAAGvR,WAAW,CAACuQ,uBAAuB,EAAE,OAAO,EAAE,MAAM,UAAUzS,IAAI,EAAEG,IAAI,EAAE;QAC1F,MAAMlG,IAAI,GAAGyY,eAAe,CAAC1S,IAAI,CAAC;QAClC,IAAI/F,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAI,IAAI,QAAQ,EAAE;UACtC;UACA;UACA;UACA;UACA,IAAIJ,IAAI,CAACe,QAAQ,IAAI,IAAI,IAAKf,IAAI,CAACa,IAAI,IAAIb,IAAI,CAACa,IAAI,CAACkT,OAAQ,EAAE;YAC3D;UACJ;UACA/T,IAAI,CAACvC,IAAI,CAACyE,UAAU,CAAClC,IAAI,CAAC;QAC9B,CAAC,MACI,IAAIiK,IAAI,CAACvM,OAAO,CAACic,iBAAiB,CAAC,KAAK,IAAI,EAAE;UAC/C;UACA,OAAOH,WAAW,CAACrU,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACxC;QACA;QACA;QACA;MACJ,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF+D,IAAI,CAAClM,YAAY,CAAC,aAAa,EAAGtB,MAAM,IAAK;IACzC;IACA,IAAIA,MAAM,CAAC,WAAW,CAAC,IAAIA,MAAM,CAAC,WAAW,CAAC,CAACqd,WAAW,EAAE;MACxDrP,cAAc,CAAChO,MAAM,CAAC,WAAW,CAAC,CAACqd,WAAW,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;IAC5F;EACJ,CAAC,CAAC;EACF7P,IAAI,CAAClM,YAAY,CAAC,uBAAuB,EAAE,CAACtB,MAAM,EAAEwN,IAAI,KAAK;IACzD;IACA,SAAS8P,2BAA2BA,CAACtF,OAAO,EAAE;MAC1C,OAAO,UAAUuF,CAAC,EAAE;QAChB,MAAMC,UAAU,GAAG5F,cAAc,CAAC5X,MAAM,EAAEgY,OAAO,CAAC;QAClDwF,UAAU,CAACC,OAAO,CAAE7Z,SAAS,IAAK;UAC9B;UACA;UACA,MAAM8Z,qBAAqB,GAAG1d,MAAM,CAAC,uBAAuB,CAAC;UAC7D,IAAI0d,qBAAqB,EAAE;YACvB,MAAMC,GAAG,GAAG,IAAID,qBAAqB,CAAC1F,OAAO,EAAE;cAC3C4F,OAAO,EAAEL,CAAC,CAACK,OAAO;cAClBC,MAAM,EAAEN,CAAC,CAACO;YACd,CAAC,CAAC;YACFla,SAAS,CAACT,MAAM,CAACwa,GAAG,CAAC;UACzB;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA,IAAI3d,MAAM,CAAC,uBAAuB,CAAC,EAAE;MACjCwN,IAAI,CAACE,UAAU,CAAC,kCAAkC,CAAC,CAAC,GAChD4P,2BAA2B,CAAC,oBAAoB,CAAC;MACrD9P,IAAI,CAACE,UAAU,CAAC,yBAAyB,CAAC,CAAC,GACvC4P,2BAA2B,CAAC,kBAAkB,CAAC;IACvD;EACJ,CAAC,CAAC;EACF9P,IAAI,CAAClM,YAAY,CAAC,gBAAgB,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IACvD6F,mBAAmB,CAAC1Y,MAAM,EAAE6S,GAAG,CAAC;EACpC,CAAC,CAAC;AACN;AAEA,SAASkL,YAAYA,CAACvQ,IAAI,EAAE;EACxBA,IAAI,CAAClM,YAAY,CAAC,kBAAkB,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IACzD,MAAM7G,8BAA8B,GAAG9B,MAAM,CAACwC,wBAAwB;IACtE,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAAc;IAClD,SAASqR,sBAAsBA,CAACrO,GAAG,EAAE;MACjC,IAAIA,GAAG,IAAIA,GAAG,CAAC3F,QAAQ,KAAKE,MAAM,CAACC,SAAS,CAACH,QAAQ,EAAE;QACnD,MAAM4G,SAAS,GAAGjB,GAAG,CAAC5N,WAAW,IAAI4N,GAAG,CAAC5N,WAAW,CAAC5B,IAAI;QACzD,OAAO,CAACyQ,SAAS,GAAGA,SAAS,GAAG,EAAE,IAAI,IAAI,GAAGqN,IAAI,CAACC,SAAS,CAACvO,GAAG,CAAC;MACpE;MACA,OAAOA,GAAG,GAAGA,GAAG,CAAC3F,QAAQ,CAAC,CAAC,GAAGE,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAACmG,GAAG,CAAC;IACrE;IACA,MAAMzP,UAAU,GAAG2S,GAAG,CAAC3H,MAAM;IAC7B,MAAMiT,sBAAsB,GAAG,EAAE;IACjC,MAAMC,yCAAyC,GAAGpe,MAAM,CAACE,UAAU,CAAC,6CAA6C,CAAC,CAAC,KAAK,KAAK;IAC7H,MAAMoK,aAAa,GAAGpK,UAAU,CAAC,SAAS,CAAC;IAC3C,MAAMqK,UAAU,GAAGrK,UAAU,CAAC,MAAM,CAAC;IACrC,MAAMme,aAAa,GAAG,mBAAmB;IACzCxL,GAAG,CAAC7H,gBAAgB,GAAIuS,CAAC,IAAK;MAC1B,IAAI1K,GAAG,CAACxH,iBAAiB,CAAC,CAAC,EAAE;QACzB,MAAMyS,SAAS,GAAGP,CAAC,IAAIA,CAAC,CAACO,SAAS;QAClC,IAAIA,SAAS,EAAE;UACXQ,OAAO,CAAClb,KAAK,CAAC,8BAA8B,EAAE0a,SAAS,YAAYhd,KAAK,GAAGgd,SAAS,CAACzO,OAAO,GAAGyO,SAAS,EAAE,SAAS,EAAEP,CAAC,CAACvc,IAAI,CAACb,IAAI,EAAE,SAAS,EAAEod,CAAC,CAACha,IAAI,IAAIga,CAAC,CAACha,IAAI,CAACZ,MAAM,EAAE,UAAU,EAAEmb,SAAS,EAAEA,SAAS,YAAYhd,KAAK,GAAGgd,SAAS,CAACS,KAAK,GAAGha,SAAS,CAAC;QAC1P,CAAC,MACI;UACD+Z,OAAO,CAAClb,KAAK,CAACma,CAAC,CAAC;QACpB;MACJ;IACJ,CAAC;IACD1K,GAAG,CAAC5H,kBAAkB,GAAG,MAAM;MAC3B,OAAOkT,sBAAsB,CAACtY,MAAM,EAAE;QAClC,MAAM2Y,oBAAoB,GAAGL,sBAAsB,CAACM,KAAK,CAAC,CAAC;QAC3D,IAAI;UACAD,oBAAoB,CAACxd,IAAI,CAAC8B,UAAU,CAAC,MAAM;YACvC,IAAI0b,oBAAoB,CAACE,aAAa,EAAE;cACpC,MAAMF,oBAAoB,CAACV,SAAS;YACxC;YACA,MAAMU,oBAAoB;UAC9B,CAAC,CAAC;QACN,CAAC,CACD,OAAOpb,KAAK,EAAE;UACVub,wBAAwB,CAACvb,KAAK,CAAC;QACnC;MACJ;IACJ,CAAC;IACD,MAAMwb,0CAA0C,GAAG1e,UAAU,CAAC,kCAAkC,CAAC;IACjG,SAASye,wBAAwBA,CAACpB,CAAC,EAAE;MACjC1K,GAAG,CAAC7H,gBAAgB,CAACuS,CAAC,CAAC;MACvB,IAAI;QACA,MAAMsB,OAAO,GAAGrR,IAAI,CAACoR,0CAA0C,CAAC;QAChE,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;UAC/BA,OAAO,CAACrV,IAAI,CAAC,IAAI,EAAE+T,CAAC,CAAC;QACzB;MACJ,CAAC,CACD,OAAOtY,GAAG,EAAE,CAAE;IAClB;IACA,SAAS6Z,UAAUA,CAAChW,KAAK,EAAE;MACvB,OAAOA,KAAK,IAAIA,KAAK,CAACiW,IAAI;IAC9B;IACA,SAASC,iBAAiBA,CAAClW,KAAK,EAAE;MAC9B,OAAOA,KAAK;IAChB;IACA,SAASmW,gBAAgBA,CAACnB,SAAS,EAAE;MACjC,OAAOoB,gBAAgB,CAACC,MAAM,CAACrB,SAAS,CAAC;IAC7C;IACA,MAAMsB,WAAW,GAAGlf,UAAU,CAAC,OAAO,CAAC;IACvC,MAAMmf,WAAW,GAAGnf,UAAU,CAAC,OAAO,CAAC;IACvC,MAAMof,aAAa,GAAGpf,UAAU,CAAC,SAAS,CAAC;IAC3C,MAAMqf,wBAAwB,GAAGrf,UAAU,CAAC,oBAAoB,CAAC;IACjE,MAAMsf,wBAAwB,GAAGtf,UAAU,CAAC,oBAAoB,CAAC;IACjE,MAAMyC,MAAM,GAAG,cAAc;IAC7B,MAAM8c,UAAU,GAAG,IAAI;IACvB,MAAMC,QAAQ,GAAG,IAAI;IACrB,MAAMC,QAAQ,GAAG,KAAK;IACtB,MAAMC,iBAAiB,GAAG,CAAC;IAC3B,SAASC,YAAYA,CAACjC,OAAO,EAAEna,KAAK,EAAE;MAClC,OAAQqc,CAAC,IAAK;QACV,IAAI;UACAC,cAAc,CAACnC,OAAO,EAAEna,KAAK,EAAEqc,CAAC,CAAC;QACrC,CAAC,CACD,OAAO7a,GAAG,EAAE;UACR8a,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE3Y,GAAG,CAAC;QACvC;QACA;MACJ,CAAC;IACL;IACA,MAAM8O,IAAI,GAAG,SAAAA,CAAA,EAAY;MACrB,IAAIiM,SAAS,GAAG,KAAK;MACrB,OAAO,SAASC,OAAOA,CAACC,eAAe,EAAE;QACrC,OAAO,YAAY;UACf,IAAIF,SAAS,EAAE;YACX;UACJ;UACAA,SAAS,GAAG,IAAI;UAChBE,eAAe,CAACxX,KAAK,CAAC,IAAI,EAAE3F,SAAS,CAAC;QAC1C,CAAC;MACL,CAAC;IACL,CAAC;IACD,MAAMod,UAAU,GAAG,8BAA8B;IACjD,MAAMC,yBAAyB,GAAGlgB,UAAU,CAAC,kBAAkB,CAAC;IAChE;IACA,SAAS6f,cAAcA,CAACnC,OAAO,EAAEna,KAAK,EAAEqF,KAAK,EAAE;MAC3C,MAAMuX,WAAW,GAAGtM,IAAI,CAAC,CAAC;MAC1B,IAAI6J,OAAO,KAAK9U,KAAK,EAAE;QACnB,MAAM,IAAIwX,SAAS,CAACH,UAAU,CAAC;MACnC;MACA,IAAIvC,OAAO,CAACwB,WAAW,CAAC,KAAKK,UAAU,EAAE;QACrC;QACA,IAAIV,IAAI,GAAG,IAAI;QACf,IAAI;UACA,IAAI,OAAOjW,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;YAC1DiW,IAAI,GAAGjW,KAAK,IAAIA,KAAK,CAACiW,IAAI;UAC9B;QACJ,CAAC,CACD,OAAO9Z,GAAG,EAAE;UACRob,WAAW,CAAC,MAAM;YACdN,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE3Y,GAAG,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC;UACJ,OAAO2Y,OAAO;QAClB;QACA;QACA,IAAIna,KAAK,KAAKkc,QAAQ,IAClB7W,KAAK,YAAYoW,gBAAgB,IACjCpW,KAAK,CAACrH,cAAc,CAAC2d,WAAW,CAAC,IACjCtW,KAAK,CAACrH,cAAc,CAAC4d,WAAW,CAAC,IACjCvW,KAAK,CAACsW,WAAW,CAAC,KAAKK,UAAU,EAAE;UACnCc,oBAAoB,CAACzX,KAAK,CAAC;UAC3BiX,cAAc,CAACnC,OAAO,EAAE9U,KAAK,CAACsW,WAAW,CAAC,EAAEtW,KAAK,CAACuW,WAAW,CAAC,CAAC;QACnE,CAAC,MACI,IAAI5b,KAAK,KAAKkc,QAAQ,IAAI,OAAOZ,IAAI,KAAK,UAAU,EAAE;UACvD,IAAI;YACAA,IAAI,CAACvV,IAAI,CAACV,KAAK,EAAEuX,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAEna,KAAK,CAAC,CAAC,EAAE4c,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;UAC1G,CAAC,CACD,OAAO3Y,GAAG,EAAE;YACRob,WAAW,CAAC,MAAM;cACdN,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAE3Y,GAAG,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC;UACR;QACJ,CAAC,MACI;UACD2Y,OAAO,CAACwB,WAAW,CAAC,GAAG3b,KAAK;UAC5B,MAAMsH,KAAK,GAAG6S,OAAO,CAACyB,WAAW,CAAC;UAClCzB,OAAO,CAACyB,WAAW,CAAC,GAAGvW,KAAK;UAC5B,IAAI8U,OAAO,CAAC0B,aAAa,CAAC,KAAKA,aAAa,EAAE;YAC1C;YACA,IAAI7b,KAAK,KAAKic,QAAQ,EAAE;cACpB;cACA;cACA9B,OAAO,CAACwB,WAAW,CAAC,GAAGxB,OAAO,CAAC4B,wBAAwB,CAAC;cACxD5B,OAAO,CAACyB,WAAW,CAAC,GAAGzB,OAAO,CAAC2B,wBAAwB,CAAC;YAC5D;UACJ;UACA;UACA;UACA,IAAI9b,KAAK,KAAKkc,QAAQ,IAAI7W,KAAK,YAAYhI,KAAK,EAAE;YAC9C;YACA,MAAM0f,KAAK,GAAGhT,IAAI,CAACpM,WAAW,IAC1BoM,IAAI,CAACpM,WAAW,CAACgD,IAAI,IACrBoJ,IAAI,CAACpM,WAAW,CAACgD,IAAI,CAACia,aAAa,CAAC;YACxC,IAAImC,KAAK,EAAE;cACP;cACAzU,oBAAoB,CAACjD,KAAK,EAAEsX,yBAAyB,EAAE;gBACnDrQ,YAAY,EAAE,IAAI;gBAClBD,UAAU,EAAE,KAAK;gBACjBxB,QAAQ,EAAE,IAAI;gBACdxF,KAAK,EAAE0X;cACX,CAAC,CAAC;YACN;UACJ;UACA,KAAK,IAAI5a,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmF,KAAK,CAAClF,MAAM,GAAG;YAC/B4a,uBAAuB,CAAC7C,OAAO,EAAE7S,KAAK,CAACnF,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAACnF,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAACnF,CAAC,EAAE,CAAC,EAAEmF,KAAK,CAACnF,CAAC,EAAE,CAAC,CAAC;UACpF;UACA,IAAImF,KAAK,CAAClF,MAAM,IAAI,CAAC,IAAIpC,KAAK,IAAIkc,QAAQ,EAAE;YACxC/B,OAAO,CAACwB,WAAW,CAAC,GAAGQ,iBAAiB;YACxC,IAAIpB,oBAAoB,GAAG1V,KAAK;YAChC,IAAI;cACA;cACA;cACA;cACA,MAAM,IAAIhI,KAAK,CAAC,yBAAyB,GACrCkd,sBAAsB,CAAClV,KAAK,CAAC,IAC5BA,KAAK,IAAIA,KAAK,CAACyV,KAAK,GAAG,IAAI,GAAGzV,KAAK,CAACyV,KAAK,GAAG,EAAE,CAAC,CAAC;YACzD,CAAC,CACD,OAAOtZ,GAAG,EAAE;cACRuZ,oBAAoB,GAAGvZ,GAAG;YAC9B;YACA,IAAImZ,yCAAyC,EAAE;cAC3C;cACA;cACAI,oBAAoB,CAACE,aAAa,GAAG,IAAI;YAC7C;YACAF,oBAAoB,CAACV,SAAS,GAAGhV,KAAK;YACtC0V,oBAAoB,CAACZ,OAAO,GAAGA,OAAO;YACtCY,oBAAoB,CAACxd,IAAI,GAAGwM,IAAI,CAACvM,OAAO;YACxCud,oBAAoB,CAACjb,IAAI,GAAGiK,IAAI,CAACpM,WAAW;YAC5C+c,sBAAsB,CAACvV,IAAI,CAAC4V,oBAAoB,CAAC;YACjD3L,GAAG,CAAC3N,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAC7B;QACJ;MACJ;MACA;MACA,OAAO0Y,OAAO;IAClB;IACA,MAAM8C,yBAAyB,GAAGxgB,UAAU,CAAC,yBAAyB,CAAC;IACvE,SAASqgB,oBAAoBA,CAAC3C,OAAO,EAAE;MACnC,IAAIA,OAAO,CAACwB,WAAW,CAAC,KAAKQ,iBAAiB,EAAE;QAC5C;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,MAAMf,OAAO,GAAGrR,IAAI,CAACkT,yBAAyB,CAAC;UAC/C,IAAI7B,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;YAC1CA,OAAO,CAACrV,IAAI,CAAC,IAAI,EAAE;cAAEsU,SAAS,EAAEF,OAAO,CAACyB,WAAW,CAAC;cAAEzB,OAAO,EAAEA;YAAQ,CAAC,CAAC;UAC7E;QACJ,CAAC,CACD,OAAO3Y,GAAG,EAAE,CAAE;QACd2Y,OAAO,CAACwB,WAAW,CAAC,GAAGO,QAAQ;QAC/B,KAAK,IAAI/Z,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuY,sBAAsB,CAACtY,MAAM,EAAED,CAAC,EAAE,EAAE;UACpD,IAAIgY,OAAO,KAAKO,sBAAsB,CAACvY,CAAC,CAAC,CAACgY,OAAO,EAAE;YAC/CO,sBAAsB,CAACnI,MAAM,CAACpQ,CAAC,EAAE,CAAC,CAAC;UACvC;QACJ;MACJ;IACJ;IACA,SAAS6a,uBAAuBA,CAAC7C,OAAO,EAAE5c,IAAI,EAAE2f,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAE;MACnFN,oBAAoB,CAAC3C,OAAO,CAAC;MAC7B,MAAMkD,YAAY,GAAGlD,OAAO,CAACwB,WAAW,CAAC;MACzC,MAAMpZ,QAAQ,GAAG8a,YAAY,GACvB,OAAOF,WAAW,KAAK,UAAU,GAC7BA,WAAW,GACX5B,iBAAiB,GACrB,OAAO6B,UAAU,KAAK,UAAU,GAC5BA,UAAU,GACV5B,gBAAgB;MAC1Bje,IAAI,CAACkE,iBAAiB,CAACvC,MAAM,EAAE,MAAM;QACjC,IAAI;UACA,MAAMoe,kBAAkB,GAAGnD,OAAO,CAACyB,WAAW,CAAC;UAC/C,MAAM2B,gBAAgB,GAAG,CAAC,CAACL,YAAY,IAAIrB,aAAa,KAAKqB,YAAY,CAACrB,aAAa,CAAC;UACxF,IAAI0B,gBAAgB,EAAE;YAClB;YACAL,YAAY,CAACpB,wBAAwB,CAAC,GAAGwB,kBAAkB;YAC3DJ,YAAY,CAACnB,wBAAwB,CAAC,GAAGsB,YAAY;UACzD;UACA;UACA,MAAMhY,KAAK,GAAG9H,IAAI,CAACgC,GAAG,CAACgD,QAAQ,EAAEzB,SAAS,EAAEyc,gBAAgB,IAAIhb,QAAQ,KAAKiZ,gBAAgB,IAAIjZ,QAAQ,KAAKgZ,iBAAiB,GACzH,EAAE,GACF,CAAC+B,kBAAkB,CAAC,CAAC;UAC3BhB,cAAc,CAACY,YAAY,EAAE,IAAI,EAAE7X,KAAK,CAAC;QAC7C,CAAC,CACD,OAAO1F,KAAK,EAAE;UACV;UACA2c,cAAc,CAACY,YAAY,EAAE,KAAK,EAAEvd,KAAK,CAAC;QAC9C;MACJ,CAAC,EAAEud,YAAY,CAAC;IACpB;IACA,MAAMM,4BAA4B,GAAG,+CAA+C;IACpF,MAAM7V,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;IAC5B,MAAM8V,cAAc,GAAGlhB,MAAM,CAACkhB,cAAc;IAC5C,MAAMhC,gBAAgB,CAAC;MACnB,OAAOlV,QAAQA,CAAA,EAAG;QACd,OAAOiX,4BAA4B;MACvC;MACA,OAAOpW,OAAOA,CAAC/B,KAAK,EAAE;QAClB,IAAIA,KAAK,YAAYoW,gBAAgB,EAAE;UACnC,OAAOpW,KAAK;QAChB;QACA,OAAOiX,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEL,QAAQ,EAAE5W,KAAK,CAAC;MAC1D;MACA,OAAOqW,MAAMA,CAAC/b,KAAK,EAAE;QACjB,OAAO2c,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAEJ,QAAQ,EAAEvc,KAAK,CAAC;MAC1D;MACA,OAAO+d,aAAaA,CAAA,EAAG;QACnB,MAAMhS,MAAM,GAAG,CAAC,CAAC;QACjBA,MAAM,CAACyO,OAAO,GAAG,IAAIsB,gBAAgB,CAAC,CAACkC,GAAG,EAAEC,GAAG,KAAK;UAChDlS,MAAM,CAACtE,OAAO,GAAGuW,GAAG;UACpBjS,MAAM,CAACgQ,MAAM,GAAGkC,GAAG;QACvB,CAAC,CAAC;QACF,OAAOlS,MAAM;MACjB;MACA,OAAOmS,GAAGA,CAACC,MAAM,EAAE;QACf,IAAI,CAACA,MAAM,IAAI,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE;UAC1D,OAAOC,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,MAAMS,QAAQ,GAAG,EAAE;QACnB,IAAIhc,KAAK,GAAG,CAAC;QACb,IAAI;UACA,KAAK,IAAIma,CAAC,IAAIyB,MAAM,EAAE;YAClB5b,KAAK,EAAE;YACPgc,QAAQ,CAAC/Y,IAAI,CAACsW,gBAAgB,CAACrU,OAAO,CAACiV,CAAC,CAAC,CAAC;UAC9C;QACJ,CAAC,CACD,OAAO7a,GAAG,EAAE;UACR,OAAOyc,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIvb,KAAK,KAAK,CAAC,EAAE;UACb,OAAO+b,OAAO,CAACvC,MAAM,CAAC,IAAI+B,cAAc,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC;QAC/E;QACA,IAAIU,QAAQ,GAAG,KAAK;QACpB,MAAMxN,MAAM,GAAG,EAAE;QACjB,OAAO,IAAI8K,gBAAgB,CAAC,CAACrU,OAAO,EAAEsU,MAAM,KAAK;UAC7C,KAAK,IAAIvZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+b,QAAQ,CAAC9b,MAAM,EAAED,CAAC,EAAE,EAAE;YACtC+b,QAAQ,CAAC/b,CAAC,CAAC,CAACmZ,IAAI,CAAEe,CAAC,IAAK;cACpB,IAAI8B,QAAQ,EAAE;gBACV;cACJ;cACAA,QAAQ,GAAG,IAAI;cACf/W,OAAO,CAACiV,CAAC,CAAC;YACd,CAAC,EAAG7a,GAAG,IAAK;cACRmP,MAAM,CAACxL,IAAI,CAAC3D,GAAG,CAAC;cAChBU,KAAK,EAAE;cACP,IAAIA,KAAK,KAAK,CAAC,EAAE;gBACbic,QAAQ,GAAG,IAAI;gBACfzC,MAAM,CAAC,IAAI+B,cAAc,CAAC9M,MAAM,EAAE,4BAA4B,CAAC,CAAC;cACpE;YACJ,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;MACA,OAAOyN,IAAIA,CAACN,MAAM,EAAE;QAChB,IAAI1W,OAAO;QACX,IAAIsU,MAAM;QACV,IAAIvB,OAAO,GAAG,IAAI,IAAI,CAAC,CAACwD,GAAG,EAAEC,GAAG,KAAK;UACjCxW,OAAO,GAAGuW,GAAG;UACbjC,MAAM,GAAGkC,GAAG;QAChB,CAAC,CAAC;QACF,SAASS,SAASA,CAAChZ,KAAK,EAAE;UACtB+B,OAAO,CAAC/B,KAAK,CAAC;QAClB;QACA,SAASiZ,QAAQA,CAAC3e,KAAK,EAAE;UACrB+b,MAAM,CAAC/b,KAAK,CAAC;QACjB;QACA,KAAK,IAAI0F,KAAK,IAAIyY,MAAM,EAAE;UACtB,IAAI,CAACzC,UAAU,CAAChW,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;UAC/B;UACAA,KAAK,CAACiW,IAAI,CAAC+C,SAAS,EAAEC,QAAQ,CAAC;QACnC;QACA,OAAOnE,OAAO;MAClB;MACA,OAAOoE,GAAGA,CAACT,MAAM,EAAE;QACf,OAAOrC,gBAAgB,CAAC+C,eAAe,CAACV,MAAM,CAAC;MACnD;MACA,OAAOW,UAAUA,CAACX,MAAM,EAAE;QACtB,MAAMY,CAAC,GAAG,IAAI,IAAI,IAAI,CAAChY,SAAS,YAAY+U,gBAAgB,GAAG,IAAI,GAAGA,gBAAgB;QACtF,OAAOiD,CAAC,CAACF,eAAe,CAACV,MAAM,EAAE;UAC7Ba,YAAY,EAAGtZ,KAAK,KAAM;YAAE4T,MAAM,EAAE,WAAW;YAAE5T;UAAM,CAAC,CAAC;UACzDuZ,aAAa,EAAGpd,GAAG,KAAM;YAAEyX,MAAM,EAAE,UAAU;YAAEmB,MAAM,EAAE5Y;UAAI,CAAC;QAChE,CAAC,CAAC;MACN;MACA,OAAOgd,eAAeA,CAACV,MAAM,EAAE7e,QAAQ,EAAE;QACrC,IAAImI,OAAO;QACX,IAAIsU,MAAM;QACV,IAAIvB,OAAO,GAAG,IAAI,IAAI,CAAC,CAACwD,GAAG,EAAEC,GAAG,KAAK;UACjCxW,OAAO,GAAGuW,GAAG;UACbjC,MAAM,GAAGkC,GAAG;QAChB,CAAC,CAAC;QACF;QACA,IAAIiB,eAAe,GAAG,CAAC;QACvB,IAAIC,UAAU,GAAG,CAAC;QAClB,MAAMC,cAAc,GAAG,EAAE;QACzB,KAAK,IAAI1Z,KAAK,IAAIyY,MAAM,EAAE;UACtB,IAAI,CAACzC,UAAU,CAAChW,KAAK,CAAC,EAAE;YACpBA,KAAK,GAAG,IAAI,CAAC+B,OAAO,CAAC/B,KAAK,CAAC;UAC/B;UACA,MAAM2Z,aAAa,GAAGF,UAAU;UAChC,IAAI;YACAzZ,KAAK,CAACiW,IAAI,CAAEjW,KAAK,IAAK;cAClB0Z,cAAc,CAACC,aAAa,CAAC,GAAG/f,QAAQ,GAAGA,QAAQ,CAAC0f,YAAY,CAACtZ,KAAK,CAAC,GAAGA,KAAK;cAC/EwZ,eAAe,EAAE;cACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;gBACvBzX,OAAO,CAAC2X,cAAc,CAAC;cAC3B;YACJ,CAAC,EAAGvd,GAAG,IAAK;cACR,IAAI,CAACvC,QAAQ,EAAE;gBACXyc,MAAM,CAACla,GAAG,CAAC;cACf,CAAC,MACI;gBACDud,cAAc,CAACC,aAAa,CAAC,GAAG/f,QAAQ,CAAC2f,aAAa,CAACpd,GAAG,CAAC;gBAC3Dqd,eAAe,EAAE;gBACjB,IAAIA,eAAe,KAAK,CAAC,EAAE;kBACvBzX,OAAO,CAAC2X,cAAc,CAAC;gBAC3B;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,CACD,OAAOE,OAAO,EAAE;YACZvD,MAAM,CAACuD,OAAO,CAAC;UACnB;UACAJ,eAAe,EAAE;UACjBC,UAAU,EAAE;QAChB;QACA;QACAD,eAAe,IAAI,CAAC;QACpB,IAAIA,eAAe,KAAK,CAAC,EAAE;UACvBzX,OAAO,CAAC2X,cAAc,CAAC;QAC3B;QACA,OAAO5E,OAAO;MAClB;MACA7b,WAAWA,CAAC4gB,QAAQ,EAAE;QAClB,MAAM/E,OAAO,GAAG,IAAI;QACpB,IAAI,EAAEA,OAAO,YAAYsB,gBAAgB,CAAC,EAAE;UACxC,MAAM,IAAIpe,KAAK,CAAC,gCAAgC,CAAC;QACrD;QACA8c,OAAO,CAACwB,WAAW,CAAC,GAAGK,UAAU;QACjC7B,OAAO,CAACyB,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3B,IAAI;UACA,MAAMgB,WAAW,GAAGtM,IAAI,CAAC,CAAC;UAC1B4O,QAAQ,IACJA,QAAQ,CAACtC,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE8B,QAAQ,CAAC,CAAC,EAAEW,WAAW,CAACR,YAAY,CAACjC,OAAO,EAAE+B,QAAQ,CAAC,CAAC,CAAC;QAC5G,CAAC,CACD,OAAOvc,KAAK,EAAE;UACV2c,cAAc,CAACnC,OAAO,EAAE,KAAK,EAAExa,KAAK,CAAC;QACzC;MACJ;MACA,KAAKoe,MAAM,CAACoB,WAAW,IAAI;QACvB,OAAO,SAAS;MACpB;MACA,KAAKpB,MAAM,CAACqB,OAAO,IAAI;QACnB,OAAO3D,gBAAgB;MAC3B;MACAH,IAAIA,CAAC6B,WAAW,EAAEC,UAAU,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIiC,CAAC,GAAG,IAAI,CAAC/gB,WAAW,GAAGyf,MAAM,CAACqB,OAAO,CAAC;QAC1C,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAG,IAAI,CAAC/gB,WAAW,IAAImd,gBAAgB;QAC5C;QACA,MAAMyB,YAAY,GAAG,IAAImC,CAAC,CAAC1X,IAAI,CAAC;QAChC,MAAMpK,IAAI,GAAGwM,IAAI,CAACvM,OAAO;QACzB,IAAI,IAAI,CAACme,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAACzW,IAAI,CAAC5H,IAAI,EAAE2f,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QACvE,CAAC,MACI;UACDJ,uBAAuB,CAAC,IAAI,EAAEzf,IAAI,EAAE2f,YAAY,EAAEC,WAAW,EAAEC,UAAU,CAAC;QAC9E;QACA,OAAOF,YAAY;MACvB;MACAoC,KAAKA,CAAClC,UAAU,EAAE;QACd,OAAO,IAAI,CAAC9B,IAAI,CAAC,IAAI,EAAE8B,UAAU,CAAC;MACtC;MACAmC,OAAOA,CAACC,SAAS,EAAE;QACf;QACA,IAAIH,CAAC,GAAG,IAAI,CAAC/gB,WAAW,GAAGyf,MAAM,CAACqB,OAAO,CAAC;QAC1C,IAAI,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;UAC/BA,CAAC,GAAG5D,gBAAgB;QACxB;QACA,MAAMyB,YAAY,GAAG,IAAImC,CAAC,CAAC1X,IAAI,CAAC;QAChCuV,YAAY,CAACrB,aAAa,CAAC,GAAGA,aAAa;QAC3C,MAAMte,IAAI,GAAGwM,IAAI,CAACvM,OAAO;QACzB,IAAI,IAAI,CAACme,WAAW,CAAC,IAAIK,UAAU,EAAE;UACjC,IAAI,CAACJ,WAAW,CAAC,CAACzW,IAAI,CAAC5H,IAAI,EAAE2f,YAAY,EAAEsC,SAAS,EAAEA,SAAS,CAAC;QACpE,CAAC,MACI;UACDxC,uBAAuB,CAAC,IAAI,EAAEzf,IAAI,EAAE2f,YAAY,EAAEsC,SAAS,EAAEA,SAAS,CAAC;QAC3E;QACA,OAAOtC,YAAY;MACvB;IACJ;IACA;IACA;IACAzB,gBAAgB,CAAC,SAAS,CAAC,GAAGA,gBAAgB,CAACrU,OAAO;IACtDqU,gBAAgB,CAAC,QAAQ,CAAC,GAAGA,gBAAgB,CAACC,MAAM;IACpDD,gBAAgB,CAAC,MAAM,CAAC,GAAGA,gBAAgB,CAAC2C,IAAI;IAChD3C,gBAAgB,CAAC,KAAK,CAAC,GAAGA,gBAAgB,CAAC8C,GAAG;IAC9C,MAAMkB,aAAa,GAAIljB,MAAM,CAACsK,aAAa,CAAC,GAAGtK,MAAM,CAAC,SAAS,CAAE;IACjEA,MAAM,CAAC,SAAS,CAAC,GAAGkf,gBAAgB;IACpC,MAAMiE,iBAAiB,GAAGjjB,UAAU,CAAC,aAAa,CAAC;IACnD,SAASwL,SAASA,CAAC0X,IAAI,EAAE;MACrB,MAAMnS,KAAK,GAAGmS,IAAI,CAACjZ,SAAS;MAC5B,MAAMyF,IAAI,GAAG5D,8BAA8B,CAACiF,KAAK,EAAE,MAAM,CAAC;MAC1D,IAAIrB,IAAI,KAAKA,IAAI,CAACtB,QAAQ,KAAK,KAAK,IAAI,CAACsB,IAAI,CAACG,YAAY,CAAC,EAAE;QACzD;QACA;QACA;MACJ;MACA,MAAMsT,YAAY,GAAGpS,KAAK,CAAC8N,IAAI;MAC/B;MACA9N,KAAK,CAAC1G,UAAU,CAAC,GAAG8Y,YAAY;MAChCD,IAAI,CAACjZ,SAAS,CAAC4U,IAAI,GAAG,UAAU+C,SAAS,EAAEC,QAAQ,EAAE;QACjD,MAAMuB,OAAO,GAAG,IAAIpE,gBAAgB,CAAC,CAACrU,OAAO,EAAEsU,MAAM,KAAK;UACtDkE,YAAY,CAAC7Z,IAAI,CAAC,IAAI,EAAEqB,OAAO,EAAEsU,MAAM,CAAC;QAC5C,CAAC,CAAC;QACF,OAAOmE,OAAO,CAACvE,IAAI,CAAC+C,SAAS,EAAEC,QAAQ,CAAC;MAC5C,CAAC;MACDqB,IAAI,CAACD,iBAAiB,CAAC,GAAG,IAAI;IAClC;IACAtQ,GAAG,CAACnH,SAAS,GAAGA,SAAS;IACzB,SAAS6X,OAAOA,CAAChiB,EAAE,EAAE;MACjB,OAAO,UAAU+H,IAAI,EAAEG,IAAI,EAAE;QACzB,IAAI+Z,aAAa,GAAGjiB,EAAE,CAACmH,KAAK,CAACY,IAAI,EAAEG,IAAI,CAAC;QACxC,IAAI+Z,aAAa,YAAYtE,gBAAgB,EAAE;UAC3C,OAAOsE,aAAa;QACxB;QACA,IAAIC,IAAI,GAAGD,aAAa,CAACzhB,WAAW;QACpC,IAAI,CAAC0hB,IAAI,CAACN,iBAAiB,CAAC,EAAE;UAC1BzX,SAAS,CAAC+X,IAAI,CAAC;QACnB;QACA,OAAOD,aAAa;MACxB,CAAC;IACL;IACA,IAAIN,aAAa,EAAE;MACfxX,SAAS,CAACwX,aAAa,CAAC;MACxB1X,WAAW,CAACxL,MAAM,EAAE,OAAO,EAAGgG,QAAQ,IAAKud,OAAO,CAACvd,QAAQ,CAAC,CAAC;IACjE;IACA;IACA0b,OAAO,CAAClU,IAAI,CAACtN,UAAU,CAAC,uBAAuB,CAAC,CAAC,GAAGie,sBAAsB;IAC1E,OAAOe,gBAAgB;EAC3B,CAAC,CAAC;AACN;AAEA,SAASwE,aAAaA,CAAClW,IAAI,EAAE;EACzB;EACA;EACAA,IAAI,CAAClM,YAAY,CAAC,UAAU,EAAGtB,MAAM,IAAK;IACtC;IACA,MAAM2jB,wBAAwB,GAAGC,QAAQ,CAACzZ,SAAS,CAACH,QAAQ;IAC5D,MAAM6Z,wBAAwB,GAAGnW,UAAU,CAAC,kBAAkB,CAAC;IAC/D,MAAMoW,cAAc,GAAGpW,UAAU,CAAC,SAAS,CAAC;IAC5C,MAAMqW,YAAY,GAAGrW,UAAU,CAAC,OAAO,CAAC;IACxC,MAAMsW,mBAAmB,GAAG,SAASha,QAAQA,CAAA,EAAG;MAC5C,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC5B,MAAM8J,gBAAgB,GAAG,IAAI,CAAC+P,wBAAwB,CAAC;QACvD,IAAI/P,gBAAgB,EAAE;UAClB,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;YACxC,OAAO6P,wBAAwB,CAACna,IAAI,CAACsK,gBAAgB,CAAC;UAC1D,CAAC,MACI;YACD,OAAO5J,MAAM,CAACC,SAAS,CAACH,QAAQ,CAACR,IAAI,CAACsK,gBAAgB,CAAC;UAC3D;QACJ;QACA,IAAI,IAAI,KAAK4N,OAAO,EAAE;UAClB,MAAMuC,aAAa,GAAGjkB,MAAM,CAAC8jB,cAAc,CAAC;UAC5C,IAAIG,aAAa,EAAE;YACf,OAAON,wBAAwB,CAACna,IAAI,CAACya,aAAa,CAAC;UACvD;QACJ;QACA,IAAI,IAAI,KAAKnjB,KAAK,EAAE;UAChB,MAAMojB,WAAW,GAAGlkB,MAAM,CAAC+jB,YAAY,CAAC;UACxC,IAAIG,WAAW,EAAE;YACb,OAAOP,wBAAwB,CAACna,IAAI,CAAC0a,WAAW,CAAC;UACrD;QACJ;MACJ;MACA,OAAOP,wBAAwB,CAACna,IAAI,CAAC,IAAI,CAAC;IAC9C,CAAC;IACDwa,mBAAmB,CAACH,wBAAwB,CAAC,GAAGF,wBAAwB;IACxEC,QAAQ,CAACzZ,SAAS,CAACH,QAAQ,GAAGga,mBAAmB;IACjD;IACA,MAAMG,sBAAsB,GAAGja,MAAM,CAACC,SAAS,CAACH,QAAQ;IACxD,MAAMoa,wBAAwB,GAAG,kBAAkB;IACnDla,MAAM,CAACC,SAAS,CAACH,QAAQ,GAAG,YAAY;MACpC,IAAI,OAAO0X,OAAO,KAAK,UAAU,IAAI,IAAI,YAAYA,OAAO,EAAE;QAC1D,OAAO0C,wBAAwB;MACnC;MACA,OAAOD,sBAAsB,CAAC3a,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC;EACL,CAAC,CAAC;AACN;AAEA,SAASgD,cAAcA,CAACqG,GAAG,EAAE5M,MAAM,EAAEoe,UAAU,EAAEC,MAAM,EAAE3K,SAAS,EAAE;EAChE,MAAMzO,MAAM,GAAGsC,IAAI,CAACtN,UAAU,CAACokB,MAAM,CAAC;EACtC,IAAIre,MAAM,CAACiF,MAAM,CAAC,EAAE;IAChB;EACJ;EACA,MAAMqZ,cAAc,GAAIte,MAAM,CAACiF,MAAM,CAAC,GAAGjF,MAAM,CAACqe,MAAM,CAAE;EACxDre,MAAM,CAACqe,MAAM,CAAC,GAAG,UAAUnkB,IAAI,EAAEqkB,IAAI,EAAEpb,OAAO,EAAE;IAC5C,IAAIob,IAAI,IAAIA,IAAI,CAACra,SAAS,EAAE;MACxBwP,SAAS,CAAC8D,OAAO,CAAC,UAAU/a,QAAQ,EAAE;QAClC,MAAMC,MAAM,GAAI,GAAE0hB,UAAW,IAAGC,MAAO,IAAG,GAAG5hB,QAAQ;QACrD,MAAMyH,SAAS,GAAGqa,IAAI,CAACra,SAAS;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI;UACA,IAAIA,SAAS,CAAC1I,cAAc,CAACiB,QAAQ,CAAC,EAAE;YACpC,MAAM+hB,UAAU,GAAG5R,GAAG,CAAC7G,8BAA8B,CAAC7B,SAAS,EAAEzH,QAAQ,CAAC;YAC1E,IAAI+hB,UAAU,IAAIA,UAAU,CAAC3b,KAAK,EAAE;cAChC2b,UAAU,CAAC3b,KAAK,GAAG+J,GAAG,CAACzG,mBAAmB,CAACqY,UAAU,CAAC3b,KAAK,EAAEnG,MAAM,CAAC;cACpEkQ,GAAG,CAACtG,iBAAiB,CAACiY,IAAI,CAACra,SAAS,EAAEzH,QAAQ,EAAE+hB,UAAU,CAAC;YAC/D,CAAC,MACI,IAAIta,SAAS,CAACzH,QAAQ,CAAC,EAAE;cAC1ByH,SAAS,CAACzH,QAAQ,CAAC,GAAGmQ,GAAG,CAACzG,mBAAmB,CAACjC,SAAS,CAACzH,QAAQ,CAAC,EAAEC,MAAM,CAAC;YAC9E;UACJ,CAAC,MACI,IAAIwH,SAAS,CAACzH,QAAQ,CAAC,EAAE;YAC1ByH,SAAS,CAACzH,QAAQ,CAAC,GAAGmQ,GAAG,CAACzG,mBAAmB,CAACjC,SAAS,CAACzH,QAAQ,CAAC,EAAEC,MAAM,CAAC;UAC9E;QACJ,CAAC,CACD,MAAM;UACF;UACA;QAAA;MAER,CAAC,CAAC;IACN;IACA,OAAO4hB,cAAc,CAAC/a,IAAI,CAACvD,MAAM,EAAE9F,IAAI,EAAEqkB,IAAI,EAAEpb,OAAO,CAAC;EAC3D,CAAC;EACDyJ,GAAG,CAACvG,qBAAqB,CAACrG,MAAM,CAACqe,MAAM,CAAC,EAAEC,cAAc,CAAC;AAC7D;AAEA,SAASG,SAASA,CAAClX,IAAI,EAAE;EACrBA,IAAI,CAAClM,YAAY,CAAC,MAAM,EAAE,CAACtB,MAAM,EAAEwN,IAAI,EAAEqF,GAAG,KAAK;IAC7C;IACA;IACA,MAAMiH,UAAU,GAAGW,eAAe,CAACza,MAAM,CAAC;IAC1C6S,GAAG,CAACtH,iBAAiB,GAAGA,iBAAiB;IACzCsH,GAAG,CAACrH,WAAW,GAAGA,WAAW;IAC7BqH,GAAG,CAACpH,aAAa,GAAGA,aAAa;IACjCoH,GAAG,CAAClH,cAAc,GAAGA,cAAc;IACnC;IACA;IACA;IACA;IACA;IACA,MAAMgZ,0BAA0B,GAAGnX,IAAI,CAACtN,UAAU,CAAC,qBAAqB,CAAC;IACzE,MAAM0kB,uBAAuB,GAAGpX,IAAI,CAACtN,UAAU,CAAC,kBAAkB,CAAC;IACnE,IAAIF,MAAM,CAAC4kB,uBAAuB,CAAC,EAAE;MACjC5kB,MAAM,CAAC2kB,0BAA0B,CAAC,GAAG3kB,MAAM,CAAC4kB,uBAAuB,CAAC;IACxE;IACA,IAAI5kB,MAAM,CAAC2kB,0BAA0B,CAAC,EAAE;MACpCnX,IAAI,CAACmX,0BAA0B,CAAC,GAAGnX,IAAI,CAACoX,uBAAuB,CAAC,GAC5D5kB,MAAM,CAAC2kB,0BAA0B,CAAC;IAC1C;IACA9R,GAAG,CAACjH,mBAAmB,GAAGA,mBAAmB;IAC7CiH,GAAG,CAACvH,gBAAgB,GAAGA,gBAAgB;IACvCuH,GAAG,CAAChH,UAAU,GAAGA,UAAU;IAC3BgH,GAAG,CAAC9G,oBAAoB,GAAGA,oBAAoB;IAC/C8G,GAAG,CAAC7G,8BAA8B,GAAGA,8BAA8B;IACnE6G,GAAG,CAAC5G,YAAY,GAAGA,YAAY;IAC/B4G,GAAG,CAAC3G,UAAU,GAAGA,UAAU;IAC3B2G,GAAG,CAAC1G,UAAU,GAAGA,UAAU;IAC3B0G,GAAG,CAACzG,mBAAmB,GAAGA,mBAAmB;IAC7CyG,GAAG,CAACxG,gBAAgB,GAAGA,gBAAgB;IACvCwG,GAAG,CAACvG,qBAAqB,GAAGA,qBAAqB;IACjDuG,GAAG,CAACtG,iBAAiB,GAAGrC,MAAM,CAACyC,cAAc;IAC7CkG,GAAG,CAACrG,cAAc,GAAGA,cAAc;IACnCqG,GAAG,CAAC/G,gBAAgB,GAAG,OAAO;MAC1BsG,aAAa;MACbD,oBAAoB;MACpB2H,UAAU;MACVlL,SAAS;MACTC,KAAK;MACLH,MAAM;MACNrB,QAAQ;MACRC,SAAS;MACTC,kBAAkB;MAClBN,sBAAsB;MACtBC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AAEA,SAAS2X,WAAWA,CAACrX,IAAI,EAAE;EACvBuQ,YAAY,CAACvQ,IAAI,CAAC;EAClBkW,aAAa,CAAClW,IAAI,CAAC;EACnBkX,SAAS,CAAClX,IAAI,CAAC;AACnB;AAEA,MAAMsX,MAAM,GAAGrY,QAAQ,CAAC,CAAC;AACzBoY,WAAW,CAACC,MAAM,CAAC;AACnB7J,YAAY,CAAC6J,MAAM,CAAC", "sources": ["./node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\nconst global = globalThis;\n// __Zone_symbol_prefix global can be used to override the default zone\n// symbol prefix with a custom one if needed.\nfunction __symbol__(name) {\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    return symbolPrefix + name;\n}\nfunction initZone() {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    class ZoneImpl {\n        // tslint:disable-next-line:require-internal-with-underscore\n        static { this.__symbol__ = __symbol__; }\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = ZoneImpl.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, ZoneImpl, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = (zoneSpec && zoneSpec.properties) || {};\n            this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            }\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && task._transitionTo(running, scheduled);\n            task.runCount++;\n            const previousTask = _currentTask;\n            _currentTask = task;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                if (task.state !== notScheduled && task.state !== unknown) {\n                    if (task.type == eventTask || (task.data && task.data.isPeriodic)) {\n                        reEntryGuard && task._transitionTo(scheduled, running);\n                    }\n                    else {\n                        task.runCount = 0;\n                        this._updateTaskCount(task, -1);\n                        reEntryGuard &&\n                            task._transitionTo(notScheduled, running, notScheduled);\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name +\n                    '; Execution: ' +\n                    this.name +\n                    ')');\n            if (task.state !== scheduled && task.state !== running) {\n                return;\n            }\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = 0;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task),\n    };\n    class _ZoneDelegate {\n        get zone() {\n            return this._zone;\n        }\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._taskCounts = {\n                'microTask': 0,\n                'macroTask': 0,\n                'eventTask': 0,\n            };\n            this._zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this._zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this._zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this._zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this._zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt =\n                zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this._zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this._zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this._zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = this._zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this._zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this._zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS\n                ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec)\n                : new ZoneImpl(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS\n                ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source)\n                : callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS\n                ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source)\n                : callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS\n                ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error)\n                : true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS\n                ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs)\n                : task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = (counts[type] = prev + count);\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type,\n                };\n                this.hasTask(this._zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zone = null;\n            this.runCount = 0;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zoneDelegates = null;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._state = 'notScheduled';\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? \" or '\" + fromState2 + \"'\" : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount,\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !ZoneImpl[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask,\n    };\n    let _currentZoneFrame = { parent: null, zone: new ZoneImpl(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return ZoneImpl;\n}\n\nfunction loadZone() {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    const global = globalThis;\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone'] && (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function')) {\n        throw new Error('Zone already loaded.');\n    }\n    // Initialize global `Zone` constant.\n    global['Zone'] ??= initZone();\n    return global['Zone'];\n}\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = __symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = __symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = __symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = __symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = (isWindowExists && internalWindow) || globalThis;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope;\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = !('nw' in _global) &&\n    typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    _global.process.toString() === '[object process]' &&\n    !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result =\n            listener &&\n                listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // issue #978, when onload handler was added before loading zone.js\n        // we should remove it with originalDescSet\n        originalDescSet && originalDescSet.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    },\n                });\n            }\n        })(prop);\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n            return true;\n        }\n    }\n    catch (error) { }\n    return false;\n}\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) { }\n    return ieOrEdge;\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n// Note that passive event listeners are now supported by most modern browsers,\n// including Chrome, Firefox, Safari, and Edge. There's a pending change that\n// would remove support for legacy browsers by zone.js. Removing `passiveSupported`\n// from the codebase will reduce the final code size for existing apps that still use zone.js.\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        const options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n            },\n        });\n        // Note: We pass the `options` object as the event handler too. This is not compatible with the\n        // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n        // without an actual handler.\n        window.addEventListener('test', options, options);\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true,\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // We use a shared global `taskData` to pass data for `scheduleEventTask`,\n        // eliminating the need to create a new object solely for passing data.\n        // WARNING: This object has a static lifetime, meaning it is not created\n        // each time `addEventListener` is called. It is instantiated only once\n        // and captured by reference inside the `addEventListener` and\n        // `removeEventListener` functions. Do not add any new properties to this\n        // object, as doing so would necessitate maintaining the information\n        // between `addEventListener` calls.\n        const taskData = {};\n        const nativeAddEventListener = (proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER]);\n        const nativeRemoveEventListener = (proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER]);\n        const nativeListeners = (proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER]);\n        const nativeRemoveAllListeners = (proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER]);\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passiveSupported && typeof options === 'object' && options) {\n                // doesn't support passive but user want to pass an object as options.\n                // this will not work on some old browser, so we just pass a boolean\n                // as useCapture parameter\n                return !!options.capture;\n            }\n            if (!passiveSupported || !passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return { ...options, passive: true };\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        /**\n         * In the context of events and listeners, this function will be\n         * called at the end by `cancelTask`, which, in turn, calls `task.cancelFn`.\n         * Cancelling a task is primarily used to remove event listeners from\n         * the task target.\n         */\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (task.removeAbortListener) {\n                                task.removeAbortListener();\n                                task.removeAbortListener = null;\n                            }\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return ((typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate));\n        };\n        const compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        function copyEventListenerOptions(options) {\n            if (typeof options === 'object' && options !== null) {\n                // We need to destructure the target `options` object since it may\n                // be frozen or sealed (possibly provided implicitly by a third-party\n                // library), or its properties may be readonly.\n                const newOptions = { ...options };\n                // The `signal` option was recently introduced, which caused regressions in\n                // third-party scenarios where `AbortController` was directly provided to\n                // `addEventListener` as options. For instance, in cases like\n                // `document.addEventListener('keydown', callback, abortControllerInstance)`,\n                // which is valid because `AbortController` includes a `signal` getter, spreading\n                // `{...options}` wouldn't copy the `signal`. Additionally, using `Object.create`\n                // isn't feasible since `AbortController` is a built-in object type, and attempting\n                // to create a new object directly with it as the prototype might result in\n                // unexpected behavior.\n                if (options.signal) {\n                    newOptions.signal = options.signal;\n                }\n                return newOptions;\n            }\n            return options;\n        }\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // don't create the bind delegate function for handleEvent\n                // case here to improve addEventListener performance\n                // we will create the bind delegate when invoke\n                let isHandleEvent = false;\n                if (typeof delegate !== 'function') {\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isHandleEvent = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = copyEventListenerOptions(buildEventListenerOptions(arguments[2], passive));\n                const signal = options?.signal;\n                if (signal?.aborted) {\n                    // the signal is an aborted one, just return without attaching the event listener.\n                    return;\n                }\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source =\n                        constructorName +\n                            addSource +\n                            (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // In the code below, `options` should no longer be reassigned; instead, it\n                // should only be mutated. This is because we pass that object to the native\n                // `addEventListener`.\n                // It's generally recommended to use the same object reference for options.\n                // This ensures consistency and avoids potential issues.\n                taskData.options = options;\n                if (once) {\n                    // When using `addEventListener` with the `once` option, we don't pass\n                    // the `once` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `once` setting and handle it ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                if (signal) {\n                    // When using `addEventListener` with the `signal` option, we don't pass\n                    // the `signal` option directly to the native `addEventListener` method.\n                    // Instead, we keep the `signal` setting and handle it ourselves.\n                    taskData.options.signal = undefined;\n                }\n                // The `scheduleEventTask` function will ultimately call `customScheduleGlobal`,\n                // which in turn calls the native `addEventListener`. This is why `taskData.options`\n                // is updated before scheduling the task, as `customScheduleGlobal` uses\n                // `taskData.options` to pass it to the native `addEventListener`.\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                if (signal) {\n                    // after task is scheduled, we need to store the signal back to task.options\n                    taskData.options.signal = signal;\n                    // Wrapping `task` in a weak reference would not prevent memory leaks. Weak references are\n                    // primarily used for preventing strong references cycles. `onAbort` is always reachable\n                    // as it's an event listener, so its closure retains a strong reference to the `task`.\n                    const onAbort = () => task.zone.cancelTask(task);\n                    nativeListener.call(signal, 'abort', onAbort, { once: true });\n                    // We need to remove the `abort` listener when the event listener is going to be removed,\n                    // as it creates a closure that captures `task`. This closure retains a reference to the\n                    // `task` object even after it goes out of scope, preventing `task` from being garbage\n                    // collected.\n                    task.removeAbortListener = () => signal.removeEventListener('abort', onAbort);\n                }\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    taskData.options.once = true;\n                }\n                if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                    // if not support passive, and we pass an option object\n                    // to addEventListener, we should save the options to task\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isHandleEvent) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            // `existingTasks` may not exist if the `addEventListener` was called before\n            // it was patched by zone.js. Please refer to the attached issue for\n            // clarification, particularly after the `if` condition, before calling\n            // the native `removeEventListener`.\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates with capture=false were removed\n                            // https:// github.com/angular/angular/issues/31643\n                            // https://github.com/angular/angular/issues/54581\n                            if (!capture && typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        // In all other conditions, when `addEventListener` is called after being\n                        // patched by zone.js, we would always find an event task on the `EventTarget`.\n                        // This will trigger `cancelFn` on the `existingTask`, leading to `customCancelGlobal`,\n                        // which ultimately removes an event listener and cleans up the abort listener\n                        // (if an `AbortSignal` was provided when scheduling a task).\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // https://github.com/angular/zone.js/issues/930\n            // We may encounter a situation where the `addEventListener` was\n            // called on the event target before zone.js is loaded, resulting\n            // in no task being stored on the event target due to its invocation\n            // of the native implementation. In this scenario, we simply need to\n            // invoke the native `removeEventListener`.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks\n            ? captureFalseTasks.concat(captureTrueTasks)\n            : captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchQueueMicrotask(global, api) {\n    api.patchMethod(global, 'queueMicrotask', (delegate) => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        data.handleId = setNative.apply(window, data.args);\n        return task;\n    }\n    function clearTask(task) {\n        return clearNative.call(window, task.data.handleId);\n    }\n    setNative = patchMethod(window, setName, (delegate) => function (self, args) {\n        if (typeof args[0] === 'function') {\n            const options = {\n                isPeriodic: nameSuffix === 'Interval',\n                delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n                args: args,\n            };\n            const callback = args[0];\n            args[0] = function timer() {\n                try {\n                    return callback.apply(this, arguments);\n                }\n                finally {\n                    // issue-934, task will be cancelled\n                    // even it is a periodic task such as\n                    // setInterval\n                    // https://github.com/angular/angular/issues/40387\n                    // Cleanup tasksByHandleId should be handled before scheduleTask\n                    // Since some zoneSpec may intercept and doesn't trigger\n                    // scheduleFn(scheduleTask) provided here.\n                    if (!options.isPeriodic) {\n                        if (typeof options.handleId === 'number') {\n                            // in non-nodejs env, we remove timerId\n                            // from local cache\n                            delete tasksByHandleId[options.handleId];\n                        }\n                        else if (options.handleId) {\n                            // Node returns complex objects as handleIds\n                            // we remove task reference from timer object\n                            options.handleId[taskSymbol] = null;\n                        }\n                    }\n                }\n            };\n            const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n            if (!task) {\n                return task;\n            }\n            // Node.js must additionally support the ref and unref functions.\n            const handle = task.data.handleId;\n            if (typeof handle === 'number') {\n                // for non nodejs env, we save handleId: task\n                // mapping in local cache for clearTimeout\n                tasksByHandleId[handle] = task;\n            }\n            else if (handle) {\n                // for nodejs env, we save task\n                // reference in timerId Object for clearTimeout\n                handle[taskSymbol] = task;\n            }\n            // check whether handle is null, because some polyfill or browser\n            // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n            if (handle &&\n                handle.ref &&\n                handle.unref &&\n                typeof handle.ref === 'function' &&\n                typeof handle.unref === 'function') {\n                task.ref = handle.ref.bind(handle);\n                task.unref = handle.unref.bind(handle);\n            }\n            if (typeof handle === 'number' || handle) {\n                return handle;\n            }\n            return task;\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(window, args);\n        }\n    });\n    clearNative = patchMethod(window, cancelName, (delegate) => function (self, args) {\n        const id = args[0];\n        let task;\n        if (typeof id === 'number') {\n            // non nodejs env.\n            task = tasksByHandleId[id];\n        }\n        else {\n            // nodejs env.\n            task = id && id[taskSymbol];\n            // other environments.\n            if (!task) {\n                task = id;\n            }\n        }\n        if (task && typeof task.type === 'string') {\n            if (task.state !== 'notScheduled' &&\n                ((task.cancelFn && task.data.isPeriodic) || task.runCount === 0)) {\n                if (typeof id === 'number') {\n                    delete tasksByHandleId[id];\n                }\n                else if (id) {\n                    id[taskSymbol] = null;\n                }\n                // Do not cancel already canceled functions\n                task.zone.cancelTask(task);\n            }\n        }\n        else {\n            // cause an error by calling it directly.\n            delegate.apply(window, args);\n        }\n    });\n}\n\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    // https://html.spec.whatwg.org/multipage/custom-elements.html#concept-custom-element-definition-lifecycle-callbacks\n    const callbacks = [\n        'connectedCallback',\n        'disconnectedCallback',\n        'adoptedCallback',\n        'attributeChangedCallback',\n        'formAssociatedCallback',\n        'formDisabledCallback',\n        'formResetCallback',\n        'formStateRestoreCallback',\n    ];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter((ip) => ip.target === target);\n    if (!tip || tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter((op) => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter((name) => name.startsWith('on') && name.length > 2)\n        .map((name) => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document',\n            'SVGElement',\n            'Element',\n            'HTMLElement',\n            'HTMLBodyElement',\n            'HTMLMediaElement',\n            'HTMLFrameSetElement',\n            'HTMLFrameElement',\n            'HTMLIFrameElement',\n            'HTMLMarqueeElement',\n            'Worker',\n        ]);\n        const ignoreErrorProperties = isIE()\n            ? [{ target: internalWindow, ignoreProperties: ['error'] }]\n            : [];\n        // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n        // so we need to pass WindowPrototype to check onProp exist or not\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest',\n        'XMLHttpRequestEventTarget',\n        'IDBIndex',\n        'IDBRequest',\n        'IDBOpenDBRequest',\n        'IDBDatabase',\n        'IDBTransaction',\n        'IDBCursor',\n        'WebSocket',\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target &&\n            target.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nfunction patchBrowser(Zone) {\n    Zone.__load_patch('legacy', (global) => {\n        const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n        if (legacyPatch) {\n            legacyPatch();\n        }\n    });\n    Zone.__load_patch('timers', (global) => {\n        const set = 'set';\n        const clear = 'clear';\n        patchTimer(global, set, clear, 'Timeout');\n        patchTimer(global, set, clear, 'Interval');\n        patchTimer(global, set, clear, 'Immediate');\n    });\n    Zone.__load_patch('requestAnimationFrame', (global) => {\n        patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n        patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n        patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n    });\n    Zone.__load_patch('blocking', (global, Zone) => {\n        const blockingMethods = ['alert', 'prompt', 'confirm'];\n        for (let i = 0; i < blockingMethods.length; i++) {\n            const name = blockingMethods[i];\n            patchMethod(global, name, (delegate, symbol, name) => {\n                return function (s, args) {\n                    return Zone.current.run(delegate, global, args, name);\n                };\n            });\n        }\n    });\n    Zone.__load_patch('EventTarget', (global, Zone, api) => {\n        patchEvent(global, api);\n        eventTargetPatch(global, api);\n        // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n        const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n        if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n            api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n        }\n    });\n    Zone.__load_patch('MutationObserver', (global, Zone, api) => {\n        patchClass('MutationObserver');\n        patchClass('WebKitMutationObserver');\n    });\n    Zone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n        patchClass('IntersectionObserver');\n    });\n    Zone.__load_patch('FileReader', (global, Zone, api) => {\n        patchClass('FileReader');\n    });\n    Zone.__load_patch('on_property', (global, Zone, api) => {\n        propertyDescriptorPatch(api, global);\n    });\n    Zone.__load_patch('customElements', (global, Zone, api) => {\n        patchCustomElements(global, api);\n    });\n    Zone.__load_patch('XHR', (global, Zone) => {\n        // Treat XMLHttpRequest as a macrotask.\n        patchXHR(global);\n        const XHR_TASK = zoneSymbol('xhrTask');\n        const XHR_SYNC = zoneSymbol('xhrSync');\n        const XHR_LISTENER = zoneSymbol('xhrListener');\n        const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n        const XHR_URL = zoneSymbol('xhrURL');\n        const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n        function patchXHR(window) {\n            const XMLHttpRequest = window['XMLHttpRequest'];\n            if (!XMLHttpRequest) {\n                // XMLHttpRequest is not available in service worker\n                return;\n            }\n            const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n            function findPendingTask(target) {\n                return target[XHR_TASK];\n            }\n            let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n            let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            if (!oriAddListener) {\n                const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n                if (XMLHttpRequestEventTarget) {\n                    const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                    oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n            }\n            const READY_STATE_CHANGE = 'readystatechange';\n            const SCHEDULED = 'scheduled';\n            function scheduleTask(task) {\n                const data = task.data;\n                const target = data.target;\n                target[XHR_SCHEDULED] = false;\n                target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n                // remove existing event listener\n                const listener = target[XHR_LISTENER];\n                if (!oriAddListener) {\n                    oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                    oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n                }\n                if (listener) {\n                    oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n                }\n                const newListener = (target[XHR_LISTENER] = () => {\n                    if (target.readyState === target.DONE) {\n                        // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                        // readyState=4 multiple times, so we need to check task state here\n                        if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                            // check whether the xhr has registered onload listener\n                            // if that is the case, the task should invoke after all\n                            // onload listeners finish.\n                            // Also if the request failed without response (status = 0), the load event handler\n                            // will not be triggered, in that case, we should also invoke the placeholder callback\n                            // to close the XMLHttpRequest::send macroTask.\n                            // https://github.com/angular/angular/issues/38795\n                            const loadTasks = target[Zone.__symbol__('loadfalse')];\n                            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                                const oriInvoke = task.invoke;\n                                task.invoke = function () {\n                                    // need to load the tasks again, because in other\n                                    // load listener, they may remove themselves\n                                    const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                    for (let i = 0; i < loadTasks.length; i++) {\n                                        if (loadTasks[i] === task) {\n                                            loadTasks.splice(i, 1);\n                                        }\n                                    }\n                                    if (!data.aborted && task.state === SCHEDULED) {\n                                        oriInvoke.call(task);\n                                    }\n                                };\n                                loadTasks.push(task);\n                            }\n                            else {\n                                task.invoke();\n                            }\n                        }\n                        else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                            // error occurs when xhr.send()\n                            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                        }\n                    }\n                });\n                oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n                const storedTask = target[XHR_TASK];\n                if (!storedTask) {\n                    target[XHR_TASK] = task;\n                }\n                sendNative.apply(target, data.args);\n                target[XHR_SCHEDULED] = true;\n                return task;\n            }\n            function placeholderCallback() { }\n            function clearTask(task) {\n                const data = task.data;\n                // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n                // to prevent it from firing. So instead, we store info for the event listener.\n                data.aborted = true;\n                return abortNative.apply(data.target, data.args);\n            }\n            const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n                self[XHR_SYNC] = args[2] == false;\n                self[XHR_URL] = args[1];\n                return openNative.apply(self, args);\n            });\n            const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n            const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n            const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n            const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n                if (Zone.current[fetchTaskScheduling] === true) {\n                    // a fetch is scheduling, so we are using xhr to polyfill fetch\n                    // and because we already schedule macroTask for fetch, we should\n                    // not schedule a macroTask for xhr again\n                    return sendNative.apply(self, args);\n                }\n                if (self[XHR_SYNC]) {\n                    // if the XHR is sync there is no task to schedule, just execute the code.\n                    return sendNative.apply(self, args);\n                }\n                else {\n                    const options = {\n                        target: self,\n                        url: self[XHR_URL],\n                        isPeriodic: false,\n                        args: args,\n                        aborted: false,\n                    };\n                    const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                    if (self &&\n                        self[XHR_ERROR_BEFORE_SCHEDULED] === true &&\n                        !options.aborted &&\n                        task.state === SCHEDULED) {\n                        // xhr request throw error when send\n                        // we should invoke task instead of leaving a scheduled\n                        // pending macroTask\n                        task.invoke();\n                    }\n                }\n            });\n            const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n                const task = findPendingTask(self);\n                if (task && typeof task.type == 'string') {\n                    // If the XHR has already completed, do nothing.\n                    // If the XHR has already been aborted, do nothing.\n                    // Fix #569, call abort multiple times before done will cause\n                    // macroTask task count be negative number\n                    if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                        return;\n                    }\n                    task.zone.cancelTask(task);\n                }\n                else if (Zone.current[fetchTaskAborting] === true) {\n                    // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                    return abortNative.apply(self, args);\n                }\n                // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n                // task\n                // to cancel. Do nothing.\n            });\n        }\n    });\n    Zone.__load_patch('geolocation', (global) => {\n        /// GEO_LOCATION\n        if (global['navigator'] && global['navigator'].geolocation) {\n            patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n        }\n    });\n    Zone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n        // handle unhandled promise rejection\n        function findPromiseRejectionHandler(evtName) {\n            return function (e) {\n                const eventTasks = findEventTasks(global, evtName);\n                eventTasks.forEach((eventTask) => {\n                    // windows has added unhandledrejection event listener\n                    // trigger the event listener\n                    const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                    if (PromiseRejectionEvent) {\n                        const evt = new PromiseRejectionEvent(evtName, {\n                            promise: e.promise,\n                            reason: e.rejection,\n                        });\n                        eventTask.invoke(evt);\n                    }\n                });\n            };\n        }\n        if (global['PromiseRejectionEvent']) {\n            Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n                findPromiseRejectionHandler('unhandledrejection');\n            Zone[zoneSymbol('rejectionHandledHandler')] =\n                findPromiseRejectionHandler('rejectionhandled');\n        }\n    });\n    Zone.__load_patch('queueMicrotask', (global, Zone, api) => {\n        patchQueueMicrotask(global, api);\n    });\n}\n\nfunction patchPromise(Zone) {\n    Zone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n        const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n        const ObjectDefineProperty = Object.defineProperty;\n        function readableObjectToString(obj) {\n            if (obj && obj.toString === Object.prototype.toString) {\n                const className = obj.constructor && obj.constructor.name;\n                return (className ? className : '') + ': ' + JSON.stringify(obj);\n            }\n            return obj ? obj.toString() : Object.prototype.toString.call(obj);\n        }\n        const __symbol__ = api.symbol;\n        const _uncaughtPromiseErrors = [];\n        const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] !== false;\n        const symbolPromise = __symbol__('Promise');\n        const symbolThen = __symbol__('then');\n        const creationTrace = '__creationTrace__';\n        api.onUnhandledError = (e) => {\n            if (api.showUncaughtError()) {\n                const rejection = e && e.rejection;\n                if (rejection) {\n                    console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n                }\n                else {\n                    console.error(e);\n                }\n            }\n        };\n        api.microtaskDrainDone = () => {\n            while (_uncaughtPromiseErrors.length) {\n                const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n                try {\n                    uncaughtPromiseError.zone.runGuarded(() => {\n                        if (uncaughtPromiseError.throwOriginal) {\n                            throw uncaughtPromiseError.rejection;\n                        }\n                        throw uncaughtPromiseError;\n                    });\n                }\n                catch (error) {\n                    handleUnhandledRejection(error);\n                }\n            }\n        };\n        const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n        function handleUnhandledRejection(e) {\n            api.onUnhandledError(e);\n            try {\n                const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n                if (typeof handler === 'function') {\n                    handler.call(this, e);\n                }\n            }\n            catch (err) { }\n        }\n        function isThenable(value) {\n            return value && value.then;\n        }\n        function forwardResolution(value) {\n            return value;\n        }\n        function forwardRejection(rejection) {\n            return ZoneAwarePromise.reject(rejection);\n        }\n        const symbolState = __symbol__('state');\n        const symbolValue = __symbol__('value');\n        const symbolFinally = __symbol__('finally');\n        const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n        const symbolParentPromiseState = __symbol__('parentPromiseState');\n        const source = 'Promise.then';\n        const UNRESOLVED = null;\n        const RESOLVED = true;\n        const REJECTED = false;\n        const REJECTED_NO_CATCH = 0;\n        function makeResolver(promise, state) {\n            return (v) => {\n                try {\n                    resolvePromise(promise, state, v);\n                }\n                catch (err) {\n                    resolvePromise(promise, false, err);\n                }\n                // Do not return value or you will break the Promise spec.\n            };\n        }\n        const once = function () {\n            let wasCalled = false;\n            return function wrapper(wrappedFunction) {\n                return function () {\n                    if (wasCalled) {\n                        return;\n                    }\n                    wasCalled = true;\n                    wrappedFunction.apply(null, arguments);\n                };\n            };\n        };\n        const TYPE_ERROR = 'Promise resolved with itself';\n        const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n        // Promise Resolution\n        function resolvePromise(promise, state, value) {\n            const onceWrapper = once();\n            if (promise === value) {\n                throw new TypeError(TYPE_ERROR);\n            }\n            if (promise[symbolState] === UNRESOLVED) {\n                // should only get value.then once based on promise spec.\n                let then = null;\n                try {\n                    if (typeof value === 'object' || typeof value === 'function') {\n                        then = value && value.then;\n                    }\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                    return promise;\n                }\n                // if (value instanceof ZoneAwarePromise) {\n                if (state !== REJECTED &&\n                    value instanceof ZoneAwarePromise &&\n                    value.hasOwnProperty(symbolState) &&\n                    value.hasOwnProperty(symbolValue) &&\n                    value[symbolState] !== UNRESOLVED) {\n                    clearRejectedNoCatch(value);\n                    resolvePromise(promise, value[symbolState], value[symbolValue]);\n                }\n                else if (state !== REJECTED && typeof then === 'function') {\n                    try {\n                        then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                    }\n                    catch (err) {\n                        onceWrapper(() => {\n                            resolvePromise(promise, false, err);\n                        })();\n                    }\n                }\n                else {\n                    promise[symbolState] = state;\n                    const queue = promise[symbolValue];\n                    promise[symbolValue] = value;\n                    if (promise[symbolFinally] === symbolFinally) {\n                        // the promise is generated by Promise.prototype.finally\n                        if (state === RESOLVED) {\n                            // the state is resolved, should ignore the value\n                            // and use parent promise value\n                            promise[symbolState] = promise[symbolParentPromiseState];\n                            promise[symbolValue] = promise[symbolParentPromiseValue];\n                        }\n                    }\n                    // record task information in value when error occurs, so we can\n                    // do some additional work such as render longStackTrace\n                    if (state === REJECTED && value instanceof Error) {\n                        // check if longStackTraceZone is here\n                        const trace = Zone.currentTask &&\n                            Zone.currentTask.data &&\n                            Zone.currentTask.data[creationTrace];\n                        if (trace) {\n                            // only keep the long stack trace into error when in longStackTraceZone\n                            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n                                configurable: true,\n                                enumerable: false,\n                                writable: true,\n                                value: trace,\n                            });\n                        }\n                    }\n                    for (let i = 0; i < queue.length;) {\n                        scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                    }\n                    if (queue.length == 0 && state == REJECTED) {\n                        promise[symbolState] = REJECTED_NO_CATCH;\n                        let uncaughtPromiseError = value;\n                        try {\n                            // Here we throws a new Error to print more readable error log\n                            // and if the value is not an error, zone.js builds an `Error`\n                            // Object here to attach the stack information.\n                            throw new Error('Uncaught (in promise): ' +\n                                readableObjectToString(value) +\n                                (value && value.stack ? '\\n' + value.stack : ''));\n                        }\n                        catch (err) {\n                            uncaughtPromiseError = err;\n                        }\n                        if (isDisableWrappingUncaughtPromiseRejection) {\n                            // If disable wrapping uncaught promise reject\n                            // use the value instead of wrapping it.\n                            uncaughtPromiseError.throwOriginal = true;\n                        }\n                        uncaughtPromiseError.rejection = value;\n                        uncaughtPromiseError.promise = promise;\n                        uncaughtPromiseError.zone = Zone.current;\n                        uncaughtPromiseError.task = Zone.currentTask;\n                        _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                        api.scheduleMicroTask(); // to make sure that it is running\n                    }\n                }\n            }\n            // Resolving an already resolved promise is a noop.\n            return promise;\n        }\n        const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n        function clearRejectedNoCatch(promise) {\n            if (promise[symbolState] === REJECTED_NO_CATCH) {\n                // if the promise is rejected no catch status\n                // and queue.length > 0, means there is a error handler\n                // here to handle the rejected promise, we should trigger\n                // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n                // eventHandler\n                try {\n                    const handler = Zone[REJECTION_HANDLED_HANDLER];\n                    if (handler && typeof handler === 'function') {\n                        handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                    }\n                }\n                catch (err) { }\n                promise[symbolState] = REJECTED;\n                for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                    if (promise === _uncaughtPromiseErrors[i].promise) {\n                        _uncaughtPromiseErrors.splice(i, 1);\n                    }\n                }\n            }\n        }\n        function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n            clearRejectedNoCatch(promise);\n            const promiseState = promise[symbolState];\n            const delegate = promiseState\n                ? typeof onFulfilled === 'function'\n                    ? onFulfilled\n                    : forwardResolution\n                : typeof onRejected === 'function'\n                    ? onRejected\n                    : forwardRejection;\n            zone.scheduleMicroTask(source, () => {\n                try {\n                    const parentPromiseValue = promise[symbolValue];\n                    const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                    if (isFinallyPromise) {\n                        // if the promise is generated from finally call, keep parent promise's state and value\n                        chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                        chainPromise[symbolParentPromiseState] = promiseState;\n                    }\n                    // should not pass value to finally callback\n                    const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution\n                        ? []\n                        : [parentPromiseValue]);\n                    resolvePromise(chainPromise, true, value);\n                }\n                catch (error) {\n                    // if error occurs, should always return this error\n                    resolvePromise(chainPromise, false, error);\n                }\n            }, chainPromise);\n        }\n        const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n        const noop = function () { };\n        const AggregateError = global.AggregateError;\n        class ZoneAwarePromise {\n            static toString() {\n                return ZONE_AWARE_PROMISE_TO_STRING;\n            }\n            static resolve(value) {\n                if (value instanceof ZoneAwarePromise) {\n                    return value;\n                }\n                return resolvePromise(new this(null), RESOLVED, value);\n            }\n            static reject(error) {\n                return resolvePromise(new this(null), REJECTED, error);\n            }\n            static withResolvers() {\n                const result = {};\n                result.promise = new ZoneAwarePromise((res, rej) => {\n                    result.resolve = res;\n                    result.reject = rej;\n                });\n                return result;\n            }\n            static any(values) {\n                if (!values || typeof values[Symbol.iterator] !== 'function') {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                const promises = [];\n                let count = 0;\n                try {\n                    for (let v of values) {\n                        count++;\n                        promises.push(ZoneAwarePromise.resolve(v));\n                    }\n                }\n                catch (err) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                if (count === 0) {\n                    return Promise.reject(new AggregateError([], 'All promises were rejected'));\n                }\n                let finished = false;\n                const errors = [];\n                return new ZoneAwarePromise((resolve, reject) => {\n                    for (let i = 0; i < promises.length; i++) {\n                        promises[i].then((v) => {\n                            if (finished) {\n                                return;\n                            }\n                            finished = true;\n                            resolve(v);\n                        }, (err) => {\n                            errors.push(err);\n                            count--;\n                            if (count === 0) {\n                                finished = true;\n                                reject(new AggregateError(errors, 'All promises were rejected'));\n                            }\n                        });\n                    }\n                });\n            }\n            static race(values) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                function onResolve(value) {\n                    resolve(value);\n                }\n                function onReject(error) {\n                    reject(error);\n                }\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    value.then(onResolve, onReject);\n                }\n                return promise;\n            }\n            static all(values) {\n                return ZoneAwarePromise.allWithCallback(values);\n            }\n            static allSettled(values) {\n                const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n                return P.allWithCallback(values, {\n                    thenCallback: (value) => ({ status: 'fulfilled', value }),\n                    errorCallback: (err) => ({ status: 'rejected', reason: err }),\n                });\n            }\n            static allWithCallback(values, callback) {\n                let resolve;\n                let reject;\n                let promise = new this((res, rej) => {\n                    resolve = res;\n                    reject = rej;\n                });\n                // Start at 2 to prevent prematurely resolving if .then is called immediately.\n                let unresolvedCount = 2;\n                let valueIndex = 0;\n                const resolvedValues = [];\n                for (let value of values) {\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    const curValueIndex = valueIndex;\n                    try {\n                        value.then((value) => {\n                            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }, (err) => {\n                            if (!callback) {\n                                reject(err);\n                            }\n                            else {\n                                resolvedValues[curValueIndex] = callback.errorCallback(err);\n                                unresolvedCount--;\n                                if (unresolvedCount === 0) {\n                                    resolve(resolvedValues);\n                                }\n                            }\n                        });\n                    }\n                    catch (thenErr) {\n                        reject(thenErr);\n                    }\n                    unresolvedCount++;\n                    valueIndex++;\n                }\n                // Make the unresolvedCount zero-based again.\n                unresolvedCount -= 2;\n                if (unresolvedCount === 0) {\n                    resolve(resolvedValues);\n                }\n                return promise;\n            }\n            constructor(executor) {\n                const promise = this;\n                if (!(promise instanceof ZoneAwarePromise)) {\n                    throw new Error('Must be an instanceof Promise.');\n                }\n                promise[symbolState] = UNRESOLVED;\n                promise[symbolValue] = []; // queue;\n                try {\n                    const onceWrapper = once();\n                    executor &&\n                        executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n                }\n                catch (error) {\n                    resolvePromise(promise, false, error);\n                }\n            }\n            get [Symbol.toStringTag]() {\n                return 'Promise';\n            }\n            get [Symbol.species]() {\n                return ZoneAwarePromise;\n            }\n            then(onFulfilled, onRejected) {\n                // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n                // may be an object without a prototype (created through `Object.create(null)`); thus\n                // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n                // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n                // object and copies promise properties into that object (within the `getOrCreateLoad`\n                // function). The zone.js then checks if the resolved value has the `then` method and\n                // invokes it with the `value` context. Otherwise, this will throw an error: `TypeError:\n                // Cannot read properties of undefined (reading 'Symbol(Symbol.species)')`.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = this.constructor || ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n                }\n                return chainPromise;\n            }\n            catch(onRejected) {\n                return this.then(null, onRejected);\n            }\n            finally(onFinally) {\n                // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n                let C = this.constructor?.[Symbol.species];\n                if (!C || typeof C !== 'function') {\n                    C = ZoneAwarePromise;\n                }\n                const chainPromise = new C(noop);\n                chainPromise[symbolFinally] = symbolFinally;\n                const zone = Zone.current;\n                if (this[symbolState] == UNRESOLVED) {\n                    this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n                }\n                else {\n                    scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n                }\n                return chainPromise;\n            }\n        }\n        // Protect against aggressive optimizers dropping seemingly unused properties.\n        // E.g. Closure Compiler in advanced mode.\n        ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n        ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n        ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n        ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n        const NativePromise = (global[symbolPromise] = global['Promise']);\n        global['Promise'] = ZoneAwarePromise;\n        const symbolThenPatched = __symbol__('thenPatched');\n        function patchThen(Ctor) {\n            const proto = Ctor.prototype;\n            const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n            if (prop && (prop.writable === false || !prop.configurable)) {\n                // check Ctor.prototype.then propertyDescriptor is writable or not\n                // in meteor env, writable is false, we should ignore such case\n                return;\n            }\n            const originalThen = proto.then;\n            // Keep a reference to the original method.\n            proto[symbolThen] = originalThen;\n            Ctor.prototype.then = function (onResolve, onReject) {\n                const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                    originalThen.call(this, resolve, reject);\n                });\n                return wrapped.then(onResolve, onReject);\n            };\n            Ctor[symbolThenPatched] = true;\n        }\n        api.patchThen = patchThen;\n        function zoneify(fn) {\n            return function (self, args) {\n                let resultPromise = fn.apply(self, args);\n                if (resultPromise instanceof ZoneAwarePromise) {\n                    return resultPromise;\n                }\n                let ctor = resultPromise.constructor;\n                if (!ctor[symbolThenPatched]) {\n                    patchThen(ctor);\n                }\n                return resultPromise;\n            };\n        }\n        if (NativePromise) {\n            patchThen(NativePromise);\n            patchMethod(global, 'fetch', (delegate) => zoneify(delegate));\n        }\n        // This is not part of public API, but it is useful for tests, so we expose it.\n        Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n        return ZoneAwarePromise;\n    });\n}\n\nfunction patchToString(Zone) {\n    // override Function.prototype.toString to make zone.js patched function\n    // look like native function\n    Zone.__load_patch('toString', (global) => {\n        // patch Func.prototype.toString to let them look like native\n        const originalFunctionToString = Function.prototype.toString;\n        const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n        const PROMISE_SYMBOL = zoneSymbol('Promise');\n        const ERROR_SYMBOL = zoneSymbol('Error');\n        const newFunctionToString = function toString() {\n            if (typeof this === 'function') {\n                const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n                if (originalDelegate) {\n                    if (typeof originalDelegate === 'function') {\n                        return originalFunctionToString.call(originalDelegate);\n                    }\n                    else {\n                        return Object.prototype.toString.call(originalDelegate);\n                    }\n                }\n                if (this === Promise) {\n                    const nativePromise = global[PROMISE_SYMBOL];\n                    if (nativePromise) {\n                        return originalFunctionToString.call(nativePromise);\n                    }\n                }\n                if (this === Error) {\n                    const nativeError = global[ERROR_SYMBOL];\n                    if (nativeError) {\n                        return originalFunctionToString.call(nativeError);\n                    }\n                }\n            }\n            return originalFunctionToString.call(this);\n        };\n        newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n        Function.prototype.toString = newFunctionToString;\n        // patch Object.prototype.toString to let them look like native\n        const originalObjectToString = Object.prototype.toString;\n        const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n        Object.prototype.toString = function () {\n            if (typeof Promise === 'function' && this instanceof Promise) {\n                return PROMISE_OBJECT_TO_STRING;\n            }\n            return originalObjectToString.call(this);\n        };\n    });\n}\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = (target[symbol] = target[method]);\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\nfunction patchUtil(Zone) {\n    Zone.__load_patch('util', (global, Zone, api) => {\n        // Collect native event names by looking at properties\n        // on the global namespace, e.g. 'onclick'.\n        const eventNames = getOnEventNames(global);\n        api.patchOnProperties = patchOnProperties;\n        api.patchMethod = patchMethod;\n        api.bindArguments = bindArguments;\n        api.patchMacroTask = patchMacroTask;\n        // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS`\n        // to define which events will not be patched by `Zone.js`. In newer version (>=0.9.0), we\n        // change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep the name consistent with\n        // angular repo. The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be\n        // supported for backwards compatibility.\n        const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n        const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n        if (global[SYMBOL_UNPATCHED_EVENTS]) {\n            global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n        }\n        if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n            Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n                global[SYMBOL_BLACK_LISTED_EVENTS];\n        }\n        api.patchEventPrototype = patchEventPrototype;\n        api.patchEventTarget = patchEventTarget;\n        api.isIEOrEdge = isIEOrEdge;\n        api.ObjectDefineProperty = ObjectDefineProperty;\n        api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n        api.ObjectCreate = ObjectCreate;\n        api.ArraySlice = ArraySlice;\n        api.patchClass = patchClass;\n        api.wrapWithCurrentZone = wrapWithCurrentZone;\n        api.filterProperties = filterProperties;\n        api.attachOriginToPatched = attachOriginToPatched;\n        api._redefineProperty = Object.defineProperty;\n        api.patchCallbacks = patchCallbacks;\n        api.getGlobalObjects = () => ({\n            globalSources,\n            zoneSymbolEventNames,\n            eventNames,\n            isBrowser,\n            isMix,\n            isNode,\n            TRUE_STR,\n            FALSE_STR,\n            ZONE_SYMBOL_PREFIX,\n            ADD_EVENT_LISTENER_STR,\n            REMOVE_EVENT_LISTENER_STR,\n        });\n    });\n}\n\nfunction patchCommon(Zone) {\n    patchPromise(Zone);\n    patchToString(Zone);\n    patchUtil(Zone);\n}\n\nconst Zone$1 = loadZone();\npatchCommon(Zone$1);\npatchBrowser(Zone$1);\n"], "names": ["global", "globalThis", "__symbol__", "name", "symbolPrefix", "initZone", "performance", "mark", "performanceMeasure", "label", "ZoneImpl", "_", "assertZonePatched", "patches", "Error", "root", "zone", "current", "parent", "_currentZoneFrame", "currentTask", "_currentTask", "__load_patch", "fn", "ignoreDuplicate", "hasOwnProperty", "checkDuplicate", "perfName", "_api", "_parent", "_name", "constructor", "zoneSpec", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "get", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "arguments", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "state", "notScheduled", "type", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "runCount", "previousTask", "data", "isPeriodic", "cancelFn", "undefined", "invokeTask", "unknown", "_updateTaskCount", "scheduleTask", "newZone", "scheduling", "zoneDelegates", "_zoneDelegates", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "count", "i", "length", "DELEGATE_ZS", "onHasTask", "delegate", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_taskCounts", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "parentHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "prev", "next", "change", "options", "_state", "self", "useG", "call", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "Object", "prototype", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeMicroTaskQueuePromise", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "loadZone", "getOwnPropertyDescriptor", "defineProperty", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "Array", "slice", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "Zone", "scheduleMacroTaskWithCurrentZone", "zoneSymbol", "isWindowExists", "window", "internalWindow", "_global", "REMOVE_ATTRIBUTE", "patchPrototype", "fnNames", "prototypeDesc", "isPropertyWritable", "patched", "propertyDesc", "writable", "set", "isWebWorker", "WorkerGlobalScope", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames$1", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "message", "filename", "lineno", "colno", "preventDefault", "patchProperty", "obj", "prop", "desc", "enumerable", "configurable", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "previousValue", "removeEventListener", "addEventListener", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "original", "isDetectedIEOrEdge", "ieOrEdge", "isIE", "ua", "navigator", "userAgent", "indexOf", "passiveSupported", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "api", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "PREPEND_EVENT_LISTENER", "PREPEND_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "originalDelegate", "once", "globalCallback", "context", "isCapture", "tasks", "errors", "copyTasks", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "prepend", "buildEventListenerOptions", "passive", "capture", "customScheduleGlobal", "isExisting", "customCancelGlobal", "symbolEventNames", "symbolEventName", "existingTasks", "existingTask", "splice", "removeAbortListener", "allRemoved", "customScheduleNonGlobal", "customSchedulePrepend", "customCancelNonGlobal", "compareTaskCallbackVsDelegate", "typeOfDelegate", "compare", "diff", "unpatchedEvents", "passiveEvents", "copyEventListenerOptions", "newOptions", "signal", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isHandleEvent", "aborted", "constructorName", "targetSource", "onAbort", "unshift", "onPropertySymbol", "findEventTasks", "keys", "match", "exec", "evtName", "symbolCaptureEventName", "captureTasks", "removeTasks", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "concat", "Event", "patchQueueMicrotask", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "clearTask", "delay", "timer", "handle", "ref", "unref", "bind", "id", "patchCustomElements", "callbacks", "customElements", "eventTargetPatch", "eventNames", "EVENT_TARGET", "patchEvent", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "filteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "substring", "propertyDescriptorPatch", "patchTargets", "ignoreErrorProperties", "patchBrowser", "legacyPatch", "clear", "blockingMethods", "s", "XMLHttpRequestEventTarget", "patchXHR", "XHR_TASK", "XHR_SYNC", "XHR_LISTENER", "XHR_SCHEDULED", "XHR_URL", "XHR_ERROR_BEFORE_SCHEDULED", "XMLHttpRequest", "XMLHttpRequestPrototype", "findPendingTask", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "newListener", "readyState", "DONE", "loadTasks", "status", "oriInvoke", "storedTask", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XMLHTTPREQUEST_SOURCE", "fetchTaskAborting", "fetchTaskScheduling", "url", "geolocation", "findPromiseRejectionHandler", "e", "eventTasks", "for<PERSON>ach", "PromiseRejectionEvent", "evt", "promise", "reason", "rejection", "patchPromise", "readableObjectToString", "JSON", "stringify", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "creationTrace", "console", "stack", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "then", "forwardResolution", "forwardRejection", "ZoneAwarePromise", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "REJECTED_NO_CATCH", "makeResolver", "v", "resolvePromise", "wasCalled", "wrapper", "wrappedFunction", "TYPE_ERROR", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "scheduleResolveOrReject", "REJECTION_HANDLED_HANDLER", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "ZONE_AWARE_PROMISE_TO_STRING", "AggregateError", "withResolvers", "res", "rej", "any", "values", "Symbol", "iterator", "Promise", "promises", "finished", "race", "onResolve", "onReject", "all", "allWithCallback", "allSettled", "P", "then<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "curValueIndex", "thenErr", "executor", "toStringTag", "species", "C", "catch", "finally", "onFinally", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "wrapped", "zoneify", "resultPromise", "ctor", "patchToString", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "nativePromise", "nativeError", "originalObjectToString", "PROMISE_OBJECT_TO_STRING", "targetName", "method", "nativeDelegate", "opts", "descriptor", "patchUtil", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "patchCommon", "Zone$1"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0]}