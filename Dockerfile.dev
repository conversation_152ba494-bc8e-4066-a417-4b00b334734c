# Stage 1: Build do frontend Angular
FROM node:20 AS frontend-build
WORKDIR /app/frontend
COPY bora-estudar-front-help/package*.json ./
RUN npm install && npm cache clean --force
COPY bora-estudar-front-help/ .
RUN npm run build-dev

# Stage 2: Build do backend Spring Boot
FROM maven:3.9-eclipse-temurin-17 AS backend-build
WORKDIR /app/backend
RUN rm -rf /app/backend/target
COPY bora-estudar-back-help/pom.xml ./
COPY bora-estudar-back-help/src ./src
RUN mvn dependency:go-offline
RUN mvn clean package -DskipTests

# Stage 3: Criar a imagem final
FROM eclipse-temurin:17-jre-alpine
WORKDIR /app

# Copiar o backend construído
COPY --from=backend-build /app/backend/target/bora-estudar-1.0.0.jar /app/backend.jar

# Copiar o frontend construído
COPY --from=frontend-build /app/frontend/dist/bora-estudar-front /app/static

# Expor a porta do backend
EXPOSE 8080

# Comando para iniciar a aplicação
CMD ["java", "-jar", "/app/backend.jar", "--spring.profiles.active=local"]