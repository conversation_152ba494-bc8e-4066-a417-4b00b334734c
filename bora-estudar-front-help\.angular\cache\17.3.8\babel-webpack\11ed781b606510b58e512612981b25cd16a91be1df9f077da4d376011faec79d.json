{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input } from '@angular/core';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"*\"];\nlet BaseIcon = /*#__PURE__*/(() => {\n  class BaseIcon {\n    label;\n    spin = false;\n    styleClass;\n    role;\n    ariaLabel;\n    ariaHidden;\n    ngOnInit() {\n      this.getAttributes();\n    }\n    getAttributes() {\n      const isLabelEmpty = ObjectUtils.isEmpty(this.label);\n      this.role = !isLabelEmpty ? 'img' : undefined;\n      this.ariaLabel = !isLabelEmpty ? this.label : undefined;\n      this.ariaHidden = isLabelEmpty;\n    }\n    getClassNames() {\n      return `p-icon ${this.styleClass ? this.styleClass + ' ' : ''}${this.spin ? 'p-icon-spin' : ''}`;\n    }\n    static ɵfac = function BaseIcon_Factory(t) {\n      return new (t || BaseIcon)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: BaseIcon,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [1, \"p-element\", \"p-icon-wrapper\"],\n      inputs: {\n        label: \"label\",\n        spin: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"spin\", \"spin\", booleanAttribute],\n        styleClass: \"styleClass\"\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function BaseIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return BaseIcon;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BaseIcon };\n//# sourceMappingURL=primeng-baseicon.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}