services:
  app:
    container_name: bora-estudar-app-local
    build:
      context: .
      dockerfile: Dockerfile.dev
    env_file:
      - .env.dev
    ports:
      - "8080:8080"
    # volumes:
    #   - ./backend:/app/backend
    environment:
      - SPRING_PROFILES_ACTIVE=local
      - DB_NAME=${DB_NAME}
      - DB_URL=jdbc:postgresql://db:5432/${DB_NAME}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
    depends_on:
      - db

  db:
    image: postgres:13
    env_file:
      - .env.dev
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data: