{"ast": null, "code": "import { ChangeDetectorRef, inject } from '@angular/core';\nimport { AuthService } from './core/security/auth/auth.service';\nimport { Router, RouterLink, RouterOutlet } from '@angular/router';\nimport { <PERSON><PERSON>avList, MatListItem } from '@angular/material/list';\nimport { <PERSON><PERSON><PERSON>avContainer, Mat<PERSON>idenav, MatSidenavContent } from '@angular/material/sidenav';\nimport { MatIcon } from '@angular/material/icon';\nimport { MatIconButton } from '@angular/material/button';\nimport { MatToolbar } from '@angular/material/toolbar';\nimport { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { NavigationServiceService } from './study-group/navigation-service.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"snav\"];\nfunction AppComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_3_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navigateToSearch());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_back\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", ctx_r1.userMenu);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Seja bem vindo(a), \", (ctx_r1.user == null ? null : ctx_r1.user.name) || \"Usu\\u00E1rio\", \"\");\n  }\n}\nfunction AppComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"mat-menu\", null, 1)(4, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_9_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8, \"Sair\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r3);\n      i0.ɵɵnextContext();\n      const snav_r4 = i0.ɵɵreference(12);\n      return i0.ɵɵresetView(snav_r4.toggle());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"menu\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const userMenu_r5 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate((ctx_r1.user == null ? null : ctx_r1.user.name) || \"Usu\\u00E1rio\");\n  }\n}\nfunction AppComponent_Conditional_13_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 13);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_13_Conditional_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.close());\n    });\n    i0.ɵɵtext(1, \"Home\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"a\", 14);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_13_Conditional_1_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.close());\n    });\n    i0.ɵɵtext(3, \"Criar Grupos\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 15);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_13_Conditional_1_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.close());\n    });\n    i0.ɵɵtext(5, \"Meus Grupos\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-nav-list\");\n    i0.ɵɵtemplate(1, AppComponent_Conditional_13_Conditional_1_Template, 6, 0);\n    i0.ɵɵelementStart(2, \"a\", 12);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_13_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵtext(3, \"Sair\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.router.url !== \"/associate\" ? 1 : -1);\n  }\n}\nfunction AppComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-nav-list\")(1, \"a\", 16);\n    i0.ɵɵtext(2, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 17);\n    i0.ɵɵtext(4, \"Register\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AppComponent {\n  constructor(location) {\n    this.location = location;\n    this.appName = 'Bora Estudar UFF';\n    this.title = 'bora-estudar-front';\n    this.isLoggedIn = false;\n    this.user = undefined;\n    this.showBackIcon = false;\n    this.snackBar = inject(MatSnackBar);\n    this.authService = inject(AuthService);\n    this.router = inject(Router);\n    this.cdr = inject(ChangeDetectorRef);\n    this.navigationService = inject(NavigationServiceService);\n  }\n  ngOnInit() {\n    this.authService.isLoggedIn().subscribe(isLoggedIn => {\n      this.isLoggedIn = isLoggedIn;\n      if (isLoggedIn) {\n        this.getUser();\n      }\n      this.cdr.detectChanges();\n    });\n    this.router.events.subscribe(() => {\n      this.showBackIcon = this.router.url !== '/search';\n      if (this.router.url === '/create') {\n        this.appName = 'Criar Grupo';\n      } else if (this.router.url === '/my-study-group') {\n        this.appName = 'Meus Grupos';\n      } else if (this.router.url.startsWith('/edit')) {\n        this.appName = 'Editar';\n      } else if (this.router.url.startsWith('/detail')) {\n        this.appName = 'Detalhes';\n      } else {\n        this.appName = 'Bora Estudar UFF';\n      }\n    });\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: data => {\n        console.log(data);\n        this.router.navigateByUrl('/login');\n        this.close();\n        this.snackBar.open('Desconectado com sucesso!', 'X', {\n          duration: 2500\n        });\n      },\n      error: error => {\n        console.log(error);\n      }\n    });\n  }\n  getUser() {\n    this.authService.getUser().subscribe({\n      next: data => {\n        console.log(data);\n        this.user = data;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.log(error);\n      }\n    });\n  }\n  navigateToSearch() {\n    this.router.navigate(['/search']);\n  }\n  // navigateToSearch(): void {\n  //   const previousUrl = this.navigationService.getPreviousUrl();\n  //   if (previousUrl) {\n  //       this.router.navigate([previousUrl]);\n  //   } else {\n  //       this.router.navigate(['/home']);\n  //   }\n  // }\n  close() {\n    if (this.sidenav) {\n      this.sidenav.close();\n    }\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Location));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    viewQuery: function AppComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sidenav = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 18,\n    vars: 5,\n    consts: [[\"snav\", \"\"], [\"userMenu\", \"matMenu\"], [1, \"app_container\"], [\"color\", \"primary\", \"position\", \"start\", 1, \"header_toolbar\"], [\"mat-icon-button\", \"\"], [1, \"user-name-toolbar\", 3, \"matMenuTriggerFor\"], [1, \"spacer\"], [1, \"app_name\"], [1, \"sidenav_container\"], [\"mode\", \"over\", 1, \"mat_sidenav_content\"], [\"mat-icon-button\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-list-item\", \"\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/search\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/create\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/my-study-group\", 3, \"click\"], [\"mat-list-item\", \"\", \"routerLink\", \"/login\"], [\"mat-list-item\", \"\", \"routerLink\", \"/register\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"body\")(1, \"div\", 2)(2, \"mat-toolbar\", 3);\n        i0.ɵɵtemplate(3, AppComponent_Conditional_3_Template, 3, 0, \"button\", 4)(4, AppComponent_Conditional_4_Template, 2, 2, \"span\", 5);\n        i0.ɵɵelement(5, \"span\", 6);\n        i0.ɵɵelementStart(6, \"h1\", 7);\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(8, \"span\", 6);\n        i0.ɵɵtemplate(9, AppComponent_Conditional_9_Template, 12, 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"mat-sidenav-container\", 8)(11, \"mat-sidenav\", 9, 0);\n        i0.ɵɵtemplate(13, AppComponent_Conditional_13_Template, 4, 1, \"mat-nav-list\")(14, AppComponent_Conditional_14_Template, 5, 0);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"mat-sidenav-content\")(16, \"router-outlet\");\n        i0.ɵɵelement(17, \"main\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(3, ctx.showBackIcon && ctx.isLoggedIn === true && ctx.router.url !== \"/associate\" ? 3 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(4, ctx.isLoggedIn === true ? 4 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.appName);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(9, ctx.isLoggedIn === true ? 9 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(13, ctx.isLoggedIn === true ? 13 : 14);\n      }\n    },\n    dependencies: [MatToolbar, MatIconButton, MatIcon, MatSidenavContainer, MatSidenav, MatNavList, MatListItem, RouterLink, MatSidenavContent, RouterOutlet, MatMenu, MatMenuItem, MatMenuTrigger],\n    styles: [\".header_toolbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n}\\n\\nh1.app_name[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n.sidenav_container[_ngcontent-%COMP%] {\\n  margin-top: 65px;\\n  display: flex;\\n  flex-direction: column;\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n}\\n\\n.mat_sidenav_content[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n\\n.header_container[_ngcontent-%COMP%]   .sidenav_container[_ngcontent-%COMP%] {\\n  flex: 1 0 auto;\\n}\\n\\n.example-spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n  .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input {\\n  width: 100% !important;\\n}\\n\\n  .p-progress-spinner-circle {\\n  stroke: #3f51b5 !important;\\n}\\n\\n.user-name-toolbar[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: white;\\n  font-weight: 500;\\n  margin-right: 16px;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.user-name-toolbar[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["ChangeDetectorRef", "inject", "AuthService", "Router", "RouterLink", "RouterOutlet", "MatNavList", "MatListItem", "Mat<PERSON>idenav<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatIcon", "MatIconButton", "MatToolbar", "MatMenu", "MatMenuItem", "MatMenuTrigger", "MatSnackBar", "NavigationServiceService", "i0", "ɵɵelementStart", "ɵɵlistener", "AppComponent_Conditional_3_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "navigateToSearch", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "userMenu", "ɵɵadvance", "ɵɵtextInterpolate1", "user", "name", "AppComponent_Conditional_9_Template_button_click_4_listener", "_r3", "logout", "AppComponent_Conditional_9_Template_button_click_9_listener", "snav_r4", "ɵɵreference", "toggle", "userMenu_r5", "ɵɵtextInterpolate", "AppComponent_Conditional_13_Conditional_1_Template_a_click_0_listener", "_r7", "close", "AppComponent_Conditional_13_Conditional_1_Template_a_click_2_listener", "AppComponent_Conditional_13_Conditional_1_Template_a_click_4_listener", "ɵɵtemplate", "AppComponent_Conditional_13_Conditional_1_Template", "AppComponent_Conditional_13_Template_a_click_2_listener", "_r6", "ɵɵconditional", "router", "url", "AppComponent", "constructor", "location", "appName", "title", "isLoggedIn", "undefined", "showBackIcon", "snackBar", "authService", "cdr", "navigationService", "ngOnInit", "subscribe", "getUser", "detectChanges", "events", "startsWith", "next", "data", "console", "log", "navigateByUrl", "open", "duration", "error", "navigate", "sidenav", "_", "ɵɵdirectiveInject", "i1", "Location", "_2", "selectors", "viewQuery", "AppComponent_Query", "rf", "ctx", "AppComponent_Conditional_3_Template", "AppComponent_Conditional_4_Template", "ɵɵelement", "AppComponent_Conditional_9_Template", "AppComponent_Conditional_13_Template", "AppComponent_Conditional_14_Template", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Documents\\uff\\tcc\\github\\monolito-bora-estudar\\bora-estudar-front-help\\src\\app\\app.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, ViewChild, inject } from '@angular/core';\r\nimport { AuthService } from './core/security/auth/auth.service';\r\nimport { Router, RouterLink, RouterOutlet } from '@angular/router';\r\nimport { UserResponseBasicDto } from './shared/models/user/user-response-basic-dto';\r\nimport { MatNavList, MatListItem } from '@angular/material/list';\r\nimport { MatSidenavContainer, MatSidenav, MatSidenavContent } from '@angular/material/sidenav';\r\nimport { MatIcon } from '@angular/material/icon';\r\nimport { MatIconButton } from '@angular/material/button';\r\nimport { MatToolbar } from '@angular/material/toolbar';\r\nimport { MatMenu, MatMenuItem, MatMenuTrigger } from '@angular/material/menu';\r\nimport { MatSnackBar } from '@angular/material/snack-bar';\r\nimport { Location } from '@angular/common'\r\nimport { NavigationServiceService } from './study-group/navigation-service.service';\r\n\r\n@Component({\r\n    selector: 'app-root',\r\n    templateUrl: './app.component.html',\r\n    styleUrls: ['./app.component.scss'],\r\n    standalone: true,\r\n    imports: [\r\n        MatToolbar,\r\n        MatIconButton,\r\n        MatIcon,\r\n        MatSidenavContainer,\r\n        MatSidenav,\r\n        MatNavList,\r\n        MatListItem,\r\n        RouterLink,\r\n        MatSidenavContent,\r\n        RouterOutlet,\r\n        MatMenu,\r\n        MatMenuItem,\r\n        MatMenuTrigger,\r\n    ],\r\n})\r\nexport class AppComponent implements OnInit {\r\n  appName: string = 'Bora Estudar UFF';\r\n  title = 'bora-estudar-front';\r\n  isLoggedIn = false;\r\n  user: UserResponseBasicDto | undefined = undefined;\r\n  showBackIcon = false;\r\n  @ViewChild('snav') sidenav!: MatSidenav;\r\n\r\n  private snackBar = inject(MatSnackBar);\r\n  private authService = inject(AuthService);\r\n  public router = inject(Router);\r\n  private cdr = inject(ChangeDetectorRef);\r\n  private navigationService = inject(NavigationServiceService);\r\n\r\n  constructor(\r\n    private location: Location\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.authService.isLoggedIn().subscribe((isLoggedIn) => {\r\n      this.isLoggedIn = isLoggedIn;\r\n      if (isLoggedIn) {\r\n        this.getUser();\r\n      }\r\n      this.cdr.detectChanges();\r\n    });\r\n\r\n    this.router.events.subscribe(() => {\r\n      this.showBackIcon = this.router.url !== '/search';\r\n\r\n      if(this.router.url === '/create'){\r\n        this.appName = 'Criar Grupo';\r\n      } else if(this.router.url === '/my-study-group'){\r\n        this.appName = 'Meus Grupos';\r\n      } else if(this.router.url.startsWith('/edit')){\r\n        this.appName = 'Editar';\r\n      } else if(this.router.url.startsWith('/detail')){\r\n        this.appName = 'Detalhes';\r\n      } else {\r\n        this.appName = 'Bora Estudar UFF';\r\n      }\r\n    });\r\n  }\r\n\r\n  public logout() {\r\n    this.authService.logout().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.router.navigateByUrl('/login');\r\n        this.close();\r\n\r\n        this.snackBar.open(\r\n          'Desconectado com sucesso!',\r\n          'X',\r\n          { duration: 2500 }\r\n        );\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  public getUser() {\r\n    this.authService.getUser().subscribe({\r\n      next: (data) => {\r\n        console.log(data);\r\n        this.user = data;\r\n        this.cdr.detectChanges();\r\n      },\r\n      error: (error) => {\r\n        console.log(error);\r\n      },\r\n    });\r\n  }\r\n\r\n  navigateToSearch(): void {\r\n    this.router.navigate(['/search']);\r\n  }\r\n\r\n  // navigateToSearch(): void {\r\n  //   const previousUrl = this.navigationService.getPreviousUrl();\r\n\r\n  //   if (previousUrl) {\r\n  //       this.router.navigate([previousUrl]);\r\n  //   } else {\r\n  //       this.router.navigate(['/home']);\r\n  //   }\r\n  // }\r\n\r\n  close(){\r\n    if (this.sidenav) {\r\n      this.sidenav.close();\r\n    }\r\n  }\r\n}\r\n", "<body>\r\n  <div class=\"app_container\">\r\n    <mat-toolbar color=\"primary\" position=\"start\" class=\"header_toolbar\">\r\n\r\n      @if (showBackIcon && isLoggedIn === true && this.router.url !== '/associate' ) {\r\n        <button mat-icon-button (click)=\"navigateToSearch()\">\r\n          <mat-icon>arrow_back</mat-icon>\r\n        </button>\r\n      }\r\n\r\n      @if (isLoggedIn === true) {\r\n        <span class=\"user-name-toolbar\" [matMenuTriggerFor]=\"userMenu\">Seja bem vindo(a), {{ user?.name || 'Usuário' }}</span>\r\n      }\r\n\r\n      <span class=\"spacer\"></span>\r\n\r\n      <h1 class=\"app_name\">{{appName}}</h1>\r\n\r\n      <span class=\"spacer\"></span>\r\n\r\n      @if (isLoggedIn === true) {\r\n        <!-- Nome do usuário logado -->\r\n        <span class=\"user-name-toolbar\" [matMenuTriggerFor]=\"userMenu\">{{ user?.name || 'Usu<PERSON>rio' }}</span>\r\n\r\n        <!-- Menu dropdown para logout -->\r\n        <mat-menu #userMenu=\"matMenu\">\r\n          <button mat-menu-item (click)=\"logout()\">\r\n            <mat-icon>logout</mat-icon>\r\n            <span>Sair</span>\r\n          </button>\r\n        </mat-menu>\r\n\r\n        <button mat-icon-button (click)=\"snav.toggle()\">\r\n          <mat-icon>menu</mat-icon>\r\n        </button>\r\n      }\r\n    </mat-toolbar>\r\n\r\n    <mat-sidenav-container class=\"sidenav_container\">\r\n      <mat-sidenav #snav mode=\"over\" class=\"mat_sidenav_content\">\r\n        @if (isLoggedIn === true) {\r\n          <mat-nav-list>\r\n            @if(this.router.url !== '/associate'){\r\n              <a mat-list-item routerLink=\"/search\" (click)=\"close()\">Home</a>\r\n              <a mat-list-item routerLink=\"/create\" (click)=\"close()\">Criar Grupos</a>\r\n              <a mat-list-item routerLink=\"/my-study-group\" (click)=\"close()\">Meus Grupos</a>\r\n            }\r\n            <a mat-list-item (click)=\"logout()\">Sair</a>\r\n          </mat-nav-list>\r\n        } @else {\r\n          <mat-nav-list>\r\n            <a mat-list-item routerLink=\"/login\">Login</a>\r\n            <a mat-list-item routerLink=\"/register\">Register</a>\r\n          </mat-nav-list>\r\n        }\r\n      </mat-sidenav>\r\n\r\n      <mat-sidenav-content>\r\n        <router-outlet><main></main></router-outlet>\r\n      </mat-sidenav-content>\r\n    </mat-sidenav-container>\r\n  </div>\r\n</body>\r\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAgCC,MAAM,QAAQ,eAAe;AACvF,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,MAAM,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AAElE,SAASC,UAAU,EAAEC,WAAW,QAAQ,wBAAwB;AAChE,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,iBAAiB,QAAQ,2BAA2B;AAC9F,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,OAAO,EAAEC,WAAW,EAAEC,cAAc,QAAQ,wBAAwB;AAC7E,SAASC,WAAW,QAAQ,6BAA6B;AAEzD,SAASC,wBAAwB,QAAQ,0CAA0C;;;;;;;ICP3EC,EAAA,CAAAC,cAAA,iBAAqD;IAA7BD,EAAA,CAAAE,UAAA,mBAAAC,4DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAClDT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,iBAAU;IACtBV,EADsB,CAAAW,YAAA,EAAW,EACxB;;;;;IAITX,EAAA,CAAAC,cAAA,cAA+D;IAAAD,EAAA,CAAAU,MAAA,GAAgD;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAAtFX,EAAA,CAAAY,UAAA,sBAAAN,MAAA,CAAAO,QAAA,CAA8B;IAACb,EAAA,CAAAc,SAAA,EAAgD;IAAhDd,EAAA,CAAAe,kBAAA,yBAAAT,MAAA,CAAAU,IAAA,kBAAAV,MAAA,CAAAU,IAAA,CAAAC,IAAA,wBAAgD;;;;;;IAW/GjB,EAAA,CAAAC,cAAA,cAA+D;IAAAD,EAAA,CAAAU,MAAA,GAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIjGX,EADF,CAAAC,cAAA,wBAA8B,iBACa;IAAnBD,EAAA,CAAAE,UAAA,mBAAAgB,4DAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,MAAA,EAAQ;IAAA,EAAC;IACtCpB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC3BX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAEdV,EAFc,CAAAW,YAAA,EAAO,EACV,EACA;IAEXX,EAAA,CAAAC,cAAA,iBAAgD;IAAxBD,EAAA,CAAAE,UAAA,mBAAAmB,4DAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAAnB,EAAA,CAAAO,aAAA;MAAA,MAAAe,OAAA,GAAAtB,EAAA,CAAAuB,WAAA;MAAA,OAAAvB,EAAA,CAAAQ,WAAA,CAASc,OAAA,CAAAE,MAAA,EAAa;IAAA,EAAC;IAC7CxB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAU,MAAA,YAAI;IAChBV,EADgB,CAAAW,YAAA,EAAW,EAClB;;;;;IAZuBX,EAAA,CAAAY,UAAA,sBAAAa,WAAA,CAA8B;IAACzB,EAAA,CAAAc,SAAA,EAA6B;IAA7Bd,EAAA,CAAA0B,iBAAA,EAAApB,MAAA,CAAAU,IAAA,kBAAAV,MAAA,CAAAU,IAAA,CAAAC,IAAA,oBAA6B;;;;;;IAqBtFjB,EAAA,CAAAC,cAAA,YAAwD;IAAlBD,EAAA,CAAAE,UAAA,mBAAAyB,sEAAA;MAAA3B,EAAA,CAAAI,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuB,KAAA,EAAO;IAAA,EAAC;IAAC7B,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAChEX,EAAA,CAAAC,cAAA,YAAwD;IAAlBD,EAAA,CAAAE,UAAA,mBAAA4B,sEAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuB,KAAA,EAAO;IAAA,EAAC;IAAC7B,EAAA,CAAAU,MAAA,mBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACxEX,EAAA,CAAAC,cAAA,YAAgE;IAAlBD,EAAA,CAAAE,UAAA,mBAAA6B,sEAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuB,KAAA,EAAO;IAAA,EAAC;IAAC7B,EAAA,CAAAU,MAAA,kBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;;;IAJnFX,EAAA,CAAAC,cAAA,mBAAc;IACZD,EAAA,CAAAgC,UAAA,IAAAC,kDAAA,OAAsC;IAKtCjC,EAAA,CAAAC,cAAA,YAAoC;IAAnBD,EAAA,CAAAE,UAAA,mBAAAgC,wDAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,MAAA,EAAQ;IAAA,EAAC;IAACpB,EAAA,CAAAU,MAAA,WAAI;IAC1CV,EAD0C,CAAAW,YAAA,EAAI,EAC/B;;;;IANbX,EAAA,CAAAc,SAAA,EAIC;IAJDd,EAAA,CAAAoC,aAAA,IAAA9B,MAAA,CAAA+B,MAAA,CAAAC,GAAA,2BAIC;;;;;IAKDtC,EADF,CAAAC,cAAA,mBAAc,YACyB;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAC9CX,EAAA,CAAAC,cAAA,YAAwC;IAAAD,EAAA,CAAAU,MAAA,eAAQ;IAClDV,EADkD,CAAAW,YAAA,EAAI,EACvC;;;ADlBzB,OAAM,MAAO4B,YAAY;EAcvBC,YACUC,QAAkB;IAAlB,KAAAA,QAAQ,GAARA,QAAQ;IAdlB,KAAAC,OAAO,GAAW,kBAAkB;IACpC,KAAAC,KAAK,GAAG,oBAAoB;IAC5B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAA5B,IAAI,GAAqC6B,SAAS;IAClD,KAAAC,YAAY,GAAG,KAAK;IAGZ,KAAAC,QAAQ,GAAGjE,MAAM,CAACgB,WAAW,CAAC;IAC9B,KAAAkD,WAAW,GAAGlE,MAAM,CAACC,WAAW,CAAC;IAClC,KAAAsD,MAAM,GAAGvD,MAAM,CAACE,MAAM,CAAC;IACtB,KAAAiE,GAAG,GAAGnE,MAAM,CAACD,iBAAiB,CAAC;IAC/B,KAAAqE,iBAAiB,GAAGpE,MAAM,CAACiB,wBAAwB,CAAC;EAIzD;EAEHoD,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACJ,UAAU,EAAE,CAACQ,SAAS,CAAER,UAAU,IAAI;MACrD,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAIA,UAAU,EAAE;QACd,IAAI,CAACS,OAAO,EAAE;MAChB;MACA,IAAI,CAACJ,GAAG,CAACK,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACjB,MAAM,CAACkB,MAAM,CAACH,SAAS,CAAC,MAAK;MAChC,IAAI,CAACN,YAAY,GAAG,IAAI,CAACT,MAAM,CAACC,GAAG,KAAK,SAAS;MAEjD,IAAG,IAAI,CAACD,MAAM,CAACC,GAAG,KAAK,SAAS,EAAC;QAC/B,IAAI,CAACI,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACL,MAAM,CAACC,GAAG,KAAK,iBAAiB,EAAC;QAC9C,IAAI,CAACI,OAAO,GAAG,aAAa;MAC9B,CAAC,MAAM,IAAG,IAAI,CAACL,MAAM,CAACC,GAAG,CAACkB,UAAU,CAAC,OAAO,CAAC,EAAC;QAC5C,IAAI,CAACd,OAAO,GAAG,QAAQ;MACzB,CAAC,MAAM,IAAG,IAAI,CAACL,MAAM,CAACC,GAAG,CAACkB,UAAU,CAAC,SAAS,CAAC,EAAC;QAC9C,IAAI,CAACd,OAAO,GAAG,UAAU;MAC3B,CAAC,MAAM;QACL,IAAI,CAACA,OAAO,GAAG,kBAAkB;MACnC;IACF,CAAC,CAAC;EACJ;EAEOtB,MAAMA,CAAA;IACX,IAAI,CAAC4B,WAAW,CAAC5B,MAAM,EAAE,CAACgC,SAAS,CAAC;MAClCK,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAACrB,MAAM,CAACwB,aAAa,CAAC,QAAQ,CAAC;QACnC,IAAI,CAAChC,KAAK,EAAE;QAEZ,IAAI,CAACkB,QAAQ,CAACe,IAAI,CAChB,2BAA2B,EAC3B,GAAG,EACH;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;MACH,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEOX,OAAOA,CAAA;IACZ,IAAI,CAACL,WAAW,CAACK,OAAO,EAAE,CAACD,SAAS,CAAC;MACnCK,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;QACjB,IAAI,CAAC1C,IAAI,GAAG0C,IAAI;QAChB,IAAI,CAACT,GAAG,CAACK,aAAa,EAAE;MAC1B,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACC,GAAG,CAACI,KAAK,CAAC;MACpB;KACD,CAAC;EACJ;EAEAvD,gBAAgBA,CAAA;IACd,IAAI,CAAC4B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEApC,KAAKA,CAAA;IACH,IAAI,IAAI,CAACqC,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACrC,KAAK,EAAE;IACtB;EACF;EAAC,QAAAsC,CAAA,G;qBA9FU5B,YAAY,EAAAvC,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAZhC,YAAY;IAAAiC,SAAA;IAAAC,SAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCjCrB3E,EAFJ,CAAAC,cAAA,WAAM,aACuB,qBAC4C;QAQnED,EANA,CAAAgC,UAAA,IAAA6C,mCAAA,oBAAgF,IAAAC,mCAAA,kBAMrD;QAI3B9E,EAAA,CAAA+E,SAAA,cAA4B;QAE5B/E,EAAA,CAAAC,cAAA,YAAqB;QAAAD,EAAA,CAAAU,MAAA,GAAW;QAAAV,EAAA,CAAAW,YAAA,EAAK;QAErCX,EAAA,CAAA+E,SAAA,cAA4B;QAE5B/E,EAAA,CAAAgC,UAAA,IAAAgD,mCAAA,QAA2B;QAgB7BhF,EAAA,CAAAW,YAAA,EAAc;QAGZX,EADF,CAAAC,cAAA,gCAAiD,yBACY;QAUvDD,EATF,CAAAgC,UAAA,KAAAiD,oCAAA,uBAA2B,KAAAC,oCAAA,OASlB;QAMXlF,EAAA,CAAAW,YAAA,EAAc;QAGZX,EADF,CAAAC,cAAA,2BAAqB,qBACJ;QAAAD,EAAA,CAAA+E,SAAA,YAAa;QAIpC/E,EAJoC,CAAAW,YAAA,EAAgB,EACxB,EACA,EACpB,EACD;;;QA1DDX,EAAA,CAAAc,SAAA,GAIC;QAJDd,EAAA,CAAAoC,aAAA,IAAAwC,GAAA,CAAA9B,YAAA,IAAA8B,GAAA,CAAAhC,UAAA,aAAAgC,GAAA,CAAAvC,MAAA,CAAAC,GAAA,2BAIC;QAEDtC,EAAA,CAAAc,SAAA,EAEC;QAFDd,EAAA,CAAAoC,aAAA,IAAAwC,GAAA,CAAAhC,UAAA,mBAEC;QAIoB5C,EAAA,CAAAc,SAAA,GAAW;QAAXd,EAAA,CAAA0B,iBAAA,CAAAkD,GAAA,CAAAlC,OAAA,CAAW;QAIhC1C,EAAA,CAAAc,SAAA,GAeC;QAfDd,EAAA,CAAAoC,aAAA,IAAAwC,GAAA,CAAAhC,UAAA,mBAeC;QAKC5C,EAAA,CAAAc,SAAA,GAcC;QAdDd,EAAA,CAAAoC,aAAA,KAAAwC,GAAA,CAAAhC,UAAA,oBAcC;;;mBDlCDlD,UAAU,EACVD,aAAa,EACbD,OAAO,EACPH,mBAAmB,EACnBC,UAAU,EACVH,UAAU,EACVC,WAAW,EACXH,UAAU,EACVM,iBAAiB,EACjBL,YAAY,EACZS,OAAO,EACPC,WAAW,EACXC,cAAc;IAAAsF,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}