"use strict";(self.webpackChunkbora_estudar_front=self.webpackChunkbora_estudar_front||[]).push([[461],{935:()=>{const ee=globalThis;function Q(t){return(ee.__Zone_symbol_prefix||"__zone_symbol__")+t}const ye=Object.getOwnPropertyDescriptor,Ne=Object.defineProperty,Ie=Object.getPrototypeOf,ht=Object.create,dt=Array.prototype.slice,Le="addEventListener",Me="removeEventListener",Ze=Q(Le),Ae=Q(Me),ce="true",ae="false",pe=Q("");function je(t,r){return Zone.current.wrap(t,r)}function He(t,r,c,n,i){return Zone.current.scheduleMacroTask(t,r,c,n,i)}const H=Q,be=typeof window<"u",de=be?window:void 0,J=be&&de||globalThis,_t="removeAttribute";function xe(t,r){for(let c=t.length-1;c>=0;c--)"function"==typeof t[c]&&(t[c]=je(t[c],r+"_"+c));return t}function ze(t){return!t||!1!==t.writable&&!("function"==typeof t.get&&typeof t.set>"u")}const qe=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Pe=!("nw"in J)&&typeof J.process<"u"&&"[object process]"===J.process.toString(),Ge=!Pe&&!qe&&!(!be||!de.HTMLElement),Xe=typeof J.process<"u"&&"[object process]"===J.process.toString()&&!qe&&!(!be||!de.HTMLElement),we={},Ye=function(t){if(!(t=t||J.event))return;let r=we[t.type];r||(r=we[t.type]=H("ON_PROPERTY"+t.type));const c=this||t.target||J,n=c[r];let i;return Ge&&c===de&&"error"===t.type?(i=n&&n.call(this,t.message,t.filename,t.lineno,t.colno,t.error),!0===i&&t.preventDefault()):(i=n&&n.apply(this,arguments),null!=i&&!i&&t.preventDefault()),i};function $e(t,r,c){let n=ye(t,r);if(!n&&c&&ye(c,r)&&(n={enumerable:!0,configurable:!0}),!n||!n.configurable)return;const i=H("on"+r+"patched");if(t.hasOwnProperty(i)&&t[i])return;delete n.writable,delete n.value;const u=n.get,_=n.set,E=r.slice(2);let p=we[E];p||(p=we[E]=H("ON_PROPERTY"+E)),n.set=function(C){let T=this;!T&&t===J&&(T=J),T&&("function"==typeof T[p]&&T.removeEventListener(E,Ye),_&&_.call(T,null),T[p]=C,"function"==typeof C&&T.addEventListener(E,Ye,!1))},n.get=function(){let C=this;if(!C&&t===J&&(C=J),!C)return null;const T=C[p];if(T)return T;if(u){let Z=u.call(this);if(Z)return n.set.call(this,Z),"function"==typeof C[_t]&&C.removeAttribute(r),Z}return null},Ne(t,r,n),t[i]=!0}function Je(t,r,c){if(r)for(let n=0;n<r.length;n++)$e(t,"on"+r[n],c);else{const n=[];for(const i in t)"on"==i.slice(0,2)&&n.push(i);for(let i=0;i<n.length;i++)$e(t,n[i],c)}}const ne=H("originalInstance");function me(t){const r=J[t];if(!r)return;J[H(t)]=r,J[t]=function(){const i=xe(arguments,t);switch(i.length){case 0:this[ne]=new r;break;case 1:this[ne]=new r(i[0]);break;case 2:this[ne]=new r(i[0],i[1]);break;case 3:this[ne]=new r(i[0],i[1],i[2]);break;case 4:this[ne]=new r(i[0],i[1],i[2],i[3]);break;default:throw new Error("Arg list too long.")}},ue(J[t],r);const c=new r(function(){});let n;for(n in c)"XMLHttpRequest"===t&&"responseBlob"===n||function(i){"function"==typeof c[i]?J[t].prototype[i]=function(){return this[ne][i].apply(this[ne],arguments)}:Ne(J[t].prototype,i,{set:function(u){"function"==typeof u?(this[ne][i]=je(u,t+"."+i),ue(this[ne][i],u)):this[ne][i]=u},get:function(){return this[ne][i]}})}(n);for(n in r)"prototype"!==n&&r.hasOwnProperty(n)&&(J[t][n]=r[n])}function le(t,r,c){let n=t;for(;n&&!n.hasOwnProperty(r);)n=Ie(n);!n&&t[r]&&(n=t);const i=H(r);let u=null;if(n&&(!(u=n[i])||!n.hasOwnProperty(i))&&(u=n[i]=n[r],ze(n&&ye(n,r)))){const E=c(u,i,r);n[r]=function(){return E(this,arguments)},ue(n[r],u)}return u}function Tt(t,r,c){let n=null;function i(u){const _=u.data;return _.args[_.cbIdx]=function(){u.invoke.apply(this,arguments)},n.apply(_.target,_.args),u}n=le(t,r,u=>function(_,E){const p=c(_,E);return p.cbIdx>=0&&"function"==typeof E[p.cbIdx]?He(p.name,E[p.cbIdx],p,i):u.apply(_,E)})}function ue(t,r){t[H("OriginalDelegate")]=r}let Ke=!1,Ve=!1;function yt(){if(Ke)return Ve;Ke=!0;try{const t=de.navigator.userAgent;(-1!==t.indexOf("MSIE ")||-1!==t.indexOf("Trident/")||-1!==t.indexOf("Edge/"))&&(Ve=!0)}catch{}return Ve}let _e=!1;if(typeof window<"u")try{const t=Object.defineProperty({},"passive",{get:function(){_e=!0}});window.addEventListener("test",t,t),window.removeEventListener("test",t,t)}catch{_e=!1}const pt={useG:!0},te={},Qe={},et=new RegExp("^"+pe+"(\\w+)(true|false)$"),tt=H("propagationStopped");function nt(t,r){const c=(r?r(t):t)+ae,n=(r?r(t):t)+ce,i=pe+c,u=pe+n;te[t]={},te[t][ae]=i,te[t][ce]=u}function mt(t,r,c,n){const i=n&&n.add||Le,u=n&&n.rm||Me,_=n&&n.listeners||"eventListeners",E=n&&n.rmAll||"removeAllListeners",p=H(i),C="."+i+":",T="prependListener",Z="."+T+":",P=function(b,h,F){if(b.isRemoved)return;const z=b.callback;let Y;"object"==typeof z&&z.handleEvent&&(b.callback=g=>z.handleEvent(g),b.originalDelegate=z);try{b.invoke(b,h,[F])}catch(g){Y=g}const B=b.options;return B&&"object"==typeof B&&B.once&&h[u].call(h,F.type,b.originalDelegate?b.originalDelegate:b.callback,B),Y};function j(b,h,F){if(!(h=h||t.event))return;const z=b||h.target||t,Y=z[te[h.type][F?ce:ae]];if(Y){const B=[];if(1===Y.length){const g=P(Y[0],z,h);g&&B.push(g)}else{const g=Y.slice();for(let U=0;U<g.length&&(!h||!0!==h[tt]);U++){const O=P(g[U],z,h);O&&B.push(O)}}if(1===B.length)throw B[0];for(let g=0;g<B.length;g++){const U=B[g];r.nativeScheduleMicroTask(()=>{throw U})}}}const W=function(b){return j(this,b,!1)},x=function(b){return j(this,b,!0)};function re(b,h){if(!b)return!1;let F=!0;h&&void 0!==h.useG&&(F=h.useG);const z=h&&h.vh;let Y=!0;h&&void 0!==h.chkDup&&(Y=h.chkDup);let B=!1;h&&void 0!==h.rt&&(B=h.rt);let g=b;for(;g&&!g.hasOwnProperty(i);)g=Ie(g);if(!g&&b[i]&&(g=b),!g||g[p])return!1;const U=h&&h.eventNameToString,O={},w=g[p]=g[i],v=g[H(u)]=g[u],S=g[H(_)]=g[_],K=g[H(E)]=g[E];let q;h&&h.prepend&&(q=g[H(h.prepend)]=g[h.prepend]);const $=F?function(s){if(!O.isExisting)return w.call(O.target,O.eventName,O.capture?x:W,O.options)}:function(s){return w.call(O.target,O.eventName,s.invoke,O.options)},A=F?function(s){if(!s.isRemoved){const l=te[s.eventName];let k;l&&(k=l[s.capture?ce:ae]);const R=k&&s.target[k];if(R)for(let y=0;y<R.length;y++)if(R[y]===s){R.splice(y,1),s.isRemoved=!0,s.removeAbortListener&&(s.removeAbortListener(),s.removeAbortListener=null),0===R.length&&(s.allRemoved=!0,s.target[k]=null);break}}if(s.allRemoved)return v.call(s.target,s.eventName,s.capture?x:W,s.options)}:function(s){return v.call(s.target,s.eventName,s.invoke,s.options)},ke=h&&h.diff?h.diff:function(s,l){const k=typeof l;return"function"===k&&s.callback===l||"object"===k&&s.originalDelegate===l},he=Zone[H("UNPATCHED_EVENTS")],Se=t[H("PASSIVE_EVENTS")],a=function(s,l,k,R,y=!1,I=!1){return function(){const L=this||t;let M=arguments[0];h&&h.transferEventName&&(M=h.transferEventName(M));let G=arguments[1];if(!G)return s.apply(this,arguments);if(Pe&&"uncaughtException"===M)return s.apply(this,arguments);let V=!1;if("function"!=typeof G){if(!G.handleEvent)return s.apply(this,arguments);V=!0}if(z&&!z(s,G,L,arguments))return;const fe=_e&&!!Se&&-1!==Se.indexOf(M),se=function f(s){if("object"==typeof s&&null!==s){const l={...s};return s.signal&&(l.signal=s.signal),l}return s}(function N(s,l){return!_e&&"object"==typeof s&&s?!!s.capture:_e&&l?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?{...s,passive:!0}:s:{passive:!0}:s}(arguments[2],fe)),Te=se?.signal;if(Te?.aborted)return;if(he)for(let ie=0;ie<he.length;ie++)if(M===he[ie])return fe?s.call(L,M,G,se):s.apply(this,arguments);const Be=!!se&&("boolean"==typeof se||se.capture),ct=!(!se||"object"!=typeof se)&&se.once,Lt=Zone.current;let Ue=te[M];Ue||(nt(M,U),Ue=te[M]);const at=Ue[Be?ce:ae];let De,ge=L[at],lt=!1;if(ge){if(lt=!0,Y)for(let ie=0;ie<ge.length;ie++)if(ke(ge[ie],G))return}else ge=L[at]=[];const ut=L.constructor.name,ft=Qe[ut];ft&&(De=ft[M]),De||(De=ut+l+(U?U(M):M)),O.options=se,ct&&(O.options.once=!1),O.target=L,O.capture=Be,O.eventName=M,O.isExisting=lt;const ve=F?pt:void 0;ve&&(ve.taskData=O),Te&&(O.options.signal=void 0);const oe=Lt.scheduleEventTask(De,G,ve,k,R);if(Te){O.options.signal=Te;const ie=()=>oe.zone.cancelTask(oe);s.call(Te,"abort",ie,{once:!0}),oe.removeAbortListener=()=>Te.removeEventListener("abort",ie)}return O.target=null,ve&&(ve.taskData=null),ct&&(O.options.once=!0),!_e&&"boolean"==typeof oe.options||(oe.options=se),oe.target=L,oe.capture=Be,oe.eventName=M,V&&(oe.originalDelegate=G),I?ge.unshift(oe):ge.push(oe),y?L:void 0}};return g[i]=a(w,C,$,A,B),q&&(g[T]=a(q,Z,function(s){return q.call(O.target,O.eventName,s.invoke,O.options)},A,B,!0)),g[u]=function(){const s=this||t;let l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));const k=arguments[2],R=!!k&&("boolean"==typeof k||k.capture),y=arguments[1];if(!y)return v.apply(this,arguments);if(z&&!z(v,y,s,arguments))return;const I=te[l];let L;I&&(L=I[R?ce:ae]);const M=L&&s[L];if(M)for(let G=0;G<M.length;G++){const V=M[G];if(ke(V,y))return M.splice(G,1),V.isRemoved=!0,0!==M.length||(V.allRemoved=!0,s[L]=null,R||"string"!=typeof l)||(s[pe+"ON_PROPERTY"+l]=null),V.zone.cancelTask(V),B?s:void 0}return v.apply(this,arguments)},g[_]=function(){const s=this||t;let l=arguments[0];h&&h.transferEventName&&(l=h.transferEventName(l));const k=[],R=rt(s,U?U(l):l);for(let y=0;y<R.length;y++){const I=R[y];k.push(I.originalDelegate?I.originalDelegate:I.callback)}return k},g[E]=function(){const s=this||t;let l=arguments[0];if(l){h&&h.transferEventName&&(l=h.transferEventName(l));const k=te[l];if(k){const I=s[k[ae]],L=s[k[ce]];if(I){const M=I.slice();for(let G=0;G<M.length;G++){const V=M[G];this[u].call(this,l,V.originalDelegate?V.originalDelegate:V.callback,V.options)}}if(L){const M=L.slice();for(let G=0;G<M.length;G++){const V=M[G];this[u].call(this,l,V.originalDelegate?V.originalDelegate:V.callback,V.options)}}}}else{const k=Object.keys(s);for(let R=0;R<k.length;R++){const I=et.exec(k[R]);let L=I&&I[1];L&&"removeListener"!==L&&this[E].call(this,L)}this[E].call(this,"removeListener")}if(B)return this},ue(g[i],w),ue(g[u],v),K&&ue(g[E],K),S&&ue(g[_],S),!0}let X=[];for(let b=0;b<c.length;b++)X[b]=re(c[b],n);return X}function rt(t,r){if(!r){const u=[];for(let _ in t){const E=et.exec(_);let p=E&&E[1];if(p&&(!r||p===r)){const C=t[_];if(C)for(let T=0;T<C.length;T++)u.push(C[T])}}return u}let c=te[r];c||(nt(r),c=te[r]);const n=t[c[ae]],i=t[c[ce]];return n?i?n.concat(i):n.slice():i?i.slice():[]}function kt(t,r){const c=t.Event;c&&c.prototype&&r.patchMethod(c.prototype,"stopImmediatePropagation",n=>function(i,u){i[tt]=!0,n&&n.apply(i,u)})}const Re=H("zoneTask");function Ee(t,r,c,n){let i=null,u=null;c+=n;const _={};function E(C){const T=C.data;return T.args[0]=function(){return C.invoke.apply(this,arguments)},T.handleId=i.apply(t,T.args),C}function p(C){return u.call(t,C.data.handleId)}i=le(t,r+=n,C=>function(T,Z){if("function"==typeof Z[0]){const P={isPeriodic:"Interval"===n,delay:"Timeout"===n||"Interval"===n?Z[1]||0:void 0,args:Z},j=Z[0];Z[0]=function(){try{return j.apply(this,arguments)}finally{P.isPeriodic||("number"==typeof P.handleId?delete _[P.handleId]:P.handleId&&(P.handleId[Re]=null))}};const W=He(r,Z[0],P,E,p);if(!W)return W;const x=W.data.handleId;return"number"==typeof x?_[x]=W:x&&(x[Re]=W),x&&x.ref&&x.unref&&"function"==typeof x.ref&&"function"==typeof x.unref&&(W.ref=x.ref.bind(x),W.unref=x.unref.bind(x)),"number"==typeof x||x?x:W}return C.apply(t,Z)}),u=le(t,c,C=>function(T,Z){const P=Z[0];let j;"number"==typeof P?j=_[P]:(j=P&&P[Re],j||(j=P)),j&&"string"==typeof j.type?"notScheduled"!==j.state&&(j.cancelFn&&j.data.isPeriodic||0===j.runCount)&&("number"==typeof P?delete _[P]:P&&(P[Re]=null),j.zone.cancelTask(j)):C.apply(t,Z)})}function ot(t,r,c){if(!c||0===c.length)return r;const n=c.filter(u=>u.target===t);if(!n||0===n.length)return r;const i=n[0].ignoreProperties;return r.filter(u=>-1===i.indexOf(u))}function st(t,r,c,n){t&&Je(t,ot(t,r,c),n)}function Fe(t){return Object.getOwnPropertyNames(t).filter(r=>r.startsWith("on")&&r.length>2).map(r=>r.substring(2))}function Ot(t,r,c,n,i){const u=Zone.__symbol__(n);if(r[u])return;const _=r[u]=r[n];r[n]=function(E,p,C){return p&&p.prototype&&i.forEach(function(T){const Z=`${c}.${n}::`+T,P=p.prototype;try{if(P.hasOwnProperty(T)){const j=t.ObjectGetOwnPropertyDescriptor(P,T);j&&j.value?(j.value=t.wrapWithCurrentZone(j.value,Z),t._redefineProperty(p.prototype,T,j)):P[T]&&(P[T]=t.wrapWithCurrentZone(P[T],Z))}else P[T]&&(P[T]=t.wrapWithCurrentZone(P[T],Z))}catch{}}),_.call(r,E,p,C)},t.attachOriginToPatched(r[n],_)}const it=function Oe(){const t=globalThis,r=!0===t[Q("forceDuplicateZoneCheck")];if(t.Zone&&(r||"function"!=typeof t.Zone.__symbol__))throw new Error("Zone already loaded.");return t.Zone??=function We(){const t=ee.performance;function r(N){t&&t.mark&&t.mark(N)}function c(N,d){t&&t.measure&&t.measure(N,d)}r("Zone");let n=(()=>{class N{static#e=this.__symbol__=Q;static assertZonePatched(){if(ee.Promise!==O.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=N.current;for(;e.parent;)e=e.parent;return e}static get current(){return v.zone}static get currentTask(){return S}static __load_patch(e,o,m=!1){if(O.hasOwnProperty(e)){const D=!0===ee[Q("forceDuplicateZoneCheck")];if(!m&&D)throw Error("Already loaded patch: "+e)}else if(!ee["__Zone_disable_"+e]){const D="Zone:"+e;r(D),O[e]=o(ee,N,w),c(D,D)}}get parent(){return this._parent}get name(){return this._name}constructor(e,o){this._parent=e,this._name=o?o.name||"unnamed":"<root>",this._properties=o&&o.properties||{},this._zoneDelegate=new u(this,this._parent&&this._parent._zoneDelegate,o)}get(e){const o=this.getZoneWith(e);if(o)return o._properties[e]}getZoneWith(e){let o=this;for(;o;){if(o._properties.hasOwnProperty(e))return o;o=o._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,o){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const m=this._zoneDelegate.intercept(this,e,o),D=this;return function(){return D.runGuarded(m,this,arguments,o)}}run(e,o,m,D){v={parent:v,zone:this};try{return this._zoneDelegate.invoke(this,e,o,m,D)}finally{v=v.parent}}runGuarded(e,o=null,m,D){v={parent:v,zone:this};try{try{return this._zoneDelegate.invoke(this,e,o,m,D)}catch($){if(this._zoneDelegate.handleError(this,$))throw $}}finally{v=v.parent}}runTask(e,o,m){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||re).name+"; Execution: "+this.name+")");if(e.state===X&&(e.type===U||e.type===g))return;const D=e.state!=F;D&&e._transitionTo(F,h),e.runCount++;const $=S;S=e,v={parent:v,zone:this};try{e.type==g&&e.data&&!e.data.isPeriodic&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,e,o,m)}catch(A){if(this._zoneDelegate.handleError(this,A))throw A}}finally{e.state!==X&&e.state!==Y&&(e.type==U||e.data&&e.data.isPeriodic?D&&e._transitionTo(h,F):(e.runCount=0,this._updateTaskCount(e,-1),D&&e._transitionTo(X,F,X))),v=v.parent,S=$}}scheduleTask(e){if(e.zone&&e.zone!==this){let m=this;for(;m;){if(m===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);m=m.parent}}e._transitionTo(b,X);const o=[];e._zoneDelegates=o,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(m){throw e._transitionTo(Y,b,X),this._zoneDelegate.handleError(this,m),m}return e._zoneDelegates===o&&this._updateTaskCount(e,1),e.state==b&&e._transitionTo(h,b),e}scheduleMicroTask(e,o,m,D){return this.scheduleTask(new _(B,e,o,m,D,void 0))}scheduleMacroTask(e,o,m,D,$){return this.scheduleTask(new _(g,e,o,m,D,$))}scheduleEventTask(e,o,m,D,$){return this.scheduleTask(new _(U,e,o,m,D,$))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||re).name+"; Execution: "+this.name+")");if(e.state===h||e.state===F){e._transitionTo(z,h,F);try{this._zoneDelegate.cancelTask(this,e)}catch(o){throw e._transitionTo(Y,z),this._zoneDelegate.handleError(this,o),o}return this._updateTaskCount(e,-1),e._transitionTo(X,z),e.runCount=0,e}}_updateTaskCount(e,o){const m=e._zoneDelegates;-1==o&&(e._zoneDelegates=null);for(let D=0;D<m.length;D++)m[D]._updateTaskCount(e.type,o)}}return N})();const i={name:"",onHasTask:(N,d,e,o)=>N.hasTask(e,o),onScheduleTask:(N,d,e,o)=>N.scheduleTask(e,o),onInvokeTask:(N,d,e,o,m,D)=>N.invokeTask(e,o,m,D),onCancelTask:(N,d,e,o)=>N.cancelTask(e,o)};class u{get zone(){return this._zone}constructor(d,e,o){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=d,this._parentDelegate=e,this._forkZS=o&&(o&&o.onFork?o:e._forkZS),this._forkDlgt=o&&(o.onFork?e:e._forkDlgt),this._forkCurrZone=o&&(o.onFork?this._zone:e._forkCurrZone),this._interceptZS=o&&(o.onIntercept?o:e._interceptZS),this._interceptDlgt=o&&(o.onIntercept?e:e._interceptDlgt),this._interceptCurrZone=o&&(o.onIntercept?this._zone:e._interceptCurrZone),this._invokeZS=o&&(o.onInvoke?o:e._invokeZS),this._invokeDlgt=o&&(o.onInvoke?e:e._invokeDlgt),this._invokeCurrZone=o&&(o.onInvoke?this._zone:e._invokeCurrZone),this._handleErrorZS=o&&(o.onHandleError?o:e._handleErrorZS),this._handleErrorDlgt=o&&(o.onHandleError?e:e._handleErrorDlgt),this._handleErrorCurrZone=o&&(o.onHandleError?this._zone:e._handleErrorCurrZone),this._scheduleTaskZS=o&&(o.onScheduleTask?o:e._scheduleTaskZS),this._scheduleTaskDlgt=o&&(o.onScheduleTask?e:e._scheduleTaskDlgt),this._scheduleTaskCurrZone=o&&(o.onScheduleTask?this._zone:e._scheduleTaskCurrZone),this._invokeTaskZS=o&&(o.onInvokeTask?o:e._invokeTaskZS),this._invokeTaskDlgt=o&&(o.onInvokeTask?e:e._invokeTaskDlgt),this._invokeTaskCurrZone=o&&(o.onInvokeTask?this._zone:e._invokeTaskCurrZone),this._cancelTaskZS=o&&(o.onCancelTask?o:e._cancelTaskZS),this._cancelTaskDlgt=o&&(o.onCancelTask?e:e._cancelTaskDlgt),this._cancelTaskCurrZone=o&&(o.onCancelTask?this._zone:e._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const m=o&&o.onHasTask;(m||e&&e._hasTaskZS)&&(this._hasTaskZS=m?o:i,this._hasTaskDlgt=e,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,o.onScheduleTask||(this._scheduleTaskZS=i,this._scheduleTaskDlgt=e,this._scheduleTaskCurrZone=this._zone),o.onInvokeTask||(this._invokeTaskZS=i,this._invokeTaskDlgt=e,this._invokeTaskCurrZone=this._zone),o.onCancelTask||(this._cancelTaskZS=i,this._cancelTaskDlgt=e,this._cancelTaskCurrZone=this._zone))}fork(d,e){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,d,e):new n(d,e)}intercept(d,e,o){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,d,e,o):e}invoke(d,e,o,m,D){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,d,e,o,m,D):e.apply(o,m)}handleError(d,e){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,d,e)}scheduleTask(d,e){let o=e;if(this._scheduleTaskZS)this._hasTaskZS&&o._zoneDelegates.push(this._hasTaskDlgtOwner),o=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,d,e),o||(o=e);else if(e.scheduleFn)e.scheduleFn(e);else{if(e.type!=B)throw new Error("Task is missing scheduleFn.");W(e)}return o}invokeTask(d,e,o,m){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,d,e,o,m):e.callback.apply(o,m)}cancelTask(d,e){let o;if(this._cancelTaskZS)o=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,d,e);else{if(!e.cancelFn)throw Error("Task is not cancelable");o=e.cancelFn(e)}return o}hasTask(d,e){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,d,e)}catch(o){this.handleError(d,o)}}_updateTaskCount(d,e){const o=this._taskCounts,m=o[d],D=o[d]=m+e;if(D<0)throw new Error("More tasks executed then were scheduled.");0!=m&&0!=D||this.hasTask(this._zone,{microTask:o.microTask>0,macroTask:o.macroTask>0,eventTask:o.eventTask>0,change:d})}}class _{constructor(d,e,o,m,D,$){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=d,this.source=e,this.data=m,this.scheduleFn=D,this.cancelFn=$,!o)throw new Error("callback is not defined");this.callback=o;const A=this;this.invoke=d===U&&m&&m.useG?_.invokeTask:function(){return _.invokeTask.call(ee,A,this,arguments)}}static invokeTask(d,e,o){d||(d=this),K++;try{return d.runCount++,d.zone.runTask(d,e,o)}finally{1==K&&x(),K--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(X,b)}_transitionTo(d,e,o){if(this._state!==e&&this._state!==o)throw new Error(`${this.type} '${this.source}': can not transition to '${d}', expecting state '${e}'${o?" or '"+o+"'":""}, was '${this._state}'.`);this._state=d,d==X&&(this._zoneDelegates=null)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const E=Q("setTimeout"),p=Q("Promise"),C=Q("then");let P,T=[],Z=!1;function j(N){if(P||ee[p]&&(P=ee[p].resolve(0)),P){let d=P[C];d||(d=P.then),d.call(P,N)}else ee[E](N,0)}function W(N){0===K&&0===T.length&&j(x),N&&T.push(N)}function x(){if(!Z){for(Z=!0;T.length;){const N=T;T=[];for(let d=0;d<N.length;d++){const e=N[d];try{e.zone.runTask(e,null,null)}catch(o){w.onUnhandledError(o)}}}w.microtaskDrainDone(),Z=!1}}const re={name:"NO ZONE"},X="notScheduled",b="scheduling",h="scheduled",F="running",z="canceling",Y="unknown",B="microTask",g="macroTask",U="eventTask",O={},w={symbol:Q,currentZoneFrame:()=>v,onUnhandledError:q,microtaskDrainDone:q,scheduleMicroTask:W,showUncaughtError:()=>!n[Q("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:q,patchMethod:()=>q,bindArguments:()=>[],patchThen:()=>q,patchMacroTask:()=>q,patchEventPrototype:()=>q,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>q,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>q,wrapWithCurrentZone:()=>q,filterProperties:()=>[],attachOriginToPatched:()=>q,_redefineProperty:()=>q,patchCallbacks:()=>q,nativeScheduleMicroTask:j};let v={parent:null,zone:new n(null,null)},S=null,K=0;function q(){}return c("Zone","Zone"),n}(),t.Zone}();(function It(t){(function St(t){t.__load_patch("ZoneAwarePromise",(r,c,n)=>{const i=Object.getOwnPropertyDescriptor,u=Object.defineProperty,E=n.symbol,p=[],C=!1!==r[E("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],T=E("Promise"),Z=E("then"),P="__creationTrace__";n.onUnhandledError=f=>{if(n.showUncaughtError()){const a=f&&f.rejection;a?console.error("Unhandled Promise rejection:",a instanceof Error?a.message:a,"; Zone:",f.zone.name,"; Task:",f.task&&f.task.source,"; Value:",a,a instanceof Error?a.stack:void 0):console.error(f)}},n.microtaskDrainDone=()=>{for(;p.length;){const f=p.shift();try{f.zone.runGuarded(()=>{throw f.throwOriginal?f.rejection:f})}catch(a){W(a)}}};const j=E("unhandledPromiseRejectionHandler");function W(f){n.onUnhandledError(f);try{const a=c[j];"function"==typeof a&&a.call(this,f)}catch{}}function x(f){return f&&f.then}function re(f){return f}function X(f){return A.reject(f)}const b=E("state"),h=E("value"),F=E("finally"),z=E("parentPromiseValue"),Y=E("parentPromiseState"),B="Promise.then",g=null,U=!0,O=!1,w=0;function v(f,a){return s=>{try{N(f,a,s)}catch(l){N(f,!1,l)}}}const S=function(){let f=!1;return function(s){return function(){f||(f=!0,s.apply(null,arguments))}}},K="Promise resolved with itself",q=E("currentTaskTrace");function N(f,a,s){const l=S();if(f===s)throw new TypeError(K);if(f[b]===g){let k=null;try{("object"==typeof s||"function"==typeof s)&&(k=s&&s.then)}catch(R){return l(()=>{N(f,!1,R)})(),f}if(a!==O&&s instanceof A&&s.hasOwnProperty(b)&&s.hasOwnProperty(h)&&s[b]!==g)e(s),N(f,s[b],s[h]);else if(a!==O&&"function"==typeof k)try{k.call(s,l(v(f,a)),l(v(f,!1)))}catch(R){l(()=>{N(f,!1,R)})()}else{f[b]=a;const R=f[h];if(f[h]=s,f[F]===F&&a===U&&(f[b]=f[Y],f[h]=f[z]),a===O&&s instanceof Error){const y=c.currentTask&&c.currentTask.data&&c.currentTask.data[P];y&&u(s,q,{configurable:!0,enumerable:!1,writable:!0,value:y})}for(let y=0;y<R.length;)o(f,R[y++],R[y++],R[y++],R[y++]);if(0==R.length&&a==O){f[b]=w;let y=s;try{throw new Error("Uncaught (in promise): "+function _(f){return f&&f.toString===Object.prototype.toString?(f.constructor&&f.constructor.name||"")+": "+JSON.stringify(f):f?f.toString():Object.prototype.toString.call(f)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(I){y=I}C&&(y.throwOriginal=!0),y.rejection=s,y.promise=f,y.zone=c.current,y.task=c.currentTask,p.push(y),n.scheduleMicroTask()}}}return f}const d=E("rejectionHandledHandler");function e(f){if(f[b]===w){try{const a=c[d];a&&"function"==typeof a&&a.call(this,{rejection:f[h],promise:f})}catch{}f[b]=O;for(let a=0;a<p.length;a++)f===p[a].promise&&p.splice(a,1)}}function o(f,a,s,l,k){e(f);const R=f[b],y=R?"function"==typeof l?l:re:"function"==typeof k?k:X;a.scheduleMicroTask(B,()=>{try{const I=f[h],L=!!s&&F===s[F];L&&(s[z]=I,s[Y]=R);const M=a.run(y,void 0,L&&y!==X&&y!==re?[]:[I]);N(s,!0,M)}catch(I){N(s,!1,I)}},s)}const D=function(){},$=r.AggregateError;class A{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(a){return a instanceof A?a:N(new this(null),U,a)}static reject(a){return N(new this(null),O,a)}static withResolvers(){const a={};return a.promise=new A((s,l)=>{a.resolve=s,a.reject=l}),a}static any(a){if(!a||"function"!=typeof a[Symbol.iterator])return Promise.reject(new $([],"All promises were rejected"));const s=[];let l=0;try{for(let y of a)l++,s.push(A.resolve(y))}catch{return Promise.reject(new $([],"All promises were rejected"))}if(0===l)return Promise.reject(new $([],"All promises were rejected"));let k=!1;const R=[];return new A((y,I)=>{for(let L=0;L<s.length;L++)s[L].then(M=>{k||(k=!0,y(M))},M=>{R.push(M),l--,0===l&&(k=!0,I(new $(R,"All promises were rejected")))})})}static race(a){let s,l,k=new this((I,L)=>{s=I,l=L});function R(I){s(I)}function y(I){l(I)}for(let I of a)x(I)||(I=this.resolve(I)),I.then(R,y);return k}static all(a){return A.allWithCallback(a)}static allSettled(a){return(this&&this.prototype instanceof A?this:A).allWithCallback(a,{thenCallback:l=>({status:"fulfilled",value:l}),errorCallback:l=>({status:"rejected",reason:l})})}static allWithCallback(a,s){let l,k,R=new this((M,G)=>{l=M,k=G}),y=2,I=0;const L=[];for(let M of a){x(M)||(M=this.resolve(M));const G=I;try{M.then(V=>{L[G]=s?s.thenCallback(V):V,y--,0===y&&l(L)},V=>{s?(L[G]=s.errorCallback(V),y--,0===y&&l(L)):k(V)})}catch(V){k(V)}y++,I++}return y-=2,0===y&&l(L),R}constructor(a){const s=this;if(!(s instanceof A))throw new Error("Must be an instanceof Promise.");s[b]=g,s[h]=[];try{const l=S();a&&a(l(v(s,U)),l(v(s,O)))}catch(l){N(s,!1,l)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return A}then(a,s){let l=this.constructor?.[Symbol.species];(!l||"function"!=typeof l)&&(l=this.constructor||A);const k=new l(D),R=c.current;return this[b]==g?this[h].push(R,k,a,s):o(this,R,k,a,s),k}catch(a){return this.then(null,a)}finally(a){let s=this.constructor?.[Symbol.species];(!s||"function"!=typeof s)&&(s=A);const l=new s(D);l[F]=F;const k=c.current;return this[b]==g?this[h].push(k,l,a,a):o(this,k,l,a,a),l}}A.resolve=A.resolve,A.reject=A.reject,A.race=A.race,A.all=A.all;const Ce=r[T]=r.Promise;r.Promise=A;const ke=E("thenPatched");function he(f){const a=f.prototype,s=i(a,"then");if(s&&(!1===s.writable||!s.configurable))return;const l=a.then;a[Z]=l,f.prototype.then=function(k,R){return new A((I,L)=>{l.call(this,I,L)}).then(k,R)},f[ke]=!0}return n.patchThen=he,Ce&&(he(Ce),le(r,"fetch",f=>function Se(f){return function(a,s){let l=f.apply(a,s);if(l instanceof A)return l;let k=l.constructor;return k[ke]||he(k),l}}(f))),Promise[c.__symbol__("uncaughtPromiseErrors")]=p,A})})(t),function Dt(t){t.__load_patch("toString",r=>{const c=Function.prototype.toString,n=H("OriginalDelegate"),i=H("Promise"),u=H("Error"),_=function(){if("function"==typeof this){const T=this[n];if(T)return"function"==typeof T?c.call(T):Object.prototype.toString.call(T);if(this===Promise){const Z=r[i];if(Z)return c.call(Z)}if(this===Error){const Z=r[u];if(Z)return c.call(Z)}}return c.call(this)};_[n]=c,Function.prototype.toString=_;const E=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":E.call(this)}})}(t),function Nt(t){t.__load_patch("util",(r,c,n)=>{const i=Fe(r);n.patchOnProperties=Je,n.patchMethod=le,n.bindArguments=xe,n.patchMacroTask=Tt;const u=c.__symbol__("BLACK_LISTED_EVENTS"),_=c.__symbol__("UNPATCHED_EVENTS");r[_]&&(r[u]=r[_]),r[u]&&(c[u]=c[_]=r[u]),n.patchEventPrototype=kt,n.patchEventTarget=mt,n.isIEOrEdge=yt,n.ObjectDefineProperty=Ne,n.ObjectGetOwnPropertyDescriptor=ye,n.ObjectCreate=ht,n.ArraySlice=dt,n.patchClass=me,n.wrapWithCurrentZone=je,n.filterProperties=ot,n.attachOriginToPatched=ue,n._redefineProperty=Object.defineProperty,n.patchCallbacks=Ot,n.getGlobalObjects=()=>({globalSources:Qe,zoneSymbolEventNames:te,eventNames:i,isBrowser:Ge,isMix:Xe,isNode:Pe,TRUE_STR:ce,FALSE_STR:ae,ZONE_SYMBOL_PREFIX:pe,ADD_EVENT_LISTENER_STR:Le,REMOVE_EVENT_LISTENER_STR:Me})})}(t)})(it),function Ct(t){t.__load_patch("legacy",r=>{const c=r[t.__symbol__("legacyPatch")];c&&c()}),t.__load_patch("timers",r=>{const c="set",n="clear";Ee(r,c,n,"Timeout"),Ee(r,c,n,"Interval"),Ee(r,c,n,"Immediate")}),t.__load_patch("requestAnimationFrame",r=>{Ee(r,"request","cancel","AnimationFrame"),Ee(r,"mozRequest","mozCancel","AnimationFrame"),Ee(r,"webkitRequest","webkitCancel","AnimationFrame")}),t.__load_patch("blocking",(r,c)=>{const n=["alert","prompt","confirm"];for(let i=0;i<n.length;i++)le(r,n[i],(_,E,p)=>function(C,T){return c.current.run(_,r,T,p)})}),t.__load_patch("EventTarget",(r,c,n)=>{(function wt(t,r){r.patchEventPrototype(t,r)})(r,n),function Pt(t,r){if(Zone[r.symbol("patchEventTarget")])return;const{eventNames:c,zoneSymbolEventNames:n,TRUE_STR:i,FALSE_STR:u,ZONE_SYMBOL_PREFIX:_}=r.getGlobalObjects();for(let p=0;p<c.length;p++){const C=c[p],P=_+(C+u),j=_+(C+i);n[C]={},n[C][u]=P,n[C][i]=j}const E=t.EventTarget;E&&E.prototype&&r.patchEventTarget(t,r,[E&&E.prototype])}(r,n);const i=r.XMLHttpRequestEventTarget;i&&i.prototype&&n.patchEventTarget(r,n,[i.prototype])}),t.__load_patch("MutationObserver",(r,c,n)=>{me("MutationObserver"),me("WebKitMutationObserver")}),t.__load_patch("IntersectionObserver",(r,c,n)=>{me("IntersectionObserver")}),t.__load_patch("FileReader",(r,c,n)=>{me("FileReader")}),t.__load_patch("on_property",(r,c,n)=>{!function Rt(t,r){if(Pe&&!Xe||Zone[t.symbol("patchEvents")])return;const c=r.__Zone_ignore_on_properties;let n=[];if(Ge){const i=window;n=n.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);const u=function gt(){try{const t=de.navigator.userAgent;if(-1!==t.indexOf("MSIE ")||-1!==t.indexOf("Trident/"))return!0}catch{}return!1}()?[{target:i,ignoreProperties:["error"]}]:[];st(i,Fe(i),c&&c.concat(u),Ie(i))}n=n.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let i=0;i<n.length;i++){const u=r[n[i]];u&&u.prototype&&st(u.prototype,Fe(u.prototype),c)}}(n,r)}),t.__load_patch("customElements",(r,c,n)=>{!function bt(t,r){const{isBrowser:c,isMix:n}=r.getGlobalObjects();(c||n)&&t.customElements&&"customElements"in t&&r.patchCallbacks(r,t.customElements,"customElements","define",["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"])}(r,n)}),t.__load_patch("XHR",(r,c)=>{!function C(T){const Z=T.XMLHttpRequest;if(!Z)return;const P=Z.prototype;let W=P[Ze],x=P[Ae];if(!W){const w=T.XMLHttpRequestEventTarget;if(w){const v=w.prototype;W=v[Ze],x=v[Ae]}}const re="readystatechange",X="scheduled";function b(w){const v=w.data,S=v.target;S[_]=!1,S[p]=!1;const K=S[u];W||(W=S[Ze],x=S[Ae]),K&&x.call(S,re,K);const q=S[u]=()=>{if(S.readyState===S.DONE)if(!v.aborted&&S[_]&&w.state===X){const d=S[c.__symbol__("loadfalse")];if(0!==S.status&&d&&d.length>0){const e=w.invoke;w.invoke=function(){const o=S[c.__symbol__("loadfalse")];for(let m=0;m<o.length;m++)o[m]===w&&o.splice(m,1);!v.aborted&&w.state===X&&e.call(w)},d.push(w)}else w.invoke()}else!v.aborted&&!1===S[_]&&(S[p]=!0)};return W.call(S,re,q),S[n]||(S[n]=w),U.apply(S,v.args),S[_]=!0,w}function h(){}function F(w){const v=w.data;return v.aborted=!0,O.apply(v.target,v.args)}const z=le(P,"open",()=>function(w,v){return w[i]=0==v[2],w[E]=v[1],z.apply(w,v)}),B=H("fetchTaskAborting"),g=H("fetchTaskScheduling"),U=le(P,"send",()=>function(w,v){if(!0===c.current[g]||w[i])return U.apply(w,v);{const S={target:w,url:w[E],isPeriodic:!1,args:v,aborted:!1},K=He("XMLHttpRequest.send",h,S,b,F);w&&!0===w[p]&&!S.aborted&&K.state===X&&K.invoke()}}),O=le(P,"abort",()=>function(w,v){const S=function j(w){return w[n]}(w);if(S&&"string"==typeof S.type){if(null==S.cancelFn||S.data&&S.data.aborted)return;S.zone.cancelTask(S)}else if(!0===c.current[B])return O.apply(w,v)})}(r);const n=H("xhrTask"),i=H("xhrSync"),u=H("xhrListener"),_=H("xhrScheduled"),E=H("xhrURL"),p=H("xhrErrorBeforeScheduled")}),t.__load_patch("geolocation",r=>{r.navigator&&r.navigator.geolocation&&function Et(t,r){const c=t.constructor.name;for(let n=0;n<r.length;n++){const i=r[n],u=t[i];if(u){if(!ze(ye(t,i)))continue;t[i]=(E=>{const p=function(){return E.apply(this,xe(arguments,c+"."+i))};return ue(p,E),p})(u)}}}(r.navigator.geolocation,["getCurrentPosition","watchPosition"])}),t.__load_patch("PromiseRejectionEvent",(r,c)=>{function n(i){return function(u){rt(r,i).forEach(E=>{const p=r.PromiseRejectionEvent;if(p){const C=new p(i,{promise:u.promise,reason:u.rejection});E.invoke(C)}})}}r.PromiseRejectionEvent&&(c[H("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),c[H("rejectionHandledHandler")]=n("rejectionhandled"))}),t.__load_patch("queueMicrotask",(r,c,n)=>{!function vt(t,r){r.patchMethod(t,"queueMicrotask",c=>function(n,i){Zone.current.scheduleMicroTask("queueMicrotask",i[0])})}(r,n)})}(it)}},ee=>{ee(ee.s=935)}]);