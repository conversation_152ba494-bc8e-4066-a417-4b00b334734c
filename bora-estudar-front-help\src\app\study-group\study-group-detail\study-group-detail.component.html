<!-- <mat-toolbar color="primary" position="start" class="header_toolbar">
  <button mat-icon-button (click)="close()">
    <mat-icon>arrow_back</mat-icon>
  </button>

  <span class="spacer"></span>

  <span>Detalhes</span>

  <span class="spacer"></span>
</mat-toolbar> -->

<div class="allGrid m-top">
  <mat-card-title class="titulo">{{ studyGroup?.code + ' - ' + studyGroup?.title }}</mat-card-title>
  <mat-card-subtitle class="subTitulo">{{ studyGroup?.shortDescription }}</mat-card-subtitle>

  <div class="allBetween m-top">
    <p><strong>Alunos:</strong> {{ studyGroup?.participants }}</p>
    <p><strong><PERSON>ra de Início:</strong> {{ studyGroup?.hour }}</p>
  </div>

  <div>
    <p><strong>Dias da Semana</strong></p>
    <mat-chip-set>
      <mat-chip *ngFor="let day of diasSemana" [ngClass]="{'selected': studyGroup?.daysOfWeek.includes(day)}">
        <p [ngClass]="{'selectedText': studyGroup?.daysOfWeek.includes(day)}" style="margin-top: 12px;">{{ day | titlecase }}</p>
      </mat-chip>
    </mat-chip-set>
  </div>

  @if(loading === true){
    <p-progressSpinner class="spinerCenter" ariaLabel="loading" />
  } @else {
    @if(this.userInGroup === true){
      <button mat-raised-button
              color="primary"
              class="full_width mtop"
              (click)="openDiscord()"
              [disabled]="!discordInviteUrl">
        <mat-icon fontIcon="discord"></mat-icon>
        Entrar no Discord
      </button>

      @if(this.isOwnerId === true){
        <button mat-raised-button color="primary" class="full_width mtop" (click)="editGroup()">Editar</button>
      }
      <button mat-raised-button class="full_width mtop" (click)="leaveGroup()">Sair</button>
    } @else {
      <button mat-raised-button color="primary" class="full_width mtop" (click)="joinGroup()">Entrar</button>
    }
  }
</div>

<p-toast />

<!-- <div class="container">
  @if( studyGroup$ | async ; as studyGroup) { @if(!!studyGroup){
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ studyGroup.title }}</mat-card-title>
      <mat-card-subtitle>{{ studyGroup.shortDescription }}</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p><strong>Monitor:</strong> {{ studyGroup.monitor }}</p>
      <p><strong>Alunos:</strong> {{ studyGroup.participants }}</p>
      <p><strong>Modalidade:</strong> {{ studyGroup.modality | titlecase }}</p>
      <p><strong>Horario:</strong> {{ studyGroup.hour }}</p>
      <mat-chip-set>
        <mat-chip *ngFor="let day of studyGroup.daysOfWeek">{{
          day | titlecase
        }}</mat-chip>
      </mat-chip-set>
    </mat-card-content>
    <mat-card-actions>
      <button
        mat-raised-button
        color="secondary"
        class="full_width"
        [routerLink]="['/study-group', studyGroup.id]"
      >
        Detalhes
      </button>
      <button
        mat-raised-button
        color="primary"
        class="full_width"
        [routerLink]="['/study-group', studyGroup.id]"
      >
        Detalhes
      </button>
    </mat-card-actions>
  </mat-card>
  } @else {
  <p>Error loading data</p>
  } } @else {
  <p>Loading...</p>
  }
</div> -->
