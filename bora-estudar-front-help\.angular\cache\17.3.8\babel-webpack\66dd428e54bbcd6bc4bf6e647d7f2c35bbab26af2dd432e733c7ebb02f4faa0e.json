{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { isSubscription, Subscription } from './Subscription';\nimport { config } from './config';\nimport { reportUnhandledError } from './util/reportUnhandledError';\nimport { noop } from './util/noop';\nimport { nextNotification, errorNotification, COMPLETE_NOTIFICATION } from './NotificationFactories';\nimport { timeoutProvider } from './scheduler/timeoutProvider';\nimport { captureError } from './util/errorContext';\nexport class Subscriber extends Subscription {\n  constructor(destination) {\n    super();\n    this.isStopped = false;\n    if (destination) {\n      this.destination = destination;\n      if (isSubscription(destination)) {\n        destination.add(this);\n      }\n    } else {\n      this.destination = EMPTY_OBSERVER;\n    }\n  }\n  static create(next, error, complete) {\n    return new SafeSubscriber(next, error, complete);\n  }\n  next(value) {\n    if (this.isStopped) {\n      handleStoppedNotification(nextNotification(value), this);\n    } else {\n      this._next(value);\n    }\n  }\n  error(err) {\n    if (this.isStopped) {\n      handleStoppedNotification(errorNotification(err), this);\n    } else {\n      this.isStopped = true;\n      this._error(err);\n    }\n  }\n  complete() {\n    if (this.isStopped) {\n      handleStoppedNotification(COMPLETE_NOTIFICATION, this);\n    } else {\n      this.isStopped = true;\n      this._complete();\n    }\n  }\n  unsubscribe() {\n    if (!this.closed) {\n      this.isStopped = true;\n      super.unsubscribe();\n      this.destination = null;\n    }\n  }\n  _next(value) {\n    this.destination.next(value);\n  }\n  _error(err) {\n    try {\n      this.destination.error(err);\n    } finally {\n      this.unsubscribe();\n    }\n  }\n  _complete() {\n    try {\n      this.destination.complete();\n    } finally {\n      this.unsubscribe();\n    }\n  }\n}\nconst _bind = Function.prototype.bind;\nfunction bind(fn, thisArg) {\n  return _bind.call(fn, thisArg);\n}\nclass ConsumerObserver {\n  constructor(partialObserver) {\n    this.partialObserver = partialObserver;\n  }\n  next(value) {\n    const {\n      partialObserver\n    } = this;\n    if (partialObserver.next) {\n      try {\n        partialObserver.next(value);\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    }\n  }\n  error(err) {\n    const {\n      partialObserver\n    } = this;\n    if (partialObserver.error) {\n      try {\n        partialObserver.error(err);\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    } else {\n      handleUnhandledError(err);\n    }\n  }\n  complete() {\n    const {\n      partialObserver\n    } = this;\n    if (partialObserver.complete) {\n      try {\n        partialObserver.complete();\n      } catch (error) {\n        handleUnhandledError(error);\n      }\n    }\n  }\n}\nexport class SafeSubscriber extends Subscriber {\n  constructor(observerOrNext, error, complete) {\n    super();\n    let partialObserver;\n    if (isFunction(observerOrNext) || !observerOrNext) {\n      partialObserver = {\n        next: observerOrNext !== null && observerOrNext !== void 0 ? observerOrNext : undefined,\n        error: error !== null && error !== void 0 ? error : undefined,\n        complete: complete !== null && complete !== void 0 ? complete : undefined\n      };\n    } else {\n      let context;\n      if (this && config.useDeprecatedNextContext) {\n        context = Object.create(observerOrNext);\n        context.unsubscribe = () => this.unsubscribe();\n        partialObserver = {\n          next: observerOrNext.next && bind(observerOrNext.next, context),\n          error: observerOrNext.error && bind(observerOrNext.error, context),\n          complete: observerOrNext.complete && bind(observerOrNext.complete, context)\n        };\n      } else {\n        partialObserver = observerOrNext;\n      }\n    }\n    this.destination = new ConsumerObserver(partialObserver);\n  }\n}\nfunction handleUnhandledError(error) {\n  if (config.useDeprecatedSynchronousErrorHandling) {\n    captureError(error);\n  } else {\n    reportUnhandledError(error);\n  }\n}\nfunction defaultErrorHandler(err) {\n  throw err;\n}\nfunction handleStoppedNotification(notification, subscriber) {\n  const {\n    onStoppedNotification\n  } = config;\n  onStoppedNotification && timeoutProvider.setTimeout(() => onStoppedNotification(notification, subscriber));\n}\nexport const EMPTY_OBSERVER = {\n  closed: true,\n  next: noop,\n  error: defaultErrorHandler,\n  complete: noop\n};\n//# sourceMappingURL=Subscriber.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}