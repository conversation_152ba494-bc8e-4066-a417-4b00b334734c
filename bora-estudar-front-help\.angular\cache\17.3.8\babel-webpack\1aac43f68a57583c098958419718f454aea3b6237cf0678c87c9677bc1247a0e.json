{"ast": null, "code": "import { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { inject } from '@angular/core';\nimport { catchError, tap } from 'rxjs';\nimport { StorageService } from './storage.service';\nimport * as i0 from \"@angular/core\";\nconst AUTH_API = '';\nconst httpOptions = {\n  headers: new HttpHeaders({\n    'Content-Type': 'application/json'\n  })\n};\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor() {\n      this.http = inject(HttpClient);\n      this.storageService = inject(StorageService);\n    }\n    login(body) {\n      return this.http.post(AUTH_API.concat('/signin'), body, httpOptions).pipe(tap(user => {\n        this.storageService.saveUser(user);\n      }), catchError(error => {\n        console.log(`Error on login: ${error.message}`);\n        this.storageService.clear();\n        throw error;\n      }));\n    }\n    register(body) {\n      return this.http.post(AUTH_API.concat('/signup'), body, httpOptions).pipe(catchError(error => {\n        console.log(`Error on login: ${error.message}`);\n        this.storageService.clear();\n        throw error;\n      }));\n    }\n    confirmEmail(token) {\n      return this.http.get(AUTH_API.concat(`/confirm?token=${token}`), httpOptions);\n    }\n    logout() {\n      return this.http.post(AUTH_API + 'signout', {}, httpOptions).pipe(tap(() => {\n        this.storageService.clear();\n      }), catchError(error => {\n        console.log(`Error on login: ${error.message}`);\n        this.storageService.clear();\n        throw error;\n      }));\n    }\n    getUser() {\n      return this.storageService.getUser();\n    }\n    isLoggedIn() {\n      return this.storageService.isLoggedIn();\n    }\n    static #_ = this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}